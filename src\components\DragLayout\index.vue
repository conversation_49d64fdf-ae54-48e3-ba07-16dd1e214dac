<template>
  <div class="drag-box" :class="type" ref="dragBox">
    <div class="drag-item" :style="firstStyle">
      <slot name="first"></slot>
    </div>
    <div
      class="resizeBar"
      :class="[type, { active: dragging }]"
      ref="resizeBar"
      @mousedown.prevent.stop="onMouseDown"
    ></div>
    <slot name="float" v-if="type === 'column'"></slot>
    <div class="drag-item" :style="secondStyle">
      <slot name="second"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'

const props = defineProps({
  type: { type: String, default: 'row' },
  modelValue: { type: Object, default: () => ({ first: '21%', second: '79%' }) },
  min: { type: Number, default: 80 },
  max: { type: Number, default: 9999 }
})

const emit = defineEmits(['update:modelValue', 'resize'])

const dragging = ref(false)
const dragBox = ref(null)
const resizeBar = ref(null)
const firstPx = ref(0)
const secondPx = ref(0)
let start = 0
let startFirst = 0
let startSecond = 0
onMounted(() => {
    // 初始化
    initPx();
});
// 初始化像素宽度/高度
function initPx() {
  nextTick(() => {
    if (!dragBox.value) return
    if (props.type === 'row') {
      const total = dragBox.value.offsetWidth

      firstPx.value = parseFloat(props.modelValue.first) / 100 * total
      secondPx.value = parseFloat(props.modelValue.second) / 100 * total
    } else {
      const total = dragBox.value.offsetHeight
      firstPx.value = parseFloat(props.modelValue.first) / 100 * total
      secondPx.value = parseFloat(props.modelValue.second) / 100 * total
    }
  })
}

onMounted(() => {
  // 只初始化一次
  if (!dragBox.value) return
  if (props.type === 'row') {
    const total = dragBox.value.offsetWidth || 1
    let fristPercent = parseFloat(props.modelValue.first)
    let secondPercent = parseFloat(props.modelValue.second)
    if (fristPercent > 1) fristPercent = fristPercent / 100
    if (secondPercent > 1) secondPercent = secondPercent / 100
    firstPx.value = total * (fristPercent > 0 ? fristPercent : 0.5)
    secondPx.value = total * (secondPercent > 0 ? secondPercent : 0.5)
  } else {
    const total = dragBox.value.offsetHeight || 1
    let fristPercent = parseFloat(props.modelValue.first)
    let secondPercent = parseFloat(props.modelValue.second)
    if (fristPercent > 1) fristPercent = fristPercent / 100
    if (secondPercent > 1) secondPercent = secondPercent / 100
    firstPx.value = total * (fristPercent > 0 ? fristPercent : 0.5)
    secondPx.value = total * (secondPercent > 0 ? secondPercent : 0.5)
  }
})
onBeforeUnmount(() => {
  onMouseUp()
})

// 不再 watch v-model

const firstStyle = computed(() => {
  return props.type === 'row'
    ? { flex: `0 0 ${firstPx.value}px` }
    : { height: firstPx.value + 'px' }
})
const secondStyle = computed(() => {
  return props.type === 'row'
    ? { flex: `0 0 ${secondPx.value - 1}px` }
    : { height: secondPx.value + 'px' }
})

function onMouseDown(e) {
  dragging.value = true
  if (props.type === 'row') {
    start = e.clientX
    startFirst = firstPx.value
    startSecond = secondPx.value
    document.addEventListener('mousemove', onMouseMoveRow)
    document.addEventListener('mouseup', onMouseUp)
  } else {
    start = e.clientY
    startFirst = firstPx.value
    startSecond = secondPx.value
    document.addEventListener('mousemove', onMouseMoveCol)
    document.addEventListener('mouseup', onMouseUp)
  }
}

function onMouseMoveRow(e) {
  if (!dragging.value) return
  const delta = e.clientX - start
  let newFirst = Math.max(props.min, Math.min(props.max, startFirst + delta))
  let newSecond = Math.max(props.min, Math.min(props.max, startSecond - delta))
  firstPx.value = newFirst
  secondPx.value = newSecond
}

function onMouseMoveCol(e) {
  if (!dragging.value) return
  const delta = e.clientY - start
  let newFirst = Math.max(props.min, Math.min(props.max, startFirst + delta))
  let newSecond = Math.max(props.min, Math.min(props.max, startSecond - delta))
  firstPx.value = newFirst
  secondPx.value = newSecond
}

function onMouseUp() {
  if (!dragging.value) return
  dragging.value = false
  document.removeEventListener('mousemove', onMouseMoveRow)
  document.removeEventListener('mousemove', onMouseMoveCol)
  document.removeEventListener('mouseup', onMouseUp)
  // 拖拽结束后，emit 百分比
  const total = firstPx.value + secondPx.value
  const first = ((firstPx.value / total) * 100).toFixed(2) + '%'
  const second = ((secondPx.value / total) * 100).toFixed(2) + '%'
  emit('update:modelValue', { first, second })
  emit('resize', { first, second })
}
</script>

<style scoped>
.drag-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
.drag-box.row {
  display: flex;
  flex-direction: row;
}
.drag-box.column {
  display: flex;
  flex-direction: column;
}
.drag-item {
  overflow: auto;
}
.resizeBar {
  background: #e0e0e0;
  transition: background 0.2s;
  z-index: 2;
}
.resizeBar.row {
  flex: none;
  width: 1px;
  min-width: 1px;
  cursor: col-resize;
  background: #e0e0e0;
  z-index: 10;
}
.resizeBar.column {
  flex: none;
  height: 1px;
  min-height: 1px;
  cursor: row-resize;
  background: #e0e0e0;
  z-index: 10;
}
.resizeBar.active {
  background: #409eff;
}
</style>
