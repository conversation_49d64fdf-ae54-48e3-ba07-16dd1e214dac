<template>
	<div>
		<div style="padding: 10px; display: flex;justify-content: space-between;">
			<div style="padding-left: 30px; display: flex;" v-if="counterTypeVal == '2'">
				<div style="text-align: center; line-height: 35px; padding-right: 15px;flex-shrink: 0;">柜面选择:</div>
				<el-select v-model="counterType" style="flex-shrink: 0;">
					<el-option label="A面" value="1"/>
					<el-option label="B面" value="2"/>
				</el-select>
				<el-button style="margin-left: 20px;" type="primary" @click="() => determine()">确定</el-button>
			</div>
			<div v-if="counterTypeVal == '1'"></div>
			<div>
				<el-button plain type="warning" icon="Close" @click="() => cancellationSelect()">取消</el-button>
				<el-button plain type="primary" icon="Check" @click="() => storageSelect()">选择</el-button>
				<el-button plain type="primary" icon="Setting" @click="() => storageAdd()">设置</el-button>
			</div>
		</div>
		<el-divider style="margin: 5px 0 30px 0;box-shadow: 0 2px 6px gray;"/>
		<div style="margin-left: 3%;width: fit-content">
			<div style="display: flex;margin-bottom: 15px;margin-left: 80px;">
				<div v-if="containerInfo.containerPitchStart === '1'"
					 class="joint" v-for="(item, index) in containerList[0]"
					 :key="index">
					{{ '第' + (index + 1) + '节' }}
				</div>
				<div  v-if="containerInfo.containerPitchStart === '2'"
					  class="joint" v-for="(item, index) in containerList[0]"
					  :key="index">
					{{ '第' + (containerInfo.containerFloors - index) + '节' }}
				</div>
			</div>
			<div style="display: flex;margin-bottom: 20px;" v-for="(floorItem, index1) in containerList" :key="index1">
				<div v-if="containerInfo.containerFloorsStart === '1'"
					 class="storey">
					{{ '第' + (index1 + 1) + '层' }}
				</div>
				<div v-if="containerInfo.containerFloorsStart === '2'"
					 class="storey">
					{{ '第' + (containerInfo.containerFloors - index1) + '层' }}
				</div>
				<el-popover placement="top-start"
							:width="120"
							trigger="hover"
							v-for="(pitchItem, index) in floorItem"
							:key="index">
					<template #reference>
						<div
							class="lattice"
							:class="{
                            	selected: pitchItem.selected,
                            	notFull: pitchItem.storageFileCount != 0 && (pitchItem.storageFileMax > pitchItem.storageFileCount),
                            	full: pitchItem.storageFileCount != 0 && (pitchItem.storageFileMax == pitchItem.storageFileCount)
                        	}"
							@click="viewJoint(pitchItem)">
							<el-text size="small" truncated>{{ pitchItem.storageRemark }}</el-text>
						</div>
					</template>
					<div style="font-size: 12px;">
						<div>额定容量：{{ pitchItem.storageFileMax }} 盒</div>
						<div>剩余容量：{{ pitchItem.storageFileMax - pitchItem.storageFileCount }} 盒</div>
						<div v-if="pitchItem.storageGroup">
							所属全宗: {{ pitchItem.storageGroup.recordGroupName }}
						</div>
						<div v-if="pitchItem.storageCategory">
							所属门类: {{ pitchItem.storageCategory.name }}
						</div>
					</div>
				</el-popover>

			</div>
		</div>
	</div>
	<el-dialog title="档案架设置" v-model="openJoint" width="1000px" append-to-body v-if="openJoint">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="140px" style="padding-right: 20px;">
			<el-form-item label="全宗:" prop="boxGroupId" style="width: 100%;">
				<el-select v-model="form.boxGroupId" placeholder="请选择全宗" style="width: 100%;" @change="dataInfo">
					<el-option :label="item.recordGroupName" :value="item.id" v-for="item in fourConfig.group"
                               :key="item.id">
                        <div class="el-flex-row-between">
                            <span>{{ item.recordGroupName }}</span>
                            <el-tag :type=" item.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,item.openFlag) }}</el-tag>
                        </div>
                    </el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="门类:" prop="boxCategoryId" style="width:100%;">
				<el-tree-select v-model="form.boxCategoryId" placeholder="请选择门类" :data="fourConfig.category"
								highlight-current check-strictly :props="{ value: 'id', label: 'name' }"
								style="width: 100%">
					<template #default="{ data }">
                        <div class="el-flex-row-between">
                            <span>{{ data.name }}</span>
                            <el-tag :type=" data.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,data.openFlag) }}</el-tag>
                        </div>
					</template>
				</el-tree-select>
			</el-form-item>
			<el-form-item label="存放量:" prop="storageFileMax">
				<el-input v-model="form.storageFileMax" placeholder="请输入存放量"/>
			</el-form-item>
			<el-form-item label="备注:" prop="storageRemark">
				<el-input v-model="form.storageRemark" placeholder="请输入备注"/>
			</el-form-item>
		</el-form>
		<div style="text-align: center; padding-top: 20px;">
			<el-button type="primary" @click="storageSave()">确 定</el-button>
			<el-button @click="cancellation()">取 消</el-button>
		</div>
	</el-dialog>
	<el-dialog title="档案盒详情" v-model="open" width="1000px" append-to-body v-if="open" style="height: 700px;">
		<el-table :data="tableData" style="width: 100%" row-key="id" lazy :load="load" height="560" border>
			<el-table-column label="盒名称" align="center" prop="boxName"/>
			<el-table-column label="档案盒规格" align="center" prop="boxSpecification"/>
			<el-table-column label="年份" align="center" prop="boxYear"/>
			<el-table-column label="容量" align="center" prop="boxSize"/>
			<el-table-column align="center" label="装盒人" prop="boxIntoPerson.name"/>
			<el-table-column label="备注" align="center" prop="boxRemark"/>
			<el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150px">
				<template #default="scope">
					<el-button link icon="View" type="primary" @click="viewFiles(scope.row)">查看
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<div style="position: absolute;bottom: 20px;right: 20px;">
			<el-button plain @click="cancellationBox()">取消</el-button>
		</div>
	</el-dialog>
	<el-dialog title="档案详情" v-model="openFiles" width="1300px" append-to-body v-if="openFiles"
			   style="height: 700px;">
		<el-table :data="receiveData" border height="560">
			<el-table-column align="center" min-width="30" type="selection" width="50"/>
			<el-table-column label="档案名称" align="center" prop="name" width="300"/>
			<el-table-column label="档案号" align="center" prop="num"/>
			<el-table-column label="档案年份" align="center" prop="year" width="80"/>
			<el-table-column label="档案月份" align="center" prop="month" width="80"/>
			<el-table-column label="档案创建时间" align="center" prop="createDate" :formatter='dateFormat' width="180">
			</el-table-column>
			<el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100px">
				<template #default="scope">
					<el-button link type="primary" @click="collectFile(scope.row)">查看
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<div style="position: absolute;bottom: 20px;right: 20px;">
			<div>
				<el-button plain @click="cancellationFiles()">取消</el-button>
			</div>
		</div>
	</el-dialog>
	<!-- 查看 -->
	<el-dialog v-if="openView" v-model="openView" append-to-body title="查看" top="8vh" width="92%">
		<viewFile :receiveId="receiveId" @childMove="parentView"></viewFile>
	</el-dialog>
</template>
<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
// 查询全宗和门类
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';
import category from '@/api/archive/categoryManagement/category';
//查询档案盒
import frameList from '@/api/archive/storeroom/upDownFrame';
// 查询档案
import boxList from '@/api/archive/storeroom/filingBox';
// 查看弹窗
import viewFile from '../view.vue';
import {getOrganizationInfo} from "@/utils/permission";

export default {
	name: 'shelves',
	props: ['latticeView',],
	components: {
		viewFile
	},
	data() {
		return {
			// 节
			pitch: "",
			// 层
			floor: '',
			// 选择AB面
			counterType: '1',
			openJoint: false,
			form: {},
			fourConfig: {},
			queryParams: {
				current: 1,
				size: 10
			},
			total: 0,
			open: false,
			// 详情data
			tableData: [],
			//选中格子的id
			selectedIds: [],
			load: false,
			// 档案柜列表
			containerList: [],
			containerInfo: {},
			// 点击档案架是否是多选
			containerType: '1',
			// 档案
			receiveData: [],
			openFiles: false,
			// 查看时档案详情数据
			receiveId: [],
			openView: false,
			// 是否显示AB面
			counterTypeVal: '',
			// 提示显示内容
			content: {
				rated: '',
				reality: ''
			},
            openFlagOptions:[],
		}
	},
    async mounted () {
        await this.containerById(this.latticeView);
		this.getById();
        this.openFlagOptions = await this.getDictList("data_permission_open_flag");
	},
	methods: {
		// 确定设置
		storageSave() {
			let ids = this.selectedIds.map((v) => v);
			let idStr = ids.join(",");
			storeroomData.updateStorageInfoList({
				ids: idStr,
				groupId: this.form.boxGroupId,
				categoryId: this.form.boxCategoryId,
				storageFileMax: this.form.storageFileMax,
				storageRemark: this.form.storageRemark
			}).then(res => {
				if (res.code === 200) {
					this.$message.success("保存成功");
                    this.containerById(this.latticeView);
                    this.getById();
					this.openJoint = false;
				}
			}).catch(err => {
				this.$Response.errorNotice(err, "保存失败");
			})
		},
		// 展示档案柜
		async containerById(id) {
			storeroomData.getStoragesByContainerId({
				containerId: id,
				counterType: this.counterType
			}).then(res => {
				if (res.code === 200) {
					this.containerList = res.data;
				}
			}).catch(err => {
				this.$Response.errorNotice(err, "查询失败");
			})
		},
		getById() {
			storeroomData.containerById({
				id: this.latticeView
			}).then(res => {
				if (res.code === 200) {
					this.counterTypeVal = res.data.containerCounterType;
					this.containerInfo = res.data;
				}
			}).catch(err => {
				this.$Response.errorNotice(err, "查询失败");
			})
		},
		// 确定
		determine() {
			this.containerById(this.latticeView);
		},
		// 取消设置
		cancellation() {
			this.openJoint = false;
			this.containerType = '1';
		},
		// 档案架设置
		storageAdd() {
			if (this.selectedIds.length > 0) {
				this.containerType = '2';
				this.getManagement();
				this.openJoint = true;
			} else {
				this.$Response.errorNotice('', "请先选择需要设置的具体架位!");
			}
		},
		// 选择档案架子
		storageSelect() {
			this.containerType = '2';
		},
		// 取消架子
		cancellationSelect() {
			this.containerType = '1';
			this.selectedIds = [];
			this.determine();
		},
		// 查询全宗
		getManagement() {
            completeManagement.getGroupSelect({ orgId: getOrganizationInfo()?.id,openFlag:false}).then(res => {
				if (res.code === 200) {
					this.fourConfig.group = res.data || [];
				}
			}).catch(() => {
				this.$Response.errorNotice("查询失败");
			});
		},
		// 查询门类
		dataInfo(groupId) {
            this.form.boxCategoryId = undefined;
            category.getCategorySelect({ orgId: getOrganizationInfo()?.id,openFlag:false,groupId}).then(res => {
                if (res.code === 200) {
                    this.fourConfig.category = res.data || [];
                }
            }).catch(() => {
                this.$Response.errorNotice("查询失败");
            })
		},
		// 查询档案
		viewFiles(val) {
			boxList.mainList({
				'box.id': val.id
			}).then(res => {
				if (res.code === 200) {
					this.receiveData = res.data.records;
					this.open = false;
					this.openFiles = true;
				}
			}).catch(() => {
				this.$Response.errorNotice("查询失败");
			})
		},
		// 取消档案盒子查看
		cancellationBox() {
			this.containerById(this.latticeView);
			this.open = false;
		},
		// 取消档案
		cancellationFiles() {
			this.open = true;
			this.openFiles = false;

		},
		// 查看档案详情
		collectFile(val) {
			this.openView = true;
			this.receiveId = val;
		},
		// 取消查看
		parentView() {
			this.openView = false;
		},
		// 点击档案架
		viewJoint(pitch) {
			if (this.containerType === '2') {
				pitch.selected = !pitch.selected;
				if (pitch.selected) {
					this.selectedIds.push(pitch.id);
				} else {
					const index = this.selectedIds.indexOf(pitch.id);
					if (index !== -1) {
						this.selectedIds.splice(index, 1);
					}
				}
			} else {
				frameList.list({
					'storage.id': pitch.id
				}).then(res => {
					if (res.code === 200) {
						this.open = true;
						this.tableData = res.data.records;
					}
				})
			}
		},
		// 时间格式转换
		dateFormat(row, column) {
			const daterc = row[column.property];
			if (daterc != null) {
				let date = new Date(daterc);
				let year = date.getFullYear();
				/*
				 * 在日期格式中，月份是从0开始，11结束，因此要加0
				 * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
				 */
				let month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
				let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
				let hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
				let minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
				let seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
				// 拼接
				return (year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds
				)
			}
		}
	}
}
</script>
<style>
.selected {
	background-color: #ccc;
}

.notFull {
	background-color: #81ee66;
}

.full {
	background-color: #db1b1b;
}

.lattice {
	width: 80px;
	height: 110px;
	border: 1px solid #368cca;
	cursor: pointer;
	margin-left: 20px;
	border-radius: 3px;
	display: flex;
	justify-content: center;
}

/* .no-left-border {
    border-left: none;
} */

.joint {
	width: 80px;
	text-align: center;
	font-size: 14px;
	font-weight: 600;
	margin-left: 20px;
}

.storey {
	width: 80px;
	margin-top: 0.25rem;
	font-size: 14px;
	font-weight: 600;
	text-align: center;
}
</style>
