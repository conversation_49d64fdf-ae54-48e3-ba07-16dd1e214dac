<template>
	<div class="category-tree" v-loading="loading">
        <div class="filter-input">
            <el-input
                v-model="filterText"
                placeholder="输入关键字进行过滤"
                clearable
            />
            <el-button :type="isUnfold ? 'success' : 'warning'" size="small" @click="unfoldClose()">
                <el-icon :class="{ rotated: isUnfold }" class="unfold-icon">
                    <arrow-down />
                </el-icon>
                {{ isUnfold ? '展开' : '收起' }}
            </el-button>
        </div>
        <el-divider style="margin: 0;"/>
		<el-main class="noPadding" :style="`height: ${height}`">
			<el-tree
				:data="treeData"
				:props="defaultProps"
				:filter-node-method="filterNode"
                :expand-on-click-node="false"
				ref="treeRef"
				node-key="id"
				highlight-current
				:default-expand-all="defaultExpandAll"
				class="tree-list"
				@node-click="clickNode"
            >
                <template #default="{ node,data }">
                    <div class="tree-node">
                        <div class="node-content">
                            <span class="node-label">{{
                                    node.label
                                }}</span>
                            <div class="node-tags">
                                <el-tag v-if="data.isGroup" size="small" type="primary">全宗</el-tag>
                                <el-tag :type=" data.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,data.openFlag)}}</el-tag>
                            </div>
                        </div>
                    </div>

                </template>
            </el-tree>
		</el-main>
	</div>
</template>

<script setup>
import { ref, watch, defineProps,onMounted ,getCurrentInstance} from "vue";
const emit = defineEmits(["clickNode"]);
import category from "@/api/archive/categoryManagement/category";
import { getOrganizationInfo } from "@/utils/permission";
import {recursiveSort} from "@/utils";
const props = defineProps({
    queryParams: {
		type: Object,
		default: () => ({
            orgId: getOrganizationInfo()?.id,
        }),
	},
    height:{
        type: String,
        default: ()=>{
            return window.innerHeight - 170 + "px";
        },
    },
    defaultExpandAll:{
        type: Boolean,
        default: false,
    },
    isTheFirstLevelClickable:{ // 是否允许点击第一层
        type: Boolean,
        default: false,
    }
});
const { proxy } = getCurrentInstance();
const filterText = ref("");
const treeRef = ref();
const treeData = ref([]);
const loading = ref(false);

const defaultProps = {
	children: "children",
	label: "name",
};
const openFlagOptions = ref([]);
const isUnfold = ref(!props.defaultExpandAll);

watch(filterText, (val) => {
	treeRef.value && treeRef.value.filter(val);
});

onMounted(async() => {
    openFlagOptions.value = await proxy.getDictList("data_permission_open_flag");
    // 初始化逻辑
    await getCategoryList();
});
/**
 * 展开关闭
 */
const unfoldClose = () => {
    const nodes = treeRef.value.store._getAllNodes();
    nodes.forEach((node) => {
        node.expanded = isUnfold.value;
    });

    isUnfold.value = !isUnfold.value;
}
/**
 * 获取全宗以及门类列表
 * @returns {Promise<void>}
 */
const getCategoryList = async () => {
    loading.value = true;
    try {
        const result = await category.getCategoryTree(props.queryParams);
        if (result.code !== 200) {
            proxy.msgError(result.msg || "获取全宗门类树列表失败");
            return;
        }

        const treeDataList = result.data;
        if (!treeDataList?.length) {
            treeData.value = [];
            return;
        }
        treeDataList.forEach((item) => {
            item.isGroup = true;
            item.disabled = true;
        });
        // 排序处理
        treeData.value = recursiveSort(treeDataList, "sort");
    } catch (error) {
        proxy.msgError("系统门类数据查询失败");
        treeData.value = [];
    } finally {
        loading.value = false;
    }
};
/**
 * 搜索
 * @param value
 * @param data
 * @returns {boolean}
 */
function filterNode(value, data) {
	if (!value) return true;
	return data.name && data.name.indexOf(value) !== -1;
}

function clickNode(val) {
    if(props.isTheFirstLevelClickable || !val.isGroup){
        emit("clickNode", val);
    }
}
</script>

<style scoped lang="scss">
.category-tree {
	background: #fff;
	width: 100%;
    height: calc(100% - 60px);
	flex-shrink: 0;
    .filter-input{
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        padding: 20px;
        ::v-deep .el-input__inner{
            border-radius: 0;
        }
        .unfold-icon {
            transition: transform 0.3s ease;
            margin-right: 2px;
        }

        .unfold-icon.rotated {
            transform: rotate(180deg);
        }
    }
    .tree-list {
        font-size: 14px;
        background: transparent;
        height: 100%;
        width: 100%;
        :deep(.el-tree-node__content) {
            height: 40px;
            border-radius: 6px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        :deep(.el-tree-node__content:hover) {
            background: #f0f9ff;
        }
        :deep(.el-tree-node.is-current > .el-tree-node__content) {
            background: #e6f7ff;
            color: #1890ff;
        }
        .tree-node {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .node-content {
            display: flex;
            align-items: center;
            flex: 1;
        }


        .node-label {
            font-size: 14px;
            margin-right: 8px;
        }
        .node-tags {
            display: flex;
            gap: 5px;
        }

    }

}
</style>
