<template>
	<el-dialog v-if="props.openStatus" v-model="props.openStatus" :close-on-click-modal="false" :close-on-press-escape="false" :title="props.title"
			   align-center append-to-body destroy-on-close width="52%"
			   @close="cancellation">
		<el-container>
			<el-main ref="main" v-loading="loading" class="noPadding">
				<el-form ref="formRef" :disabled="viewModel" :error="errorBox" :inline="true" :model="formBox"
						 :rules="rules" label-width="auto">
					<el-form-item label="盒编号:" prop="boxNum" style="width: 46%;">
						<el-input v-model="formBox.boxNum" clearable :disabled="moveModel" placeholder="请输入盒编号"/>
					</el-form-item>
					<el-form-item label="盒名称:" prop="boxName" style="width: 46%;">
						<el-input v-model="formBox.boxName" clearable :disabled="moveModel" placeholder="请输入盒名称"/>
					</el-form-item>
					<el-form-item label="盒规格:" prop="boxSpecification" style="width: 46%;">
						<el-input v-model="formBox.boxSpecification" clearable :disabled="moveModel" placeholder="请输入盒规格"/>
					</el-form-item>
					<el-form-item label="全宗:" prop="boxGroupId" style="width: 46%;">
						<el-select v-model="formBox.boxGroupId" placeholder="请选择全宗" clearable filterable style="width: 100%;" @change="onGroupChange">
							<el-option v-for="item in fourConfig.group"
									   :key="item.id"
									   :label="item.recordGroupName"
                                       :value="item.id">
                                <div class="el-flex-row-between">
                                    <span>{{ item.recordGroupName }}</span>
                                    <el-tag :type=" item.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,item.openFlag) }}</el-tag>
                                </div>
                            </el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="门类:" prop="boxCategoryId" style="width: 46%;">
						<el-tree-select v-model="formBox.boxCategoryId" :data="fourConfig.category"
										:props="{ value: 'id', label: 'name' }"
										check-strictly
										highlight-current
                                        filterable
                                        clearable
										placeholder="请选择门类"
										style="width: 100%">
							<template #default="{ data }">
                                <div class="el-flex-row-between">
                                    <span>{{ data.name }}</span>
                                    <el-tag :type=" data.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,data.openFlag) }}</el-tag>
                                </div>
							</template>
						</el-tree-select>
					</el-form-item>
					<el-form-item label="年份:" prop="boxYear" style="width: 46%;">
						<el-date-picker v-model="formBox.boxYear" clearable :disabled="moveModel" format="YYYY"
										placeholder="请选择年份" style="width: 100%"
										type="year" value-format="YYYY"/>
					</el-form-item>
					<el-form-item label="月份:" prop="boxMonth" style="width: 46%;">
						<el-date-picker v-model="formBox.boxMonth" clearable :disabled="moveModel" format="MM"
										placeholder="请选择月份" style="width: 100%"
										type="month" value-format="MM"/>
					</el-form-item>
					<el-form-item label="盒内数量:" prop="boxSize" style="width: 46%;">
						<el-input v-model="formBox.boxSize" clearable :disabled="moveModel" placeholder="请输入盒内数量"/>
					</el-form-item>
					<el-form-item label="档案盒保留期限:" prop="boxRetentionPeriod" style="width: 46%;">
						<el-input v-model="formBox.boxRetentionPeriod" clearable :disabled="moveModel"
								  placeholder="请输入档案盒保留期限">
							<template #append>
								<span>年</span>
							</template>
						</el-input>
					</el-form-item>
					<el-form-item label="备注:" prop="boxRemark" style="width: 95.5%;">
						<el-input v-model="formBox.boxRemark" clearable :disabled="moveModel" :rows="6" placeholder="请输入备注"
								  type="textarea"/>
					</el-form-item>
				</el-form>
			</el-main>

			<!-- 查看 -->
			<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="8vh" width="92%">
				<viewFiles :receiveId="receiveId" @childMove="parentView"></viewFiles>
			</el-dialog>
		</el-container>
		<template #footer>
			<el-button plain @click="cancellation">
				取消
			</el-button>
			<el-button v-if="!viewModel" type="primary" @click="addBox">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import {defineProps, getCurrentInstance, reactive, ref, toRefs, watch,onMounted} from 'vue'
import viewFiles from '../view.vue';
import libraries from "@/api/archive/managementFile/managingLibraries";
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';
import category from "@/api/archive/categoryManagement/category";
import tool from "@/utils/tool";
import {getOrganizationInfo} from "@/utils/permission";

const emit = defineEmits(["closeMethod"]);
const props = defineProps({
	title: {
		type: String,
		default: ''
	},
	openStatus: {
		type: Boolean,
		default: false
	},
	boxInfo: {
		type: Object,
		default: () => {
            return {}
        }
	},
	viewModel: {
		type: Boolean,
		default: false
	},
	moveModel: {
		type: Boolean,
		default: false
	},
})
const loading = ref(false)
const data = reactive({
	fourConfig: {},
})
const {fourConfig} = toRefs(data)
const {proxy} = getCurrentInstance()
const receiveId = ref('')
const errorBox = ref('')
const formRef = ref(null);
const title = ref('')
const openView = ref(false)
const viewModel = ref(false)
// 新增表单
const formBox = ref({
	boxYear: formatDate(new Date()),
	boxMonth: formatDate(new Date()),
})
// 表单校验
const rules = reactive({
	boxNum: [
		{required: true, message: '请输入盒编号', trigger: 'blur'},
		{validator: validatePass, trigger: 'blur'}
	],
	boxName: [
		{required: true, message: '请输入盒名称', trigger: 'blur'},
		{validator: validatePass, trigger: 'blur'}
	],
	boxSpecification: [
		{required: true, message: '请输入盒规格', trigger: 'blur'},
	],
	boxGroupId: [
		{required: true, message: '请选择全宗', trigger: 'change'},
	],
	boxCategoryId: [
		{required: true, message: '请选择门类', trigger: 'change'},
	],
	boxYear: [
		{required: true, message: '请选择年份', trigger: 'change'},
	],
	boxSize: [
		{required: true, message: '请输入盒内数量', trigger: 'blur'},
		{pattern: /^[0-9]*$/, message: '只能为数字', trigger: 'blur'}
	],
	boxRetentionPeriod: [
		{required: true, message: '请输入档案盒保留年限', trigger: 'blur'},
		{pattern: /^[0-9]*$/, message: '只能为数字', trigger: 'blur'}
	],
	boxIntoPersonId: [
		{required: true, message: '请输入装盒人', trigger: 'blur'},
	],
})
const openFlagOptions = ref([]);

watch(() => props.openStatus, async (newData) => {
	if (newData) {
		formBox.value = {
			boxYear: formatDate(new Date()),
			boxMonth: formatDate(new Date()),
		}
        // await getManagement();
		// 如果是编辑模式且有boxInfo，先加载对应全宗的门类
		if (props.boxInfo && props.boxInfo.controlGroup) {
			getCategory(props.boxInfo?.controlGroup?.id);
		} else {
			// 新增模式，初始化为空的门类列表
			fourConfig.value.category = [];
		}
	}
	if (props.viewModel) {
		viewModel.value = true;
	}
	if (props.boxInfo) {
		formBox.value = {};
		formBox.value = {
			...formBox.value,
			boxGroupId: props.boxInfo.controlGroup?.id,
			boxCategoryId: props.boxInfo.controlCategory?.id,
			...props.boxInfo
		};
		delete formBox.value.controlGroup;
		delete formBox.value.controlCategory;
	}
});
onMounted(async() => {
    openFlagOptions.value = await proxy.getDictList("data_permission_open_flag");
    await getManagement()
})
// 查询全宗
async function getManagement() {
    try {
       const res = await completeManagement.getGroupSelect({
           orgId: getOrganizationInfo()?.id,
           openFlag:false
       });
       if(res.code === 200){
           fourConfig.value.group = res.data || [];
       }
    } catch (e){
        proxy.msgError('获取全宗门类失败');
    }
}

// 查询门类
function getCategory(groupId = null) {
    category.getCategorySelect({ orgId: getOrganizationInfo()?.id,openFlag:false,groupId}).then(res => {
        if (res.code === 200) {
            fourConfig.value.category = res.data || [];
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

// 全宗变化时的处理函数
function onGroupChange(groupId) {
	// 清空门类选择
	formBox.value.boxCategoryId = null;
	// 根据选中的全宗加载对应的门类
	if (groupId) {
		getCategory(groupId);
	} else {
		// 如果没有选择全宗，清空门类列表
		fourConfig.value.category = [];
	}
}

// 校验参数
function validatePass(rule, value, callback) {
	let data = {};
	data.boxNum = rule.field === "boxNum" ? value : null;
	data.boxName = rule.field === "boxName" ? value : null;
	if (value === '' || value === null) {
		callback(new Error('请输入数据！'))
	}
	if (props.boxInfo?.id) {
		callback();
	}
	libraries.findListConditions(data).then(res => {
		if (res.code === 200) {
			if (res.data.length > 0) {
				callback(new Error('该数据已存在请重新输入！'))
			} else {
				callback()
			}
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 取消
function cancellation() {
	viewModel.value = false;
	emit("closeMethod");
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 格式化日期
function formatDate(date) {
	const year = date.getFullYear();
	return `${year}`;
}

// 验证盒名称和编号是否存在
function testVerify(val) {
	libraries.list({
		current: 1,
		size: -1,
		boxNum: val,
        'org.id': getOrganizationInfo()?.id,
	}).then(res => {
		if (res.code === 200) {
			if (res.data.records === '') {
				errorBox.value = '该名称已存在请重新输入！'
				console.log(errorBox.value, '----');
			}
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 确定提交新增
function addBox() {
	loading.value = true;
	proxy.$refs["formRef"].validate(valid => {
		if (valid) {
			libraries.save({
				'controlGroup': {
					id: formBox.value.boxGroupId
				},
				'controlCategory': {
					id: formBox.value.boxCategoryId
				},
				'boxIntoPerson': {
					id: tool.data.get("USER_INFO")?.id
				},
				id: props.boxInfo.id ? props.boxInfo?.id : null,
				boxMonth: formBox.value.boxMonth,
				boxRetentionPeriod: formBox.value.boxRetentionPeriod,
				boxNum: formBox.value.boxNum,
				boxName: formBox.value.boxName,
				boxRemark: formBox.value.boxRemark,
				boxSize: formBox.value.boxSize,
				boxSpecification: formBox.value.boxSpecification,
				boxYear: formBox.value.boxYear,
			}).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess(props.title + '成功');
					formRef.value.resetFields();
					loading.value = false;
					cancellation();
				}
			}).catch((error) => {
				console.log(error);
				formRef.value.resetFields();
				loading.value = false;
				proxy.msgError(props.title + '失败');
			})
		} else {
			loading.value = false;
		}
	})
}
</script>

<style scoped>
</style>
