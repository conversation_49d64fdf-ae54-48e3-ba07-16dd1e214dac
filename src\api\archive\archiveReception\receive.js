import http from "@/utils/request"

export default {
    // 查询档案原始信息主表列表数据
    list: function (params) {
        return http.get(
			'/archive/original/info/list',
			params
        )
    },
    // 查询档案原始结构数据表列表数据
    getOriginalInfoByMasterId: function (params) {
        return http.get(
            '/archive/original/data/getOriginalInfoByMasterId',
            params
        )
    },
	// 查询档案原始结构数据
	getDataById: function (params) {
		return http.get(
			'/archive/original/data/getDataById',
			params
		)
	},
    // 查询档案原始文件信息表列表数据
    fileFist: function (params) {
        return http.get(
            '/archive/original/file/list',
            params
        )
    },
    // 档案原始信息合并成件
    mergeOriginalInfo: function (params) {
        return http.post(
            '/archive/original/info/mergeOriginalInfo',
            params
        )
    },
    // 查询档案原始信息主表列表数据
    queryById: function (params) {
        return http.get(
            '/archive/file/relevance/list',
            params
        )
    },
    // 保存或更新档案文件关联信息
    save: function (params) {
        return http.post(
            '/archive/file/relevance/save',
            params
        )
    },
    // 根据id返回有关联的文件集合
    getRelevanceFileList: function (params) {
        return http.get(
            '/archive/file/relevance/getRelevanceFileList',
            params
        )
    },
}
