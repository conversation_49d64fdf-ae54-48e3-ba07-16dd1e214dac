<template>
    <DragLayout v-model="layoutScale" type="row">
        <template #first>
            <el-aside class="w-full h-full">
                <tree @clickChild="menuClick" height="500px"/>
            </el-aside>
        </template>
        <template #second>
            <el-container>
                <el-main ref="main" class="noPadding">
                    <div v-if="!openStorey" style="display: flex; justify-content: center">
                        <div style="margin: 20% 0">
                            <img alt="" src="img/archiveTag.png" style="width: 300px" />
                            <div style="margin-left: 15%">请根据左侧树形结构选择档案</div>
                        </div>
                    </div>
                    <!-- 格子展示 -->
                    <div v-if="openStorey">
                        <lattice :key="componentKey" :latticeView="latticeView" :receiveId="receiveId" @childMove="childMove"> </lattice>
                    </div>
                </el-main>
            </el-container>
        </template>
    </DragLayout>
</template>

<script>
import lattice from "@/views/storeroom/upDownFrame/lattice.vue";
import tree from "@/views/storeroom/tree/index.vue";
import DragLayout from '@/components/DragLayout/index.vue';

export default {
    name: 'storeroom',
    components: {
        lattice,
        tree,
        DragLayout
    },
    props: ['receiveId'],

    data() {
        return {
            layoutScale:{ first: "30%", second: "70%" },
            // 右侧仓库展示
            detailsOpen: true,
            componentKey: 0,
            //父级关联Id
            parentTargetId: '',
            //父级ID
            parentId: '',
            mainID: '',
            ID: '',
            title: '',
            // 新增弹窗
            openAdd: false,
            // 分页
            queryParams: {
                current: 1,
                size: 10
            },
            total: 0,
            // 库房数据
            storeroomList: [],
            parentDataInfo: [],
            // 所属区域
            houseRegion: '',
            // 柜子查看展示
            openStorey: false,
            // 货架展示
            openShelves: false,
            latticeView: '',
            // 货架
            list: [
                {
                    name: '1',
                    name1: '2',
                    name2: '3',
                    name3: '4',
                    name4: '5',
                    name5: '6',
                    name6: '7',
                    name7: '8',
                    name8: '9',
                    name9: '10',
                    name10: '10'
                },
                {
                    name: '1',
                    name1: '2',
                    name2: '3',
                    name3: '4',
                    name4: '5',
                    name5: '6',
                    name6: '7',
                    name7: '8',
                    name8: '9',
                    name9: '10',
                    name10: '10'
                }
            ],
            // 货柜form
            form: {},
            // 货柜编辑弹窗
            open: false,
            // 展示货柜页
            openContainer: false,
            // 档案柜参数
            parameters: [],
            // 密集柜详情
            openJoint: false,

            //查询表单
            searchForm: {},
            // 档案库区分
            filesType: ''
        };
    },
    watch: {},
    mounted() {
        // this.getDataList();
        this.detailsOpen = true;
    },
    methods: {
        // 新增库房
        addLibrary(type, node, data) {
            if (data.recordHouseInfoType === '1') {
                this.ID = '';
                this.mainID = '';
                this.filesType = '2';
                this.parentId = data.id;
                this.title = '新增档案室';
                this.parentTargetId = data.recordHouseInfoTargetId;
                this.openAdd = true;
            } else if (data === '1') {
                this.ID = '';
                this.mainID = '';
                this.filesType = '1';
                this.title = '新增档案库';
                this.openAdd = true;
            } else if (data.recordHouseInfoType === '2') {
                this.ID = '';
                this.filesType = '3';
                this.parentId = data.id;
                this.title = '新增档案柜';
                this.parentTargetId = data.recordHouseInfoTargetId;
                this.openAdd = true;
            }
        },
        // 关闭弹窗
        childMove() {
            this.$emit('openNewAdd');
        },

        // 查看柜详情
        handleView(data) {
            this.latticeView = data.id;
            this.detailsOpen = false;
            this.openShelves = false;
            this.openContainer = false;
            this.openStorey = true;
        },

        //右侧点击查看库房详情
        libraryView(data) {
            this.menuClick(data, null, '1');
        },
        // 左侧树结构点击事件
        menuClick(data, node, type) {
            this.detailsOpen = false;
            this.componentKey += 1;
            switch (data.recordHouseInfoType || type) {
                case '1':
                    this.parentDataInfo = data;
                    this.openShelves = true;
                    this.detailsOpen = false;
                    this.openContainer = false;
                    this.openStorey = false;
                    break;
                case '2':
                    this.parameters = data;
                    this.detailsOpen = false;
                    this.openShelves = false;
                    this.openContainer = true;
                    this.openStorey = false;
                    break;
                case '3':
                    this.latticeView = data.recordHouseInfoTargetId;
                    this.detailsOpen = false;
                    this.openShelves = false;
                    this.openContainer = false;
                    this.openStorey = true;
                    break;
            }
        },

        // 关闭左侧
        openNewAdd() {
            this.openAdd = false;
            // this.getDataList();
        },
        /*
         * 根据Id获取仓库配置数据
         * @author: saya
         */
        queryById(node, data) {
            switch (data.recordHouseInfoType) {
                case '1':
                    this.mainID = data.id;
                    this.ID = data.recordHouseInfoTargetId;
                    this.filesType = data.recordHouseInfoType;
                    this.title = '修改档案库';
                    this.openAdd = true;
                    break;
                case '2':
                    this.mainID = data.id;
                    this.parentId = data.parentId;
                    this.ID = data.recordHouseInfoTargetId;
                    this.filesType = data.recordHouseInfoType;
                    this.title = '修改档案室';
                    this.openAdd = true;
                    break;
                case '3':
                    this.mainID = data.id;
                    this.parentId = data.parentId;
                    this.ID = data.recordHouseInfoTargetId;
                    this.filesType = data.recordHouseInfoType;
                    this.title = '修改档案室';
                    this.openAdd = true;
                    break;
            }
        },
        // 右侧点击查看档案室详情
        shelvesView(data) {
            this.menuClick(data, null, '2');
        },

        // 查看节
        viewJoint() {
            this.openJoint = true;
        }
    }
};
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
    display: flex;
    flex: 1;
    height: 100%;
}
.tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.node-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.node-icon {
    margin-right: 8px;
    color: #909399;
}

.node-label {
    font-size: 14px;
    margin-right: 8px;
}
.node-tags {
    display: flex;
    gap: 5px;
}
</style>
<script setup lang="ts"></script>
