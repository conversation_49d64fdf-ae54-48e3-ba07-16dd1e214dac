<template>
  <el-container>
    <el-header height="auto" style="background: none;border-bottom: none;padding: 10px 10px 5px 10px">
      <el-card class="query-form">
          <el-form ref="queryForm" :inline="true" :model="queryParams" label-position="right" label-width="75px">
              <el-form-item label="全宗名称:" prop="tagName"  >
                  <el-input v-model="queryParams.tagName" class="form_225" placeholder="请输入需要查询的名称" @keydown.enter="handleQuery"/>
              </el-form-item>
              <el-form-item >
                  <el-button icon="Search" style="margin-left: 20px;" type="primary" @click="handleQuery">查询</el-button>
                  <el-button icon="RefreshRight" plain @click="() => resetQuery()">重置</el-button>
              </el-form-item>
          </el-form>
			</el-card>
    </el-header>
    <el-main style="padding: 5px 10px 10px 10px">
      <el-card :body-style="{height: '100%',width: '100%',padding: '20px 20px 0 20px'}"
               style="height: 100%;width: 100%">
        <el-container>
          <el-header height="auto" style="background: none;border-bottom: none;padding: 0 0 15px 0">
            <el-button icon="Plus" plain type="primary" @click="() => {form = {};open = true;}">
              新增
            </el-button>
          </el-header>
          <el-main style="padding: 0">
            <el-table v-loading="loading" :data="dataList" border style="height: 100%;width: 100%">
              <el-table-column align="center" label="序号" prop="sort" width="80">
                <template #default="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="标签名称" prop="tagName"/>
              <el-table-column align="center" label="标签主题" prop="tagTheme">
                <template #default="scope">
                  {{ judgeTheme(scope.row.tagTheme).label }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="标签形状" prop="tagRound">
                <template #default="scope">
                  {{ judgeShape(scope.row.tagRound).label }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="标签颜色" prop="tagColor">
                <template #default="scope">
                  {{ judgeColor(scope.row.tagColor).label }}
                </template>
              </el-table-column>
                <el-table-column align="center" label="组织机构" prop="org.name"/>
              <el-table-column align="center" label="标签备注" prop="tagRemark"/>
              <el-table-column align="center" class-name="small-padding fixed-width" label="操作"
                               min-width="120">
                <template #default="scope">
                  <el-button icon="Edit" link type="primary" @click="editInfo(scope.row)">
                    编辑
                  </el-button>
                  <el-popover placement="left" trigger="hover" width="50">
                    <template #reference>
                      <el-button icon="View" link type="primary">预览</el-button>
                    </template>
                    <el-tag
                        :effect="judgeTheme(scope.row.tagTheme).type"
                        :round="judgeShape(scope.row.tagRound).type"
                        :type="judgeColor(scope.row.tagColor).type"
                        auto-close="300"
                        style="margin-left: 22%;"
                    >
                      {{ scope.row.tagName }}
                    </el-tag>
                  </el-popover>
                  <el-button icon="Delete" link type="danger" @click="deleteInfo(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-main>
          <el-footer>
            <div style="display: flex;justify-content: flex-end">
              <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
                          :total="total" style="padding: 0" @pagination="selectList()"/>
            </div>
          </el-footer>
        </el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog :title="title" v-model="open" width="30%" append-to-body v-if="open">
			<el-form v-loading="loading" ref="formRef" :model="form" :rules="rules" label-width="auto"
					 style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="标签名称" prop="tagName">
					<el-input v-model="form.tagName" placeholder="请输入标签名称"/>
				</el-form-item>
				<el-form-item label="标签主题" prop="tagTheme">
          <el-select v-model="form.tagTheme" placeholder="请选择标签主题" style="width: 100%;">
						<el-option label="深色" value="1"/>
						<el-option label="浅色" value="2"/>
						<el-option label="默认" value="3"/>
					</el-select>
				</el-form-item>
				<el-form-item label="标签形状" prop="tagRound">
          <el-select v-model="form.tagRound" placeholder="请选择标签形状" style="width: 100%;">
						<el-option label="圆角" value="1"/>
						<el-option label="椭圆" value="2"/>
					</el-select>
				</el-form-item>
				<el-form-item label="标签颜色" prop="tagColor">
          <el-select v-model="form.tagColor" placeholder="请选择标签颜色" style="width: 100%;">
						<el-option label="默认" value="1"/>
						<el-option label="绿色" value="2"/>
						<el-option label="灰色" value="3"/>
						<el-option label="红色" value="4"/>
						<el-option label="橙色" value="5"/>
					</el-select>
				</el-form-item>
				<el-form-item label="备注" prop="tagRemark">
					<el-input v-model="form.tagRemark" placeholder="请输入备注" type="textarea" maxlength="500"
							  :autosize="{minRows: 6}"
							  show-word-limit clearable/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitData">确 定</el-button>
					<el-button @click="() => open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref, toRefs} from 'vue';
import tagsManagement from '@/api/archive/tagsManagement';
import {getOrganizationInfo} from "@/utils/permission";
import sysOrgService from "@/api/model/sys/sysOrgService";

const {proxy} = getCurrentInstance();
const institution = ref([]);

const data = reactive({
	form: {
		id: '',
		tagName: '',
		tagTheme: '',
		tagRound: '',
		tagColor: '',
		tagRemark: ''
	},
	queryParams: {
		current: 1,
		size: 10,
        tagName: undefined,
        orgId: undefined,
	}
});
// 表单校验
const rules = reactive({
	tagName: [{required: true, message: '请输入数据名称', trigger: 'blur'},],
	tagTheme: [{required: true, message: '请选择标签主题', trigger: 'change'},],
	tagRound: [{required: true, message: '请选择标签形状', trigger: 'change'},],
	tagColor: [{required: true, message: '请选择标签颜色', trigger: 'change'},],
	tagRemark: [{required: true, message: '请输入标签备注', trigger: 'blur'},],
})
const loading = ref(false);
const total = ref(0);
const {queryParams, form} = toRefs(data);
// 数据集合
const dataList = ref([]);
//弹窗状态
const open = ref(false);
const title = ref("");
const formRef = ref(null);

onBeforeMount(() => {
	selectList();
    institutionList();
});
//查询数据集合
function selectList() {
	loading.value = true
    // 传getOrganizationInfo中的id
    queryParams.value.orgId = getOrganizationInfo()?.id;
    const {orgId,...params } = queryParams.value;
	tagsManagement.getList({
        ...params,
        'org.id':orgId
	}).then(res => {
		if (res.code === 200) {
			dataList.value = res.data.records;
			total.value = res.data.total;
			loading.value = false;
		}
	})
}
/**
 * 机构列表
 */
function institutionList() {
    sysOrgService.list({
        current: 1,
        size: -1
    }).then(res => {
        if (res.code === 200) {
            institution.value = res.data.records
        }
    }).catch(() => {
        this.msgError('查询失败');
    })
}

/**
 * 重置搜索
 */
function resetQuery(){
    proxy.$refs["queryForm"].resetFields();
    queryParams.value.current = 1;
    selectList();
}

/**
 * 搜索
 */
function handleQuery(){
    queryParams.value.current = 1;
    selectList();
}

//数据提交
function submitData() {
    formRef.value.validate((valid) => {
        if (valid) {
            tagsManagement.save({
                id: form.value.id,
                tagName: form.value.tagName,
                tagTheme: form.value.tagTheme,
                tagRound: form.value.tagRound,
                tagColor: form.value.tagColor,
                tagRemark: form.value.tagRemark,
                org: form.value.org ? form.value.org : getOrganizationInfo()
            }).then((res) => {
                if (res.code === 200) {
                    proxy.msgSuccess("操作成功");
                    open.value = false;
                    selectList();
                }
            }).catch(() => {
                proxy.msgError("操作失败");
            });
        }
    })

}

//修改数据
function editInfo(data) {
	form.value.id = data.id;
	form.value.tagName = data.tagName;
	form.value.tagTheme = data.tagTheme;
	form.value.tagRound = data.tagRound;
	form.value.tagColor = data.tagColor;
	form.value.tagRemark = data.tagRemark;
    form.value.org = data.org;
	open.value = true;
}


//删除数据
function deleteInfo(data) {
	tagsManagement.delete({
		ids: data.id
	}).then((res) => {
		if (res.code === 200) {
			proxy.msgSuccess("删除成功");
			selectList();
		}
	});
}

//判断主题
function judgeTheme(type) {
	if (type === '1') {
		return {label: '深色', type: 'dark'};
	} else if (type === '2') {
		return {label: '浅色', type: 'light'};
	} else {
		return {label: '默认', type: 'plain'};
	}
}

//判断形状
function judgeShape(type) {
	if (type === '1') {
		return {label: '圆角', type: false};
	} else if (type === '2') {
		return {label: '椭圆', type: true};
	} else {
		return {label: '圆角', type: false};
	}
}

//判断颜色
function judgeColor(type) {
	if (type === '1') {
		return {label: '蓝色', type: ""};
	} else if (type === '2') {
		return {label: '绿色', type: 'success'};
	} else if (type === '3') {
		return {label: '灰色', type: 'info'};
	} else if (type === '4') {
		return {label: '红色', type: 'danger'};
	} else if (type === '5') {
		return {label: '橙色', type: 'warning'};
	} else {
		return {label: '蓝色', type: ""};
	}
}
</script>

<style lang="scss" scoped>
.query-form{
    height: 100%;
    width: 100%;
    ::v-deep .el-card__body{
        padding-bottom: 0;
    }
}
</style>
