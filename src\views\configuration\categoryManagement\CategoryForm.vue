<template>
    <el-form ref="formRef" v-loading="formLoading" :model="form" :rules="rules" label-width="auto" style="margin-top: 0; padding-right: 20px">
        <!-- 门类数据权限 -->
        <el-form-item label="门类数据权限" prop="openFlag">
            <el-radio-group v-model="form.openFlag" disabled>
                <el-radio :disabled="isSuperAdminFlag && item.value ==='1'" v-for="(item,index) in openFlagOptions" :key="index" :value="item.value">{{item.name}}</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-divider border-style="dashed" class="m-15" />
        <!-- 上级 -->
        <div class="form-flex">
            <el-form-item v-if="!showActions" label="上级" prop="parentName">
                <el-input v-model="form.parentName" :disabled="disabled" class="w-full" placeholder="请输入名称" />
            </el-form-item>

            <!-- 所属全宗 -->
            <el-form-item label="所属全宗" prop="recordGroupId">
                <el-select v-model="form.recordGroupId" :disabled="disabled" class="w-full" placeholder="请选择全宗ID">
                    <el-option v-for="item in recordGroupList" :key="item.id" :label="item.recordGroupName" :value="item.id" />
                </el-select>
            </el-form-item>

            <!-- 门类名称 -->
            <el-form-item label="门类名称" prop="name">
                <el-input v-model="form.name" :disabled="disabled" class="w-full" placeholder="请输入名称" />
            </el-form-item>

            <!-- 门类编号 -->
            <el-form-item label="门类编号" prop="num">
                <el-input v-model="form.num" :disabled="disabled" class="w-full" placeholder="请输入门类编号" />
            </el-form-item>

            <!-- 门类整理方式 -->
            <el-form-item label="门类整理方式" prop="recordCategoryType">
                <el-input v-model="form.recordCategoryType" :disabled="disabled" class="w-full" placeholder="请输入门类整理方式" />
            </el-form-item>

            <!-- 排序 -->
            <el-form-item label="排序" prop="sort">
                <el-input v-model="form.sort" :disabled="disabled" class="w-full" placeholder="请输入排序号" />
            </el-form-item>
            <!-- 是否对外开放 -->
            <el-form-item v-if="isTianTunFlag" label="是否对外开放" prop="isOpen">
                <el-switch v-model="form.isOpen" :disabled="disabled" active-color="#13ce66" inactive-color="#ff4949" />
            </el-form-item>
        </div>
        <el-divider border-style="dashed" class="m-15" />
        <div class="form-flex">
            <el-form-item label="保留年限:" prop="retentionPeriod">
                <el-select v-model="form.retentionPeriod" :disabled="disabled" class="w-full" clearable placeholder="请选择保留年限">
                    <el-option label="永久" value="Y" />
                    <el-option label="5年" value="D5" />
                    <el-option label="10年" value="D10" />
                    <el-option label="20年" value="D20" />
                    <el-option label="30年" value="D30" />
                </el-select>
            </el-form-item>
            <el-form-item label="保密密级:" prop="protectLevel">
                <el-select v-model="form.protectLevel" :disabled="disabled" class="w-full" clearable placeholder="请选择保密密级">
                    <el-option label="公开" value="GK" />
                    <el-option label="限制" value="KZ" />
                    <el-option label="秘密" value="MOM" />
                    <el-option label="机密" value="JM" />
                    <el-option label="绝密" value="UM" />
                </el-select>
            </el-form-item>
            <el-form-item label="控制等级:" prop="controlStatus">
                <el-select v-model="form.controlStatus" :disabled="disabled" class="w-full" clearable placeholder="请选择控制等级" @change="controlGroupChange">
                    <el-option :disabled="form.openFlag !== '1'" label="公开" value="1" />
                    <el-option :disabled="form.openFlag === '1'" label="公司内部开放" value="2" />
                    <el-option :disabled="form.openFlag === '1'" label="部门内部开放" value="3" />
                    <el-option :disabled="form.openFlag === '1' || !form?.org?.id" label="控制" value="4">
                        <el-text v-if="form?.org?.id">控制</el-text>
                        <el-tooltip v-else content="请先选择组织机构" effect="dark" placement="right">
                            <el-text type="info">控制</el-text>
                        </el-tooltip>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="form.openFlag !== '1'" label="所属机构:" prop="org.id">
                <el-select v-model="form.org.id" :disabled="disabled" class="w-full" clearable placeholder="请选择所属机构" @change="departmentList">
                    <el-option v-for="item in institution" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item v-if="form.controlStatus === '4'" label="可查看人员:" prop="ruleUserId">
                <el-select v-model="form.ruleUserId" :disabled="disabled" class="w-full" clearable collapse-tags collapse-tags-tooltip multiple placeholder="请选择可查看人员">
                    <el-option v-for="item in userByOrg" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item v-if="form.openFlag !== '1'" label="所属部门:" prop="office.id">
                <el-select v-model="form.office.id" :disabled="disabled" class="w-full" clearable placeholder="请选择所属部门">
                    <el-option v-for="item in department" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否全电文档:" prop="isElectronic">
                <el-select v-model="form.isElectronic" :disabled="disabled" class="w-full" clearable placeholder="请选择是否全电文档">
                    <el-option label="否" value="0" />
                    <el-option label="是" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="档案标签:" prop="tagInfo" style="margin-bottom: 0;">
                <tagSelection :selected-tag-str="form.tagInfo" :show-button="!disabled" @update:selected-tag-str="returnToTheSelectionTabs" />
            </el-form-item>
        </div>
        <el-divider border-style="dashed" class="m-15" />

        <!-- 数据结构 -->
        <el-form-item label="数据结构" prop="dataPlatform">
            <el-tree-select v-model="form.dataPlatform" :data="platformList" :disabled="disabled" :props="{ value: 'id', label: 'name' }" check-strictly class="w-full" clearable highlight-current placeholder="请选择数据结构" @change="dataPlatformChange" />
        </el-form-item>
        <!-- 筛选字段 -->
        <el-form-item label="筛选字段" prop="tableField">
            <div v-if="showActions" class="table-actions">
                <el-button type="primary" @click="addTableField">添加</el-button>
                <el-button type="danger" @click="deleteTableFields">删除</el-button>
            </div>
            <el-table :data="tableFields" border @selection-change="handleTableSelect">
                <el-table-column v-if="!disabled" type="selection" width="55" />
                <el-table-column align="center" label="字段名称" prop="tableField">
                    <template #default="scope">
                        <el-select v-model="scope.row.tableField" :disabled="disabled" placeholder="请选择字段名称" @change="handleFieldChange(scope.row, scope.$index)">
                            <el-option v-for="item in fieldOptions" :key="item.id" :disabled="isFieldUsed(item.id)" :label="item.remark" :value="item.id" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="字段类型" prop="type">
                    <template #default="scope">
                        <el-select v-model="scope.row.type" :disabled="disabled" @change="handleParamChange(scope.row)">
                            <el-option label="文本" value="text" />
                            <el-option label="数字" value="number" />
                            <el-option label="时间" value="date" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="查询参数" prop="param">
                    <template #default="scope">
                        <el-select v-model="scope.row.param" :disabled="disabled">
                            <template v-if="scope.row.type === 'text'">
                                <el-option label="无" value="无" />
                            </template>
                            <template v-else-if="scope.row.type === 'number'">
                                <el-option label="大于" value=">" />
                                <el-option label="等于" value="=" />
                                <el-option label="小于" value="<" />
                                <el-option label="大于等于" value=">=" />
                                <el-option label="小于等于" value="<=" />
                            </template>
                            <template v-else-if="scope.row.type === 'date'">
                                <el-option label="精确时间" value="1" />
                                <el-option label="范围时间" value="2" />
                            </template>
                        </el-select>
                    </template>
                </el-table-column>
            </el-table>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { ref, defineProps, defineEmits, defineExpose, onBeforeMount, getCurrentInstance } from 'vue';
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';
import dataManager from '@/api/archive/dataManager';
import sysOfficeService from '@/api/model/sys/sysOfficeService';
import sysOrgService from '@/api/model/sys/sysOrgService';
import { node } from '@/api/model/systemDeploy/auditAndFlow';

import category from '@/api/archive/categoryManagement/category';
import tagSelection from '@/components/tagSelection/index.vue';
import { isTianTun, isSuperAdmin, getOrganizationInfo } from '@/utils/permission';

const { proxy } = getCurrentInstance();
const props = defineProps({
    // 表单数据
    info: {
        type: Object,
        required: true
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 是否显示操作按钮
    showActions: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['submit-success', 'submit-error']);

const formRef = ref();
const form = ref({
    parentName: '',
    recordGroupId: '',
    name: '',
    num: '',
    recordCategoryType: '',
    sort: '',
    dataPlatform: '',
    isOpen: false,
    openFlag: '1',
    retentionPeriod: '',
    protectLevel: '',
    controlStatus: '',
    ruleUserId: [],
    org: { id: '' },
    office: { id: '' },
    isElectronic: '',
    tagInfo: ''
});
const formLoading = ref(false);
// 全宗列表
const recordGroupList = ref([]);
// 平台列表 -数据结构
const platformList = ref([]);
const fieldOptions = ref([]); // 字段选项
// 所属机构
const institution = ref([]);
const department = ref([]); // 部门列表
// 用户列表
const userByOrg = ref([]);
const tableFields = ref([]); // 表格字段
const selectedTableFields = ref([]); // 已选择的表格字段
const isTianTunFlag = ref(isTianTun());
const isSuperAdminFlag = ref(isSuperAdmin());
const openFlagOptions = ref([]);

// 表单验证规则
const rules = {
    recordGroupId: [
        {
            required: true,
            message: '全宗ID不能为空',
            trigger: 'blur'
        }
    ],
    name: [
        {
            required: true,
            message: '名称不能为空',
            trigger: 'blur'
        }
    ],
    num: [
        {
            required: true,
            message: '编号不能为空',
            trigger: 'blur'
        }
    ],
    recordCategoryType: [
        {
            required: true,
            message: '整理方式不能为空',
            trigger: 'blur'
        }
    ],
    sort: [
        {
            required: true,
            message: '排序不能为空',
            trigger: 'blur'
        },
        {
            pattern: /^[0-9]*$/,
            message: '只能为数字',
            trigger: 'blur'
        }
    ]
};

onBeforeMount(async() => {
    openFlagOptions.value = await proxy.getDictList("data_permission_open_flag");
    getGroupList(); // 获取全宗列表
    getPlatformList(); // 获取数据结构
    institutionList(); // 查询机构信息
});
//指定档案可查看人员
function controlGroupChange(chooseValue) {
    if (chooseValue === '4') {
        node.staff({ 'sysOrg.id': form.value?.org?.id }).then((res) => {
            userByOrg.value = res.data.records;
        });
    }
}

// 表格选择变更
const handleTableSelect = (selection) => {
    selectedTableFields.value = selection;
};

// 表格字段变更
const handleFieldChange = (row) => {
    const selectedField = fieldOptions.value.find((f) => f.id === row.tableField);
    if (!selectedField) return;
    // 根据字段类型设置默认值
    switch (selectedField.type.toString()) {
        case '1':
            row.type = 'text';
            break;
        case '4':
            row.type = 'date';
            break;
        default:
            row.type = 'text'; // 默认类型
    }
    handleParamChange(row);
};

// 字段类型参数变更
const handleParamChange = (row) => {
    // emit("param-change", row);
    // 根据字段类型设置默认值
    switch (row.type.toString()) {
        case 'number':
            row.param = '=';
            break;
        case 'date':
            row.param = '2';
            break;
        default:
            row.param = '无'; // 默认类型
    }
};

// 添加表格字段
const addTableField = () => {
    const newField = {
        tableField: `列名称${tableFields.value.length + 1}`,
        type: '选择字段类型',
        param: ''
    };
    tableFields.value.push(newField);
};

// 删除表格字段
const deleteTableFields = () => {
    selectedTableFields.value.forEach((item) => {
        const index = tableFields.value.indexOf(item);
        tableFields.value.splice(index, 1);
    });
};
// 检查字段是否已被使用
const isFieldUsed = (fieldId) => {
    return tableFields.value.some((field) => field.tableField === fieldId);
};

// 表单验证
const validate = () => {
    return formRef.value?.validate();
};

// 重置表单
const resetForm = () => {
    formRef.value?.resetFields();
};

defineExpose({
    loadDetail,
    handleSubmit
});
async function loadDetail() {
    resetForm(); // 重置表单
    if (props.info?.id) {
        formLoading.value = true;
        try {
            let res = await category.queryById({
                id: props.info?.id
            });
            if (res.code === 200) {
                const ruleUserId = res?.data?.ruleUserId;
                form.value = {
                    parentName: res?.data?.parent?.name ? res?.data?.parent?.name : res?.data?.name,
                    name: res?.data?.name,
                    num: res?.data?.num,
                    recordGroupId: res?.data?.recordGroupId,
                    recordCategoryType: res?.data?.recordCategoryType,
                    sort: res?.data?.sort,
                    parentId: res?.data?.parent?.id,
                    id: res?.data?.id,
                    dataPlatform: res?.data?.dataPlatform,
                    isOpen: res?.data?.isOpen === '1',
                    openFlag: res?.data?.openFlag || '2',
                    retentionPeriod: res?.data?.retentionPeriod,
                    protectLevel: res?.data?.protectLevel,
                    controlStatus: res?.data?.controlStatus,
                    ruleUserId: ruleUserId ? ruleUserId.split(',') : [],
                    org: res?.data?.org || { id: '' },
                    office: res?.data?.office || { id: '' },
                    isElectronic: res?.data?.isElectronic,
                    tagInfo: res?.data?.tagInfo
                };
                departmentList();
                controlGroupChange(form.value?.controlStatus);
                const re = await dataManager.versionList({
                    platformId: res?.data?.dataPlatform,
                    current: 1,
                    size: -1
                });
                const reArry = re.data.records.map((item) => ({
                    structureId: item?.structureId
                }));
                // 获取字段数据
                const ress = await dataManager.getFieldsByPlatform({
                    configId: reArry[0]?.structureId
                });
                if (ress.code === 200) {
                    fieldOptions.value = ress.data.map((item) => ({
                        id: item.id,
                        name: item.name,
                        type: item.type,
                        remark: item.remark || '无备注',
                        length: item.length,
                        dataConfigValue: item.dataConfigValue
                    }));
                }
                tableFields.value = JSON.parse(res?.data?.tableField || '[]').map((field) => ({
                    tableField: field.id,
                    param: field.param,
                    type: field.type
                }));
            }
        } finally {
            formLoading.value = false;
        }
    } else {
        proxy.msgError('查询失败');
    }
}

/**
 * 返回标签
 * @param data
 */
function returnToTheSelectionTabs(data) {
    form.value.tagInfo = data;
}

/**
 * @: 查询全宗ID
 * @return {*}
 */
function getGroupList() {
    completeManagement
        .getList({
            current: 1,
            size: -1
        })
        .then((res) => {
            if (res.code === 200) {
                recordGroupList.value = res.data.records;
            }
        });
}
/**
 * @: 查询数据结构
 * @return {*}
 */
function getPlatformList() {
    dataManager.classifyList().then((res) => {
        platformList.value = res.data;
    });
}
//数据分类切换
const dataPlatformChange = async (platformId) => {
    // 保留原有的版本列表请求（如果仍需要）
    const re = await dataManager.versionList({
        platformId: platformId,
        current: 1,
        size: -1
    });
    const reArry = re.data.records.map((item) => ({
        structureId: item?.structureId
    }));
    try {
        // 清空已选字段
        form.value.tableField = '';
        // 获取字段数据
        const res = await dataManager.getFieldsByPlatform({
            configId: reArry[0]?.structureId
        });
        if (res.code === 200) {
            fieldOptions.value = res.data.map((item) => ({
                id: item.id,
                name: item.name,
                type: item.type,
                remark: item.remark || '无备注',
                length: item.length,
                dataConfigValue: item.dataConfigValue
            }));
        } else {
            proxy.msgError('字段加载失败');
            fieldOptions.value = [];
        }
    } catch (error) {
        fieldOptions.value = [];
    }
};

// 提交表单
async function handleSubmit() {
    try {
        // 表单验证
        const valid = await validate();
        if (!valid) {
            emit('submit-error');
            return false;
        }
        const tableField = JSON.stringify(
            tableFields.value.map((field) => {
                const fieldInfo = fieldOptions.value.find((option) => option.id === field.tableField);
                return {
                    type: field.type,
                    param: field.param,
                    id: field.tableField,
                    remark: fieldInfo ? fieldInfo?.remark : '',
                    name: fieldInfo ? fieldInfo?.name : ''
                };
            })
        );
        // 构建字段数据
        let newMenuData = {
            //父级ID
            parent: {
                id: form.value.parentId
            },
            //  id
            id: form.value?.id,
            //  档案门类编号
            num: form.value?.num,
            //  名称
            name: form.value?.name,
            //  全宗ID
            recordGroupId: form.value?.recordGroupId,
            //  整理方式
            recordCategoryType: form.value?.recordCategoryType,
            //  排序
            sort: form.value?.sort,
            //数据结构
            dataPlatform: form.value?.dataPlatform,
            tableField: tableField, // 字段数据
            isOpen: form.value.isOpen ? '1' : '0', // 是否开放
            openFlag: form.value?.openFlag || '2', // 开放范围
            retentionPeriod: form.value?.retentionPeriod, // 保管期限
            protectLevel: form.value?.protectLevel, // 保密等级
            controlStatus: form.value?.controlStatus, // 控制状态
            ruleUserId: form.value?.ruleUserId.join(','), // 规则用户ID
            org: form.value?.org || getOrganizationInfo(), // 组织机构
            office: form.value?.office || { id: '' }, // 部门
            isElectronic: form.value?.isElectronic, // 是否电子
            tagInfo: form.value?.tagInfo // 标签
        };
        category
            .save(newMenuData)
            .then((res) => {
                if (res.code === 200) {
                    proxy.msgSuccess('修改成功');
                    emit('submit-success');
                }
            })
            .catch(() => {
                proxy.msgError('修改失败');
                emit('submit-error');
            });
    } catch (error) {
        console.error('操作失败:');
        emit('submit-error');
    } finally {
        // submitLoading.value = false;
    }
}

// 查询机构信息
function institutionList() {
    sysOrgService
        .list({
            current: 1,
            size: -1
        })
        .then((res) => {
            if (res.code === 200) {
                institution.value = res.data.records;
                form.value = {
                    ...form.value,
                    org: {
                        id: ''
                    }
                };
            }
        })
        .catch(() => {
            proxy.msgError('查询失败');
        });
}

// 根据组织机构查询部门
function departmentList(val) {
    sysOfficeService
        .list({
            current: 1,
            size: -1,
            'sysOrg.id': form.value?.org?.id,
            'pparent.id': 0
        })
        .then((res) => {
            if (res.code === 200) {
                department.value = res.data.records;
                if(val){
                    form.value = {
                        ...form.value,
                        office: {
                            id: ''
                        }
                    };
                }
            }
        })
        .catch(() => {
            proxy.msgError('查询失败');
        });
}
</script>

<style scoped>

.table-actions {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
}
.m-15 {
    margin: 15px 0;
}
.form-flex {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}
::v-deep .form-flex .el-form-item {
    width: 43%;
}
::v-deep .el-form-item{
    margin-bottom: 15px;
}
::v-deep .form-flex .el-form-item .el-form-item__content{
    align-items:self-start;
}
::v-deep .el-select-dropdown .el-select-dropdown__item.is-disabled .el-text{
    color: var(--el-text-color-placeholder) !important;
}
</style>
