
<template>
    <el-main style="padding-top: 0;">
        <el-container>
            <div style="height: 550px;">
                <div style="padding: 10px; display: flex;justify-content: space-between;">
					<div v-if="counterTypeVal == '2'" style="display: flex;">
						<div style="text-align: center; line-height: 35px; padding-right: 15px;text-wrap: nowrap;">柜面选择:</div>
						<el-select v-model="counterType" style="width: 100px;">
							<el-option label="A面" value="1"/>
							<el-option label="B面" value="2"/>
						</el-select>
						<el-button style="margin-left: 20px;" type="primary" @click="() => determine()">确定</el-button>
					</div>
					<div v-if="counterTypeVal == '1'"></div>
				</div>
				<div v-if="counterTypeVal" style="margin-left: 1%;width: fit-content;">
					<div style="display: flex;margin-bottom: 15px;margin-left: 80px;">
						<template v-if="containerInfo && containerInfo.containerPitchStart === '1'">
							<div v-for="(_item, index) in containerList[0]" :key="index" class="joint">
								{{ '第' + (index + 1) + '节' }}
							</div>
						</template>
						<template v-if="containerInfo && containerInfo.containerPitchStart === '2'">
							<div v-for="(_item, index) in containerList[0]" :key="index" class="joint">
								{{ '第' + (containerInfo.containerFloors - index) + '节' }}
							</div>
						</template>
					</div>
					<div v-for="(floorItem, index1) in containerList" :key="index1"
						 style="display: flex;margin-bottom: 10px;">
						<div v-if="containerInfo && containerInfo.containerFloorsStart === '1'"
							 class="storey">
							{{ '第' + (index1 + 1) + '层' }}
						</div>
						<div v-if="containerInfo && containerInfo.containerFloorsStart === '2'"
							 class="storey">
							{{ '第' + (containerInfo.containerFloors - index1) + '层' }}
						</div>
						<el-popover v-for="(pitchItem, index) in floorItem" :key="index" :width="120"
									placement="top-start" trigger="hover">
							<template #reference>
								<div :class="{
                                    selected: pitchItem.selected,
                                    notFull: pitchItem.storageFileCount != 0 && (pitchItem.storageFileMax > pitchItem.storageFileCount),
                                    full: pitchItem.storageFileCount != 0 && (pitchItem.storageFileMax == pitchItem.storageFileCount)
                                }" class="lattice" @click="viewJoint(pitchItem)"></div>
							</template>
							<div style="font-size: 12px;">
                                <div>额定容量：{{ pitchItem.storageFileMax }} 盒</div>
                                <div>剩余容量：{{ pitchItem.storageFileMax - pitchItem.storageFileCount }} 盒</div>
								<div v-if="pitchItem.storageGroup">
									所属全宗: {{ pitchItem.storageGroup.recordGroupName }}
								</div>
								<div v-if="pitchItem.storageCategory">
									所属门类: {{ pitchItem.storageCategory.name }}
								</div>
                            </div>
                        </el-popover>
                    </div>
                </div>
            </div>
        </el-container>
    </el-main>
    <el-main>
        <el-container style="float: right;">
            <el-main style="padding: 0;">
                <div style="float: right;">
                    <el-button plain @click="() => cancellation()">取消</el-button>
                    <el-button type="primary" @click="() => collectRemove()">确定</el-button>
                </div>
            </el-main>
        </el-container>
    </el-main>
    <el-dialog v-if="open" v-model="open" append-to-body title="详情" width="1000px">
        <el-table :data="tableData" :load="load" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" height="650" lazy row-key="id"
            style="width: 100%">
            <el-table-column label="架位名称" prop="recordStorageName">
            </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
// 查询全宗和门类
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';
import category from '@/api/archive/categoryManagement/category';
import frameList from '@/api/archive/storeroom/upDownFrame';
import {getOrganizationInfo} from "@/utils/permission";

export default {
    name: 'shelves',
    props: ['latticeView', 'receiveId'],
    data() {
        return {
            // 节
            pitch: "",
            // 层
            floor: '',
            // 选择AB面storageSave
            counterType: '1',
            openJoint: false,
            form: {},
            fourConfig: {},
            queryParams: {
                current: 1,
                size: 10
            },
            total: 0,
            open: false,
            // 详情data
            tableData: [],
            selectedIds: [],//选中格子的id
            load: false,

            // 档案柜列表
            containerList: [],
            // 档案柜信息
            containerInfo: {},
            // 点击档案架是否是多选
            containerType: '1',
            counterTypeVal: '',
        }
    },
    mounted() {
		this.containerById(this.latticeView);
		this.getById();
    },
    methods: {
        // 取消弹窗
        cancellation() {
            this.$emit('childMove');
        },
        // 确定上架
        collectRemove() {
            var ids = this.selectedIds.map((v) => v);
            var idStr = ids.join(",");
            var idTow = this.receiveId.map((v) => v.id);
            var idTowStr = idTow.join(",");
            console.log(idTowStr);
            frameList.boxMove({
				storageId: idStr,
				controlType: '1',
				ids: idTowStr,
			}).then(res => {
                if (res.code === 200) {
                    this.cancellation();
                    this.$message.success("移库成功");
                }
            }).catch(() => {
                this.$Response.errorNotice("移库失败");
            });
        },
        // 展示档案柜
        async containerById(id) {
			storeroomData.getStoragesByContainerId({
				containerId: id,
				counterType: this.counterType
			}).then(res => {
				if (res.code === 200) {
					this.containerList = res.data;
				}
			}).catch(error => {
				this.$Response.errorNotice(error, "查询失败");
			})
        },
        // 查询门类
        dataInfo() {
            category.getCategorySelect({ orgId: getOrganizationInfo()?.id,openFlag:true}).then(res => {
                if (res.code === 200) {
                    this.fourConfig.category = res.data || [];
                }
            }).catch(() => {
                this.$Response.errorNotice("查询失败");
            })
        },
        // 确定查询面
        determine() {
            this.containerById(this.latticeView);
        },
		getById() {
			storeroomData.containerById({
				id: this.latticeView
			}).then(res => {
				if (res.code === 200) {
					this.counterTypeVal = res.data.containerCounterType;
					this.containerInfo = res.data;
				}
			}).catch(err => {
				this.$Response.errorNotice(err, "查询失败");
			})
		},
        // 查询全宗
        getManagement() {
            completeManagement.getGroupSelect({ orgId: getOrganizationInfo()?.id,openFlag:true}).then(res => {
                if (res.code === 200) {
                    this.fourConfig.group = res.data.records;
                }
            }).catch(() => {
                this.$Response.errorNotice("查询失败");
            });
        },
        // 档案架设置
        storageAdd() {
            this.getManagement();
            this.dataInfo();
            this.openJoint = true;
        },
        viewJoint(pitch) {
			if (this.selectedIds.length < 1) {
				pitch.selected = !pitch.selected;
				if (pitch.selected) {
					this.selectedIds.push(pitch.id);
				} else {
					const index = this.selectedIds.indexOf(pitch.id);
					if (index !== -1) {
						this.selectedIds.splice(index, 1);
					}
				}
			} else if (this.selectedIds.includes(pitch.id)) {
				pitch.selected = !pitch.selected;
				const index = this.selectedIds.indexOf(pitch.id);
				if (index !== -1) {
					this.selectedIds.splice(index, 1);
				}
			} else {
				this.$Response.errorNotice('', "上架时只能选择一个架位!");
			}
		},
	},
    emits: ['childMove']
}
</script>
<style>
.selected {
    background-color: #ccc;
}

.notFull {
    background-color: #81ee66;
}

.full {
    background-color: #db1b1b;
}

.lattice {
	width: 80px;
	height: 110px;
	border: 1px solid #368cca;
	cursor: pointer;
	margin-left: 20px;
	border-radius: 3px;
}

/* .no-left-border {
    border-left: none;
} */

.joint {
	width: 80px;
	text-align: center;
	font-size: 14px;
	font-weight: 600;
	margin-left: 20px;
}

.storey {
	width: 80px;
	margin-top: 0.25rem;
	font-size: 14px;
	font-weight: 600;
	text-align: center;
}
</style>
