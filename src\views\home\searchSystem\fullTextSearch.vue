<template>
	<el-container style="padding: 15px;background-color: #FFFFFF;box-shadow: var(--el-box-shadow-light);
		border-radius: 3px;">
		<el-aside width="22%">
			<div style="height: 100%;
						width: 100%;
						display: flex;
						flex-flow: wrap;
						flex-direction: column;
						flex-wrap: nowrap;
						align-items: stretch;
						justify-content: space-between;
						padding: 10px 15px 0 10px;">
				<el-form ref="formList" :model="form" label-position="left">
					<el-form-item label="档案年份:" prop="year">
						<el-date-picker v-model="form.year" format="YYYY" placeholder="请选择档案年份"
										style="width: 100%" type="year" value-format="YYYY" @change="handleQuery"/>
					</el-form-item>
					<el-form-item label="档案全宗:" prop="boxGroupId">
						<el-select v-model="form.boxGroupId" placeholder="请选择全宗" @change="handleQuery">
							<el-option v-for="item in fourConfig.group" :key="item.id"
                                       :label="item.recordGroupName" :value="item.id">
                                <div class="el-flex-row-between">
                                    <span>{{ item.recordGroupName }}</span>
                                    <el-tag :type=" item.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,item.openFlag) }}</el-tag>
                                </div>
                            </el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="档案门类:" prop="boxCategoryId">
						<el-tree-select v-model="form.boxCategoryId" :data="fourConfig.category"
										:props="{ value: 'id', label: 'name' }"
										check-strictly highlight-current
										placeholder="请选择门类" @change="handleQuery">
							<template #default="{ data }">
                                <div class="el-flex-row-between">
                                    <span>{{ data.name }}</span>
                                    <el-tag :type=" data.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,data.openFlag) }}</el-tag>
                                </div>
							</template>
						</el-tree-select>
					</el-form-item>
					<el-form-item label="档案标签:" prop="tagInfo">
						<el-button plain type="primary" @click="openTagsChose = true">选择标签</el-button>
						<tagsListDialog v-if="openTagsChose" :choseTagsId="form.tagInfo" :dialogIsOpen="openTagsChose"
										@choseTagMethodReturn="choseTagMethodReturn"/>
					</el-form-item>

					 <!-- 新增字段选择表单项 -->
					<el-form-item label="查询字段:" prop="searchFields" v-if="categoryFields.length > 0">
						<el-select
						v-model="form.searchFields"
						multiple
						placeholder="请选择查询字段"
						@change="handleQuery"
						>
						<el-option
							v-for="field in categoryFields"
							:key="field"
							:label="field"
							:value="field"
						/>
						</el-select>
					</el-form-item>

				</el-form>
				<div style="width: 100%;display: flex;justify-content: flex-end;">
					<el-button icon="RefreshRight" plain @click="resetQuery">
						重置
					</el-button>
				</div>
			</div>
		</el-aside>
		<el-main style="padding: 0 0 0 10px">
			<el-container>
				<el-main style="padding: 0">
					<div style="height: 100%;width: 100%;overflow-y: scroll;padding-right: 10px">
						<div v-for="(item, index) in searchHits" :key="index" class="retrieval_result infoBox">
							<div style="padding: 10px;">
								<div style="display: flex;flex-direction: row;flex-wrap: nowrap;align-content: center;
										justify-content: flex-start;align-items: center;">
									<div style="line-height: 26px;font-size: 16px;font-weight: bold;"
										 v-html="item.content.name"></div>
									<div v-if="item.content.tagInfo">
										<el-tag v-for="tagInfo in choseTageInfo(item.content.tagInfo)"
												:effect="judgeTheme(tagInfo.tagTheme).type"
												:round="judgeShape(tagInfo.tagRound).type"
												:type="judgeColor(tagInfo.tagColor).type"
												auto-close="300"
												style="margin-left: 10px"
										>
											{{ tagInfo.tagName }}
										</el-tag>
									</div>
								</div>
								<div style="line-height: 26px;font-size: 12px;"
									 v-html="item.content.digestContent ? item.content.digestContent : '暂无档案摘要'"></div>
								<div style="display: flex;flex-direction: row;flex-wrap: nowrap;
										align-content: center;justify-content: flex-start;align-items: center;gap: 15px">
									<div style="line-height: 26px;font-size: 12px;">档号:
										{{ item.content.num ? item.content.num : "暂无" }}
									</div>
									<div style="line-height: 26px;font-size: 12px;">档案存址:
										{{ item.content.storageAddress ? item.content.storageAddress : "暂无" }}
									</div>
									<div style="line-height: 26px;font-size: 12px;">档案版本:
										{{ item.content.version ? item.content.version : "暂无" }}
									</div>
								</div>
							</div>
							<div style="width: 16%;display: flex;flex-direction: row;flex-wrap: nowrap;
									align-content: center;justify-content: space-around;align-items: center;">
								<div style="display: flex;align-items: center;margin-right: 12px">
									<el-button icon="Edit" link style="font-size: 25px;" type="warning"
											   @click="borrowEvent(item)"/>
									<el-button icon="ShoppingTrolley" link style="font-size: 25px;" type="primary"
											   @click="filesJoin(item)"/>
									<el-button round style="margin-right: 15px" type="primary"
											   @click="collectView(item)">
										详情
									</el-button>
								</div>
							</div>
						</div>
					</div>
				</el-main>
				<el-footer style="padding: 20px 10px 0 10px">
					<div style="display: flex;justify-content: flex-end">
						<pagination v-model:limit="queryParam.size" v-model:page="queryParam.current" :total="total"
									style="padding: 0" @pagination="handleQuery"/>
					</div>
				</el-footer>
			</el-container>
		</el-main>

		<!-- 查看 -->
		<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
			<viewFiles :receiveId="receiveId" @childMove="parentView"/>
		</el-dialog>
		<!-- 借阅 -->
		<el-dialog v-if="openBorrow" v-model="openBorrow" :title="title" append-to-body width="1300px">
			<Borrowing :handList="handList" @childMove="parentMove"/>
		</el-dialog>
	</el-container>
</template>

<script setup>
//档案标签选择弹窗
import tagsListDialog from "@/views/archiveReception/common/tagsListDialog.vue";
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from "vue";
import retrievalList from "@/api/archive/retrieval";
import view from "@/api/archive/managementFile";
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';
import category from "@/api/archive/categoryManagement/category";
import viewFiles from '@/views/archiveReception/view.vue';
import Borrowing from "@/views/retrieval/archivalRetrieval/borrowing.vue";
import tagsManagement from "@/api/archive/tagsManagement";
import tool from "@/utils/tool";
import {getOrganizationInfo} from "@/utils/permission";

const {proxy} = getCurrentInstance();
const props = defineProps({
	searchString: {
		type: String,
		default: ''
	}
});
const emit = defineEmits(["returnBack", "shakeStatusChange"]);
//头部查询
const form = ref({
	year: null,
	boxGroupId: null,
	boxCategoryId: null,
	tagInfo: null
});
const data = reactive({
	categoryFields: [], // 存储查询字段
	queryParam: {
		current: 1,
		size: 10,
	},
	queryParams: {
		current: 1,
		size: -1,
	},
	fourConfig: {},
});
const {queryParams, queryParam, fourConfig} = toRefs(data);
const total = ref(0);
const title = ref('');
const receiveId = ref({});
const handList = ref([]);
const searchHits = ref([]);
//标签信息集合
const tagsList = ref([]);
const formList = ref(null);
const openView = ref(false);
const openBorrow = ref(false);
const openTagsChose = ref(false);
const openFlagOptions = ref([]);

defineExpose({
	handleQuery,
	resetQuery
});

onMounted(async () => {
    openFlagOptions.value = await proxy.getDictList("data_permission_open_flag");
	getManagement();
	dataInfo();
	getTagsList();
	handleQuery();
});

// 重置
function resetQuery() {
	formList.value.resetFields();
	handleQuery();
}

function handleQuery() {
	// 当门类变化时查询字段
  if (form.value.boxCategoryId && !data.categoryFields.length) {

  }
  console.log("fjeiwofewfewfewfewfefewffwefewfe");
 category.getFieldsByCategory({
    id: form.value.boxCategoryId
  }).then(res => {
    if (res.code === 200) {
      data.categoryFields = res.data;
    }
  }).catch(err => {
    console.error('字段获取失败:', err);
    data.categoryFields = [];
  });
	retrievalList.listByIndex({
		tagManagerInfo: form.value.tagInfo,
		queryString: props.searchString ? props.searchString : null,
		current: queryParam.value.current,
		size: queryParam.value.size,
		year: form.value.year,
		'controlGroup.id': form.value.boxGroupId,
		'controlCategory.id': form.value.boxCategoryId
	}).then(res => {
		if (res.code === 200) {
			searchHits.value = res.data.searchHits;
			searchHits.value.forEach(item => {
				item.content.tagManagerInfo = tagsList.value.filter(tag => tag.id === item.content.tagInfo)[0];
			});
			total.value = res.data.totalHits;
		}
	});
}

// 新增获取字段方法
function getCategoryFields() {
  category.getFieldsByCategory({
    id: form.value.boxCategoryId
  }).then(res => {
    if (res.code === 200) {
      data.categoryFields = res.data;
    }
  }).catch(err => {
    console.error('字段获取失败:', err);
    data.categoryFields = [];
  });
}



//标签选择回调方法
function choseTagMethodReturn(data) {
	if (data.length > 0) {
		form.value.tagInfo = data.join(' ');
		openTagsChose.value = false;
		handleQuery();
	} else {
		form.value.tagInfo = null;
		openTagsChose.value = false;
		handleQuery();
	}
}

// 获取档案标签数据
function getTagsList() {
	tagsManagement.getList({
		current: 1,
		size: -1
	}).then(res => {
		if (res.code === 200) {
			tagsList.value = res.data.records;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

//查询标签数据集合
function choseTageInfo(choseTagsId) {
	let split = choseTagsId.split(",");
	return tagsList.value.filter(record => {
		return split.includes(record.id);
	});
}

// 查询全宗
function getManagement() {
    completeManagement.getGroupSelect({ orgId: getOrganizationInfo()?.id,openFlag:true}).then(res => {
		if (res.code === 200) {
			fourConfig.value.group = res.data || [];
		}
	});
}

// 查询门类
function dataInfo() {
    category.getCategorySelect({ orgId: getOrganizationInfo()?.id,openFlag:true}).then(res => {
		if (res.code === 200) {
			fourConfig.value.category = res.data;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

//判断主题
function judgeTheme(type) {
	if (type === '1') {
		return {label: '深色', type: 'dark'};
	} else if (type === '2') {
		return {label: '浅色', type: 'light'};
	} else {
		return {label: '默认', type: 'plain'};
	}
}

//判断形状
function judgeShape(type) {
	if (type === '1') {
		return {label: '圆角', type: false};
	} else if (type === '2') {
		return {label: '椭圆', type: true};
	} else {
		return {label: '圆角', type: false};
	}
}

//判断颜色
function judgeColor(type) {
	if (type === '1') {
		return {label: '蓝色', type: ""};
	} else if (type === '2') {
		return {label: '绿色', type: 'success'};
	} else if (type === '3') {
		return {label: '灰色', type: 'info'};
	} else if (type === '4') {
		return {label: '红色', type: 'danger'};
	} else if (type === '5') {
		return {label: '橙色', type: 'warning'};
	} else {
		return {label: '蓝色', type: ""};
	}
}

// 借阅
function borrowEvent(val) {
	handList.value = val;
	title.value = '借阅';
	openBorrow.value = true;
}

// 档案加入
function filesJoin(val) {
	retrievalList.carList({
		'info.id': val.content.id
	}).then(res => {
		if (res.code === 200) {
			if (res.data.records.length > 0) {
				proxy.msgError('该档案已加入');
			} else {
				borrowSave(val.content.id);
				proxy.msgSuccess('添加成功');
			}
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	});
}

function borrowSave(val) {
	retrievalList.save({
		info: {
			id: val
		},
		person: {
			id: tool.data.get('USER_INFO').id,
		}
	}).then(res => {
		if (res.code === 200) {
			emit("shakeStatusChange", "");
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

function collectView(val) {
	view.queryById({
		id: val.content.id,
		showDeleteInfo: true
	}).then(res => {
		if (res.code === 200) {
			receiveId.value = res.data;
			title.value = '查看';
			openView.value = true;
		}
	}).catch(() => {
		proxy.msgError('查看失败');
	})
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 取消借阅
function parentMove() {
	openBorrow.value = false;
}
</script>

<style scoped>
.retrieval_result {
	width: 100%;
	border-radius: 10px;
	border: 1px solid #dfe2e8;
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.infoBox:hover {
	box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.12);
}
</style>
