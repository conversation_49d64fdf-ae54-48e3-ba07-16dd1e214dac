<template>
	<el-container>
        <DragLayout type="row">
            <template #first>
                <el-aside  class="h-full w-full p-el">
                    <el-card body-class="h-full w-full p-0" class="h-full w-full">
                        <dataClassificationTree :default-expand-all="true"  @clickNode="menuClick"/>
                    </el-card>
                </el-aside>
            </template>
            <template #second>
                <el-main ref="main" class="h-full w-full p-el">
                    <el-container>
                        <el-header height="auto" class="p-0" style="background: none;border-bottom: none">
                            <el-card :body-style="{ height: '100%', width: '100%' }" class="w-full" style="height: 100%;width:100%">
                                <el-form :model="form" ref="formList" label-position="left" :inline="true" label-width="auto" class="search-form">
                                    <el-form-item label="档案名称" prop="name" style="margin-bottom: 0;">
                                        <el-input v-model="form.name" placeholder="请输入档案名称"  class="form_225" clearable
                                                  @keydown.enter="handleQuery"/>
                                    </el-form-item>
                                    <el-form-item label="合并状态" prop="mergeFlag" style="margin-bottom: 0;">
                                        <el-select v-model="form.mergeFlag" placeholder="请选择合并状态" clearable @change="handleQuery" class="form_225">
                                            <el-option v-for="item in mergeFlagOptions" :key="item.value" :label="item.name" :value="item.value" />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="所属机构:" prop="orgId" style="margin-bottom: 0;" v-if="institution.length > 1">
                                        <el-select v-model="form.orgId" class="form_225" clearable placeholder="请选择所属机构" @change="handleQuery">
                                            <el-option v-for="item in institution" :key="item.id" :label="item.name"
                                                       :value="item.id"/>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item style="margin-bottom: 0;">
                                        <el-button icon="Search" type="primary" @click="handleQuery">
                                            查询
                                        </el-button>
                                        <el-button icon="RefreshRight" plain @click="resetQuery">
                                            重置
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                            </el-card>
                        </el-header>
                        <el-main class="p-0 pt-el">
                            <el-card :body-style="{ height: '100%', width: '100%', padding: '15px 15px 0 15px' }" class="w-full h-full">
                                <el-container>
                                    <el-header height="auto" style="padding: 0 0 10px 0">
                                        <div style="height: 100%;width:100%;display: flex;justify-content:space-between;
									align-items:center;">
                                            <el-button icon="Files" plain type="primary" @click="handleMerge">
                                                合并成件
                                            </el-button>
                                            <div style="display: flex;justify-content: flex-end;align-items: center;">
                                                <div style="margin-right: 15px;" @click="getList">
                                                    <el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
                                                        <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                                            <Refresh/>
                                                        </el-icon>
                                                    </el-tooltip>
                                                </div>
                                                <div @click="screen">
                                                    <el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
                                                        <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                                            <FullScreen/>
                                                        </el-icon>
                                                    </el-tooltip>
                                                </div>
                                            </div>
                                        </div>
                                    </el-header>
                                    <el-main style="padding: 0">
                                        <el-table v-loading="dataLoading" :data="receiveData" border
                                                  class="h-full w-full"
                                                  @sort-change="tableSortChange"
                                                  @selection-change="handleSelectionChange">
                                            <el-table-column align="center"
                                                             type="selection"
                                                             :selectable="selectable"
                                                             width="50"/>
                                            <el-table-column align="left"
                                                             label="档案名称"
                                                             sortable="custom"
                                                             prop="name"/>
                                            <el-table-column align="center"
                                                             label="保留年限"
                                                             prop="retentionPeriod"
                                                             width='100'>
                                                <template #default="scope">
                                                    <span>{{ originalRetention(scope.row.retentionPeriod) }}</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center"
                                                             label="接收时间"
                                                             prop="createDate"
                                                             sortable="custom"
                                                             width='132'>
                                                <template #default="scope">
                                                    {{ moment(scope.row.createDate).format('YYYY年MM月DD日') }}
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center" label="合并状态" prop="mergeFlag" sortable="custom" width='132'>
                                                <template #default="{row}">
                                                    <el-tag :type="judgeMergeFlagColor(row.mergeFlag)">{{ selectDictLabel(mergeFlagOptions,row.mergeFlag)}}</el-tag>
                                                </template>
                                            </el-table-column>
                                            <el-table-column align="center"
                                                             label="来源"
                                                             prop="remark"
                                                             width='200'/>
                                            <el-table-column align="center"
                                                             label="所属机构"
                                                             prop="org.name"
                                                             width='200'/>
                                            <el-table-column align="center"
                                                             class-name="small-padding fixed-width"
                                                             fixed="right"
                                                             label="操作"
                                                             width="120">
                                                <template #default="scope">
                                                    <el-button icon="View" link type="primary" @click="receiveView(scope.row)">
                                                        查看
                                                    </el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </el-main>
                                    <el-footer>
                                        <div style="display: flex;justify-content: flex-end">
                                            <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :page-sizes="[20,40,60,80]"
                                                        :total="total" style="padding: 0"
                                                        @pagination="getList"/>
                                        </div>
                                    </el-footer>
                                </el-container>
                            </el-card>
                        </el-main>
                    </el-container>
                </el-main>
            </template>
        </DragLayout>





		<!-- 查看 -->
		<el-dialog v-if="open" v-model="open" :title="title" align-center append-to-body status_change top="8vh"
				   width="92%">
			<receive :receiveId="receiveId"/>
		</el-dialog>
		<!-- 关联 -->
		<el-dialog v-if="openAssociation" v-model="openAssociation" :title="title" append-to-body width="1300px">
			<association :relevance="relevance" style="height: 600px;" @childRelevance="parentRelevance"/>
		</el-dialog>
		<el-dialog v-if="RelevanceFileList" v-model="RelevanceFileList" :title="title" append-to-body
				   style="min-height: 600px;" width="1300px">
			<relevanceFile :relevanceList="relevanceList"/>
		</el-dialog>
		<!-- 合并 -->
		<el-dialog v-if="openMerge" v-model="openMerge" :title="title" v-loading="mergeLoading" append-to-body custom-class="dialogNoPadding"
				   width="1000px" >
            <el-divider style="margin: 0;"/>
            <DragLayout v-model="layoutScale" type="row">
                <template #first>
                    <categoryTree :query-params="categoryParams" height="500px"  @clickNode="onCategoryClick"/>
                </template>
                <template #second>
                    <archive-info-form ref="archiveInfoFormRef" :disabledObj="disabledObj"  v-model:model-value="archiveFormData"/>
                </template>
            </DragLayout>
            <el-divider style="margin: 0;"/>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="openMerge=false">取消</el-button>
                    <el-button :loading="submitLoading" type="primary" @click="handleConfirmation">
                        确定
                    </el-button>
                </div>
            </template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {FullScreen, Refresh} from '@element-plus/icons-vue'
import receive from './receive.vue'
import association from './association.vue'
// import merge from './merge.vue'
import relevanceFile from './relevanceFile.vue'
import DragLayout from "@/components/DragLayout/index.vue";
import archiveInfoForm from "@/components/archiveInfoForm/index.vue";
import dataClassificationTree from "@/components/dataClassificationTree/index.vue";
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import receiveList from "@/api/archive/archiveReception/receive"
import moment from "moment/moment";
import tool from "@/utils/tool";
import {getOrganizationInfo} from "@/utils/permission";
import category from "@/api/archive/categoryManagement/category";
import sysOrgService from "@/api/model/sys/sysOrgService";
const data = reactive({
	queryParams: {
		current: 1,
		size: 20,
	}
});
const receiveId = ref();
const total = ref(0);
const {queryParams} = toRefs(data);
const {proxy} = getCurrentInstance();
// 接收库List
const receiveData = ref([]);
const dataLoading = ref(false);
// 点击查询列表
const openTree = ref(true);
const receiveDataList = ref([]);
const originalClassifyId = ref("");
const handList = ref([]);
const openMerge = ref(false); // 合并弹窗
const archiveFormData = ref({});
const disabledObj = ref({});
const categoryParams = ref({});
const layoutScale = ref({ first: "40%", second: "60%" });
const submitLoading = ref(false);
const mergeLoading = ref(false);
const archiveInfoFormRef = ref(null);
const clickCategory = ref(undefined);
// 头部查询
const form = ref({});
// 有关联数据后弹窗显示关联数据
const RelevanceFileList = ref(false);
const relevanceList = ref([]);
// 关联数据
const relevance = ref([]);
//查看弹窗状态
const open = ref(false);
//排序字段
const sortParam = ref('');
// 查看标题
const title = ref('');
// 关联状态
const openAssociation = ref(false);
const mergeFlagOptions = ref([]);

const institution = ref([]);

onMounted(async () => {
    mergeFlagOptions.value = await proxy.getDictList("receive_merge_status");
	getList();
    institutionList();
});
/**
 * 机构列表
 */
function institutionList() {
    sysOrgService.list({
        current: 1,
        size: -1
    }).then(res => {
        if (res.code === 200) {
            institution.value = res.data.records
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}
// 判断颜色
function judgeMergeFlagColor(value) {
    return (
        {
            '1': 'warning',
            '2': 'primary',
            '3': 'success',
            '4': 'danger'
        }[value] || ''
    );
}
/**
 * 点击
 * @param data
 */
const onCategoryClick = async (data) => {
    mergeLoading.value = true;
    let res = await category.queryById({
        id: data?.id,
    });
    mergeLoading.value = false;
    if (res.code === 200) {
        clickCategory.value = data;
        const categoryInfo = res?.data;
        const ruleUserId = res?.data?.ruleUserId;
        archiveFormData.value = {
            ...archiveFormData.value,
            retentionPeriod:  categoryInfo.retentionPeriod || archiveFormData.value.retentionPeriod, // 保留年限
            protectLevel:  categoryInfo.protectLevel || archiveFormData.value.protectLevel, // 保密等级
            controlStatus:  categoryInfo.controlStatus || archiveFormData.value.controlStatus, // 控制等级
            ruleUserId: ruleUserId?ruleUserId.split(','):archiveFormData.value.ruleUserId, // 可查看人员
            // org: categoryInfo.org || archiveFormData.value.org, // 所属机构
            office:categoryInfo.office || archiveFormData.value.office, // 所属部门
            isElectronic: categoryInfo.isElectronic || archiveFormData.value.isElectronic, // 是否全电文档
            tagInfo: categoryInfo.tagInfo || archiveFormData.value.tagInfo, // 档案标签
        }
        archiveInfoFormRef.value.setRequired('office',data.openFlag !== '1');
    } else {
        proxy.msgError(res.msg);
    }
}
/**
 * 提交
 */
const handleConfirmation = async () => {
    const form = archiveInfoFormRef.value.getFormData();
    let idStr = handList.value.map((v) => v.id);
    if(clickCategory.value === undefined){
        return proxy.msgError('请选择门类');
    }
    const isValid = await archiveInfoFormRef.value.validateForm();
    if(isValid){
        submitLoading.value = true;
        let params = {
            year:form.year,
            month:form.month,
            categoryConfig:{
                id:clickCategory.value.id,
                recordGroupId:clickCategory.value.groupId,
                retentionPeriod:  form.retentionPeriod, // 保留年限
                protectLevel:  form.protectLevel, // 保密等级
                controlStatus:  form.controlStatus, // 控制等级
                isElectronic: form.isElectronic, // 是否全电文档
                tagInfo: form.tagInfo, // 档案标签
                ruleUserId:form.ruleUserId.toString(), // 可查看人员
                orgId:form.org.id, // 所属机构
                officeId:form.office.id, // 所属部门
            },
            originalIds:idStr.toString(),
        }
        receiveList.mergeOriginalInfo(params).then(res => {
            if (res.code === 200) {
                proxy.msgSuccess(res.msg);
                clickCategory.value = undefined;
                openMerge.value = false;
                getList();
            } else {
                proxy.msgError(res.msg);
            }
        }).catch((e) => {
            proxy.msgError(e.msg);
        }).finally(()=>{
            submitLoading.value = false;
        })

    }
}

/**
 * 合并成件
 */
function handleMerge(){
    if (handList.value.length > 0) {
        // 判断数据是否是同一个机构
        const data = handList.value[0];
        let orgId = data?.org?.id;
        handList.value.forEach(item => {
            if (item.org?.id !== orgId) {
                proxy.msgError('请选择同一机构的数据！');
                return false;
            }
        })
        categoryParams.value = {
            orgId: orgId ||  getOrganizationInfo()?.id,
        }
        archiveFormData.value = {
            year:data.year,
            month:data.month,
            retentionPeriod:  data.retentionPeriod, // 保留年限
            protectLevel:  "", // 保密等级
            controlStatus:  "", // 控制等级
            ruleUserId: [], // 可查看人员
            org: {id:orgId ||  getOrganizationInfo()?.id}, // 所属机构
            office:{ id: "" }, // 所属部门
            isElectronic: "", // 是否全电文档
            tagInfo: '', // 档案标签
        }
        openMerge.value = true;
        title.value = '合并成件';
        disabledObj.value = {
            year: false,
            month: false,
            retentionPeriod:false,
            protectLevel: false,
            controlStatus:false,
            ruleUserId: false,
            org: true,
            office: false,
            isElectronic: false,
            tagInfo: false,
        }

    } else {
        proxy.msgError('请先选择合并的数据！');
    }
}


function menuClick(data) {
	dataLoading.value = true;
	receiveDataList.value = data;
	originalClassifyId.value = data.id;
	getList();
}

// 查看数据
function receiveView(data) {
	receiveId.value = data.id;
	open.value = true;
	title.value = '查看';
}

// function associationList(val) {
// 	receiveList.queryById({
// 		current: 1,
// 		size: -1,
// 		fileOriginalId: val.id
// 	}).then(res => {
// 		if (res.code === 200) {
// 			if (res.data.records.length == 0) {
// 				openAssociation.value = true;
// 				title.value = '关联';
// 				relevance.value = val;
// 			} else {
// 				proxy.msgError('该条数据已有关联文件！');
// 				getRelevanceFileList(res.data.records[0])
// 			}
// 		}
// 	}).catch(() => {
// 		proxy.msgError('查询失败');
// 	})
// }

// function getRelevanceFileList(val) {
// 	relevanceList.value = val;
// 	RelevanceFileList.value = true;
// 	title.value = '已关联数据';
// }

function parentRelevance() {
	openAssociation.value = false;
	menuClick(receiveDataList.value);
}

// 保留年限字段筛选
function originalRetention(val) {
	if (val == 'Y') {
		return '永久'
	} else if (val == 'D5') {
		return '5年'
	} else if (val == 'D10') {
		return '10年 '
	} else if (val == 'D20') {
		return '20年'
	} else if (val == 'D30') {
		return '30年'
	}
}

function handleQuery() {
	getList();
}

// 重置
function resetQuery() {
	form.value = {};
	getList();
}

// function handleExport() {
// 	if (handList.value.length > 0) {
// 		openMerge.value = true;
// 		title.value = '合并成件';
// 	} else {
// 		proxy.msgError('请先选择合并的数据！');
// 	}
//
// }

function handleSelectionChange(val) {
	handList.value = val;
}
function selectable(row){
    return row?.org?.id === getOrganizationInfo().id && row.mergeFlag ==='1';
}

function tableSortChange(data) {
	sortParam.value = '';
	if (data.order) {
		sortParam.value = data.prop + ':' + data.order;
	}
	handleQuery();
}

// function parentMethod() {
// 	openMerge.value = false;
// 	menuClick(receiveDataList.value);
// }

// 进入时查询全部
function getList() {
	dataLoading.value = true;
	receiveList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		name: form.value.name,
        mergeFlag: form.value.mergeFlag,
		sortParam: sortParam.value ? sortParam.value : null,
		'classifyConfig.id': originalClassifyId.value ? originalClassifyId.value : null,
        'org.id': form.value.orgId?form.value.orgId:getOrganizationInfo()?.id,
	}).then(res => {
		if (res.code === 200) {
			openTree.value = true;
			receiveData.value = res.data.records;
			total.value = res.data.total
		}
		dataLoading.value = false;
	}).catch(() => {
		dataLoading.value = false;
		proxy.msgError('查询失败');
	})
}

//全屏
function screen() {
	let element = document.documentElement;
	tool.screen(element);
}
</script>

<style scoped lang="scss">
.search-form{
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.myHeader {
	font-size: 16px;
	padding: 15px 0 0 20px;
}

:deep(.el-dialog__body) {
	padding: 0;
}

.dialogNoPadding {
	padding: 0;
    :deep(.el-dialog__body){
        height: 500px;
    }
}
</style>
