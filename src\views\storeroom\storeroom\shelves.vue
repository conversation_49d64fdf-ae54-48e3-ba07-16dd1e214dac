<template>
	<div>
		<el-tabs v-model="activeName" class="shelves-tabs">
			<el-tab-pane label="档案库" name="first">
				<div class="image-container">
					<img
						:src="houseInfoUrl"
						class="house-image"
						@error="handleImageError"
						@load="handleImageLoad"
						v-if="houseInfoUrl && imageLoaded"
					>
					<div v-if="!imageLoaded || !houseInfoUrl" class="default-image-container">
						<el-icon size="64" color="#909399"><Picture /></el-icon>
						<span class="default-text">暂无平面图</span>
					</div>
				</div>
			</el-tab-pane>
			<el-tab-pane label="档案室" name="second" class="h-full">
				<!-- 加载状态 -->
				<div v-if="loading" class="loading-container">
					<el-skeleton :rows="3" animated />
				</div>

				<!-- 档案室卡片网格 -->
				<div v-else class="rooms-grid">
					<el-card
						v-for="(item, index) in storeroomList"
						:key="index"
						class="room-card"
						shadow="hover"
						@click="addShelves(item)"
					>
						<!-- 图片区域 -->
						<div class="card-image">
							<img
								:src="item.roomCoverUrl"
								:alt="item.roomName"
								class="room-image"
								@error="handleRoomImageError($event, index)"
								@load="handleRoomImageLoad($event, index)"
							>
							<!-- 默认图片 -->
							<div v-if="!item.imageLoaded" class="default-image">
								<el-icon size="48" color="#909399"><Picture /></el-icon>
								<span class="default-text">暂无图片</span>
							</div>
							<div class="image-overlay">
								<el-button type="primary" size="small" icon="View">查看详情</el-button>
							</div>
						</div>

						<!-- 信息区域 -->
						<div class="card-content">
							<h3 class="room-name">{{ item.roomName || '暂无名称' }}</h3>
							<div class="room-info">
								<div class="info-item">
									<span class="label">编号：</span>
									<span class="value">{{ item.roomNum || '暂无' }}</span>
								</div>
								<div class="info-item">
									<span class="label">所属仓库：</span>
									<span class="value">{{ item.roomHouse?.houseName || '暂无' }}</span>
								</div>
								<div class="info-item">
									<span class="label">面积：</span>
									<span class="value">{{ item.roomArea || '暂无' }} ㎡</span>
								</div>
								<div class="info-item">
									<span class="label">管理员：</span>
									<span class="value">{{ item.roomManager?.name || '暂无' }}</span>
								</div>
							</div>
						</div>
					</el-card>
				</div>

				<!-- 空状态 -->
				<div v-if="!loading && storeroomList.length === 0" class="empty-state">
					<el-empty description="暂无档案室数据" :image-size="120">
					</el-empty>
				</div>

				<!-- 分页器 -->
				<div class="pagination-wrapper">
					<pagination
						:total="total"
						v-model:page="queryParams.current"
						v-model:limit="queryParams.size"
						@pagination="selectList"
					/>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
import { Picture } from '@element-plus/icons-vue';

export default {
	name: 'shelves',
	components: { Picture },
	props: {
		parentDataInfo: {}
	},
	data() {
		return {
			activeName: 'first',
			total: 0,
			storeroomList: [],
			houseInfoUrl: '',
			imageLoaded: false,
			loading: false,
			queryParams: {
				current: 1,
				size: 10
			},
		}
	},
	mounted() {
		this.queryList()
		this.selectList();
	},
	methods: {
		addShelves(data) {
			this.$emit('shelvesView', data);
		},
		// 处理档案库图片加载错误
		handleImageError() {
            console.log(111,'error')
			this.imageLoaded = false;
		},
		// 处理档案库图片加载成功
		handleImageLoad() {
            console.log(222,'success')
			this.imageLoaded = true;
		},
		// 处理档案室图片加载错误
		handleRoomImageError(event, index) {
			event.target.style.display = 'none'
            this.storeroomList[index].imageLoaded = false;
		},
		// 处理档案室图片加载成功
		handleRoomImageLoad(event, index) {
            this.storeroomList[index].imageLoaded = true;
		},
		async selectList() {
			this.loading = true;
			try {
				const result = await storeroomData.roomList({
					...this.queryParams,
					'roomHouse.id': this.parentDataInfo.recordHouseInfoTargetId ? this.parentDataInfo.recordHouseInfoTargetId : this.parentDataInfo.id
				});

				if (result.code === 200) {
					// 为每个项添加图片加载状态标记
					this.storeroomList = result.data.records.map(item => ({
						...item,
						imageLoaded: true // 初始状态为未加载
					}));
					this.total = result.data.total;
				}
			} catch (error) {
				console.error('获取档案室列表失败:', error);
				this.$message.error('获取数据失败');
			} finally {
				this.loading = false;
			}
		},
		async queryList() {
			try {
				const result = await storeroomData.queryById({
					id: this.parentDataInfo.recordHouseInfoTargetId ? this.parentDataInfo.recordHouseInfoTargetId : this.parentDataInfo.id
				});

				if (result.code === 200) {
					this.houseInfoUrl = result.data.housePlaneUrl;
					this.imageLoaded = true; // 重置图片加载状态
				}
			} catch (error) {
				console.error('获取档案库信息失败:', error);
				this.$message.error('获取档案库信息失败');
			}
		}
	}
}
</script>

<style scoped>
.shelves-tabs {
	border-radius: 8px;
	overflow: hidden;
}

.shelves-tabs :deep(.el-tabs__header) {
	margin: 0;
	padding: 0 20px;
	border-bottom: 1px solid #ebeef5;
}

.shelves-tabs :deep(.el-tabs__content) {
	padding: 20px;
    height: 100%;
}

/* 档案库平面图样式 */
.image-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
    min-height: 500px;
    max-height: 600px;
	border-radius: 8px;
    overflow: hidden;
}

.house-image {
    width: auto;
    height: 100%;
    min-width: 300px;
    min-height: 500px;
    max-height: 600px;
	object-fit: contain;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: #f9fafb;
    padding: 20px;
    border: 2px dashed #dcdfe6;
}

.default-image-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #909399;
    height: 100%;
}

.default-text {
	margin-top: 16px;
	font-size: 16px;
	color: #909399;
}

/* 档案室列表样式 */
.loading-container {
	padding: 20px;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.rooms-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(225px, 1fr));
	gap: 10px;
}

.room-card {
	border-radius: 5px;
	overflow: hidden;
	transition: all 0.3s ease;
	cursor: pointer;
	border: none;
}

.room-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.room-card :deep(.el-card__body) {
	padding: 0;
}
::v-deep .room-card .el-card__body{
    padding: 15px;
}

.card-image {
	position: relative;
	height: 150px;
	overflow: hidden;
	background: #f5f7fa;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 10px;
}

.room-image {
	max-width: calc(100% - 20px);
	max-height: calc(100% - 20px);
	width: auto;
	height: auto;
	object-fit: contain;
	transition: transform 0.3s ease;
	border-radius: 4px;
	background: #fff;
	padding: 5px;
	border: 1px solid #e4e7ed;
}

.room-card:hover .room-image {
	transform: scale(1.05);
}

.default-image {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #f5f7fa;
	color: #909399;
}

.default-text {
	margin-top: 8px;
	font-size: 12px;
	color: #909399;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.room-card:hover .image-overlay {
	opacity: 1;
}

.room-name {
	margin: 15px 0 15px 0;
	font-size: 18px;
	font-weight: 600;
	color: #303133;
	line-height: 1.4;
}

.room-info {
	display: flex;
	flex-direction: column;
    gap: 10px;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.label {
	font-size: 14px;
	color: #606266;
	font-weight: 500;
	flex-shrink: 0;
}

.value {
	font-size: 14px;
	color: #303133;
	font-weight: 600;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.empty-state {
	padding: 60px 20px;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	text-align: center;
}

.pagination-wrapper {
	display: flex;
	justify-content: right;
	padding: 20px;
	background: #fff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.rooms-grid {
		grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
		gap: 5px;
	}
}

@media (max-width: 768px) {
	.rooms-grid {
		grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
		gap: 12px;
	}
}

@media (max-width: 480px) {
	.rooms-grid {
		grid-template-columns: 1fr;
	}
}
</style>
