<template>
    <div class="comDoorTable" v-loading="loading">
        <el-header class="filter-input">
            <el-input placeholder="输入关键字进行过滤" v-model="menuFilterText" clearable></el-input>
            <el-button :type="isUnfold ? 'success' : 'warning'" size="small" @click="unfoldClose()">
                <el-icon :class="{ rotated: isUnfold }" class="unfold-icon">
                    <arrow-down />
                </el-icon>
                {{ isUnfold ? '展开' : '收起' }}
            </el-button>
        </el-header>
        <el-main class="noPadding" :style="`height: ${height}`">
        <el-tree ref="treeRef" :data="menuList" :props="menuProps" :filter-node-method="menuFilterNode" default-expand-all @node-click="leftHandleCurrentChange">
            <template #default="{ node }">
                <span class="tree-node">
                    <div class="node-content">
                        <el-icon v-if="node.level === 1" class="node-icon">
                            <MessageBox />
                        </el-icon>
                        <el-icon v-else-if="node.level === 2" class="node-icon">
                            <Files />
                        </el-icon>
                        <el-icon v-else class="node-icon">
                            <Guide />
                        </el-icon>
                        <span class="node-label">{{ node.label }}</span>
                        <div class="node-tags">
                            <el-tag v-if="node.level === 1" size="small" type="primary">库</el-tag>
                            <el-tag v-else-if="node.level === 2" size="small" type="success">室</el-tag>
                            <el-tag v-else-if="node.level === 3" size="small" type="warning">柜</el-tag>
                        </div>
                    </div>
                </span>
            </template>
        </el-tree>
        </el-main>
    </div>
</template>
<script setup>
import storeroomData from '@/api/archive/storeroom/storeroom';
import { reactive, ref, toRefs, onBeforeMount, defineEmits, watch,defineProps } from 'vue';
import { getOrganizationInfo } from '@/utils/permission';
defineProps({
    height:{
        type: String,
        default: ()=>{
            return window.innerHeight - 170 + "px";
        },
    },
    });
const treeRef = ref();
const menuList = ref();
const data = reactive({
    queryParams: {
        current: 1,
        size: 10
    }
});
const menuFilterText = ref('');
const menuProps = ref({
    children: 'children',
    label: 'name'
});
watch(menuFilterText, (val) => {
    treeRef.value.filter(val);
});
const menuFilterNode = (value, data) => {
    if (!value) return true;
    return data.name.includes(value);
};

const loading = ref(true);
const total = ref(0);
const { queryParams } = toRefs(data);
const isUnfold = ref(false);
/**
 * 展开关闭
 */
const unfoldClose = () => {
    const nodes = treeRef.value.store._getAllNodes();
    nodes.forEach((node) => {
        node.expanded = isUnfold.value;
    });

    isUnfold.value = !isUnfold.value;
}
/** 查询全宗列表 */
async function getList() {
    await storeroomData.treeData({ ...queryParams.value, orgId: getOrganizationInfo()?.id }).then((res) => {
        if (res.code === 200) {
            menuList.value = res.data;
            loading.value = false;
            total.value = res.data.total;
        }
    });
}
const emit = defineEmits(['clickChild']);
function leftHandleCurrentChange(val) {
    emit('clickChild', val);
}
onBeforeMount(() => {
    getList();
});
</script>
<style scoped>
.comDoorTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}

.comDoorTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}

.comDoorTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
.filter-input{
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    padding: 20px;

}
::v-deep .filter-input .el-input__inner{
    border-radius: 0;
}
.filter-input .unfold-icon {
    transition: transform 0.3s ease;
    margin-right: 2px;
}

.filter-input .unfold-icon.rotated {
    transform: rotate(180deg);
}
.tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.node-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.node-icon {
    margin-right: 8px;
    color: #909399;
}

.node-label {
    font-size: 14px;
    margin-right: 8px;
}
.node-tags {
    display: flex;
    gap: 5px;
}
</style>
