<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2024-12-23 14:54:38
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2025-01-07 14:32:26
 * @FilePath: \archive-manage-front\src\views\home\searchSystem\newRelationShip.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<div v-loading="loading">
		<!-- <div style="display: flex; padding: 10px;">
			<div style="margin-right: 10px"><el-switch v-model="horizontal"></el-switch> 横向</div>
			<div style="margin-right: 10px"><el-switch v-model="collapsable"></el-switch> 可收起</div>
			<div style="margin-right: 10px"><el-switch v-model="disaled"></el-switch> 禁止编辑</div>
			<div style="margin-right: 10px"><el-switch v-model="onlyOneNode"></el-switch> 仅拖动当前节点</div>
			<div style="margin-right: 10px"><el-switch v-model="cloneNodeDrag"></el-switch> 拖动节点副本</div>
		</div>
		<div style="padding: 0 10px 10px">
			背景色：<el-color-picker v-model="style.background" size="small"></el-color-picker>&nbsp;
			文字颜色：<el-color-picker v-model="style.color" size="small"></el-color-picker>&nbsp;
		</div> -->
		<div style="height: 85vh;">
			<vue3-tree-org v-if="Object.keys(data).length" ref="treeOrg" :collapsable="collapsable" :data="data" :horizontal="horizontal"
						   :label-style="style" :node-draggable="false" :only-one-node="onlyOneNode" :props="props"
						   :toolBar="toolBar" center disabled>
				<template v-slot:expand="{ node }">
					<div v-if="!node.expand" class="expandTrue">
						<el-icon style="color:#fff">
							<Plus/>
						</el-icon>
					</div>
					<div v-else class="expandFalse">
						<el-icon style="color:#fff">
							<Minus/>
						</el-icon>
					</div>
				</template>
				<template v-slot="{ node }">
					<div v-if="node.$$data.type === '1'" class="tree-org-node__text node-label shopList">
						<div>
							<p style="padding:8px 20px;text-align: left;font-size:16px;color:#3A8CFF;background: #E8F0FF;">
								商品列表</p>
							<div style="padding:8px 20px 0 20px;">
								<el-table :data="node.$$data.data" height="200px" style="width: 100%"
										  @wheel.native.stop="handleWheel">
									<el-table-column align="center" width="62">
										<template #default="scope">
											<div
												style="width: 100%;height: 100%;display: flex;justify-content: right;align-items: center">
												<el-radio v-model="shopValue" :value="scope.row.id" size="large"
														  @change="changeShop(node, scope.row)"/>
											</div>
										</template>
									</el-table-column>
									<el-table-column label="商品名称" property="common_name" show-overflow-tooltip
													 width="120"/>
									<el-table-column label="生产厂家" property="manufacture_name" show-overflow-tooltip
													 width="200"/>
									<el-table-column label="规格" property="package_specification" show-overflow-tooltip
													 width="100"/>
								</el-table>
							</div>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '2'" class="tree-org-node__text node-label"
						 style="border-radius: 10px;">
						<div style="padding: 14px 14px 6px 14px">
							<div class="custom-content">
								<div style="width:70px;text-align: right;">
									商品名称：
								</div>
								<el-button link size="small" style="color:#333" type="primary">
									{{ node.$$data.label }}
								</el-button>
							</div>
							<div class="custom-content">
								<div style="width:70px;text-align: right;">
									规格：
								</div>
								<el-button link size="small" style="color:#333" type="primary">
									{{ node.$$data.package_specification }}
								</el-button>
							</div>
							<div class="custom-content">
								<div style="width:70px;text-align: right;">
									生产厂家：
								</div>
								<el-button link size="small" style=" cursor: pointer;"
										   type="primary"
										   @click="handleOtherDetail(node, '1')">
									{{ node.$$data.manufacture_name }}
								</el-button>
							</div>
							<div class="custom-content">
								<div style="width:70px;text-align: right;">批号：</div>
								<el-button link size="small" style=" cursor: pointer;"
										   type="primary"
										   @click.stop="batchNumberClickOpen(node)">
									{{ node.$$data.batch_number ? node.$$data.batch_number : '请选择' }}
								</el-button>
							</div>
						</div>
						<div class="custom-content" style="display: flex;justify-content: space-between;padding:8px;
							background: #EDF2F9;">
							<div style="flex: 1;text-align:center;border-right:1px solid #C8CDD3">
								<el-button link size="small" style=" cursor: pointer;color:#32465B"
										   type="primary"
										   @click.stop="handleClick('4', node)">
									采购订单
								</el-button>
							</div>
							<div style="flex: 1;text-align:center">
								<el-button link size="small" style=" cursor: pointer;color:#32465B"
										   type="primary"
										   @click.stop="handleClick('5', node)">
									销售订单
								</el-button>
							</div>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '4'" class="tree-org-node__text node-label"
						 style="width:232px">
						<div class="custom-content" style="padding: 14px 14px 0px 14px">采购订单：
							<div style="color:#2A76F8;cursor: pointer;" @click.stop="handleOtherDetail(node, '1')">
								{{ node.$$data.label }}
							</div>
						</div>
						<div class="custom-content" style="padding: 0 14px 0 14px">单据日期：
							<div style="color:#32465B;cursor: pointer;">
								{{ dateFormatting(node.$$data.order_time) }}
							</div>
						</div>
						<div class="custom-content" style="display: flex;justify-content: space-between;padding:8px;
							background: #FFF7ED;">
							<el-button link size="small" style="flex:1; cursor: pointer;color: #D78723;margin:0;padding:2px 8px;
									   border-right:1px solid #E8DBCB"
									   type="primary"
									   @click.stop="handleClick('6', node)">
								入库记录
							</el-button>
							<el-button link size="small" style=" flex:1;cursor: pointer;color: #D78723;margin:0;padding:2px 8px;
									   border-right:1px solid #E8DBCB;text-align:center"
									   type="primary"
									   @click.stop="handleClick('7', node, '1')">
								商品
							</el-button>
							<el-button link size="small" style=" flex:1;cursor: pointer;color: #D78723;margin:0;padding:2px 8px;text-align:center"
									   type="primary"
									   @click.stop="handleClick('8', node)">
								供应商
							</el-button>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '5'" class="tree-org-node__text node-label"
						 style="width:242px">
						<div class="custom-content" style="padding: 14px 14px 0px 14px">销售订单：
							<div style="color:#2A76F8;cursor: pointer;"
								 @click.stop="handleOtherDetail(node, '2')">
								{{ node.$$data.label }}
							</div>
						</div>
						<div class="custom-content" style="padding: 0 14px 0 14px">单据日期：
							<div style="color:#32465B;cursor: pointer;">
								{{ dateFormatting(node.$$data.doc_date) }}
							</div>
						</div>
						<div class="custom-content" style="display: flex;justify-content: space-between;padding:8px;
							background: #FFF7ED;">
							<el-button link size="small" style=" cursor: pointer;color: #D78723;margin:0;padding:2px 8px;
									   border-right:1px solid #E8DBCB"
									   type="primary"
									   @click.stop="handleClick('9', node)">
								出库记录
							</el-button>
							<el-button link size="small" style=" cursor: pointer;color: #D78723;margin:0;padding:2px 8px;
									   border-right:1px solid #E8DBCB"
									   type="primary"
									   @click.stop="handleClick('7', node, '2')">
								商品
							</el-button>
							<el-button link size="small" style=" cursor: pointer;color: #D78723;margin:0;padding:2px 8px;
									   border-right:1px solid #E8DBCB"
									   type="primary"
									   @click.stop="handleClick('10', node)">
								客户
							</el-button>
							<el-button link size="small" style=" cursor: pointer;color: #D78723;margin:0;padding:2px 8px"
									   type="primary"
									   @click.stop="handleClick('11', node)">
								运输记录
							</el-button>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '7'" class="tree-org-node__text node-label"
						 style="border-top: 6px solid #2AB0AE;">
						<div>
							<div style="padding:8px 20px 0 20px;display:flex;justify-content: space-between;
								align-items:center;background: #E5F6F6;">
								<p style="text-align: left;margin-bottom:8px;font-size:16px;color:#149D9B">商品</p>
							</div>
							<div style="padding:8px 20px 0 20px; ">
								<el-table :data="node.$$data.data"
										  height="200px" style="width: 100%"
										  @wheel.native.stop="handleWheel">
									<el-table-column label="自编码" property="commodity_self_code" show-overflow-tooltip
													 width="100"/>
									<el-table-column label="商品名称" property="common_name" show-overflow-tooltip
													 width="120"/>
									<el-table-column label="规格" property="package_specification" show-overflow-tooltip
													 width="100"/>
									<el-table-column label="厂家" property="manufacture_name" show-overflow-tooltip
													 width="200"/>
									<el-table-column label="操作" width="100">
										<template #default="scope">
											<el-button link size="small" style=" cursor: pointer;" type="primary"
													   @click.stop="handleOtherDetail({
													   		$$data:{recordId:scope.row.recordId}
													   }, '1')">
												查看
											</el-button>
											<el-button link size="small" type="warning"
													   @click.stop="handleDownload(scope.row, '1')">
												下载
											</el-button>
										</template>
									</el-table-column>
								</el-table>
							</div>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '6'" class="tree-org-node__text node-label"
						 style="border-top: 6px solid #2AB0AE;">
						<div>
							<div style="padding:8px 20px 0 20px;display:flex;justify-content: space-between;
								align-items:center;background: #E5F6F6;">
								<p style="text-align: left;margin-bottom:8px;font-size:16px;color:#149D9B">入库记录</p>
								<p style="border-radius: 6px;text-align: left;margin-bottom:8px;font-size:14px;
								   color:#000000;cursor: pointer;padding:6px 16px;background:#97EDEC"
								   @click.stop="exportTableToExcel(node.$$data.data, [
										{ title: '自编码', key: 'commodity_self_code' },
										{ title: '商品名称', key: 'common_name' },
										{ title: '规格', key: 'package_specification' },
										{ title: '生产厂家', key: 'manufacture_name' },
										{ title: '数量', key: 'into_quantity' },
										{ title: '批号', key: 'batch_number' },
										{ title: '入库时间', key: 'into_time' },
									], '入库记录')">
									导出
								</p>
							</div>
							<div style="padding:8px 20px 0px 20px; ">
								<el-table :data="node.$$data.data" height="200px" style="width: 100%"
										  @wheel.native.stop="handleWheel">
									<el-table-column label="自编码" property="commodity_self_code" show-overflow-tooltip
													 width="100"/>
									<el-table-column label="商品名称" property="common_name" show-overflow-tooltip
													 width="120"/>
									<el-table-column label="规格" property="package_specification" show-overflow-tooltip
													 width="100"/>
									<el-table-column label="生产厂家" property="manufacture_name" show-overflow-tooltip
													 width="200"/>
									<el-table-column label="数量" property="into_quantity" show-overflow-tooltip
													 width="80"/>
									<el-table-column label="批号" property="batch_number" show-overflow-tooltip
													 width="100"/>
									<el-table-column :formatter="row => dateFormatting(row.into_time)" label="入库时间" property="into_time"
													 show-overflow-tooltip
													 width="150"/>
									<el-table-column label="操作" width="100">
										<template #default="scope">
											<el-button link size="small" type="primary"
													   @click.stop="handleOtherDetail({
													   		$$data:{recordId:scope.row.recordId}
													   }, '1')">
												查看
											</el-button>
											<el-button link size="small" type="warning"
													   @click.stop="handleDownload(scope.row, '1')">
												下载
											</el-button>
										</template>
									</el-table-column>
								</el-table>
							</div>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '9'" class="tree-org-node__text node-label"
						 style="border-top: 6px solid #2AB0AE;">
						<div>
							<div style="display:flex;justify-content: space-between;padding:8px 20px 0 20px;
								background: #E5F6F6;align-items:center">
								<p style="text-align: left;margin-bottom:8px;font-size:16px;color:#149D9B">出库记录</p>
								<p style="border-radius: 6px;text-align: left;margin-bottom:8px;font-size:14px;
									color:#000000;cursor: pointer;padding:6px 16px;background:#97EDEC"
								   @click.stop="exportTableToExcel(node.$$data.data, [
										{ title: '自编码', key: 'commodity_self_code' },
										{ title: '商品名称', key: 'common_name' },
										{ title: '规格', key: 'package_specification' },
										{ title: '生产厂家', key: 'manufacture_name' },
										{ title: '数量', key: 'out_quantity' },
										{ title: '批号', key: 'batch_number' },
										{ title: '出库时间', key: 'out_time' },
									], '出库记录')">
									导出
								</p>
							</div>
							<div style="padding:8px 20px 0 20px; ">
								<el-table :data="node.$$data.data"
										  height="200px" style="width: 100%"
										  @wheel.native.stop="handleWheel">
									<el-table-column label="自编码" property="commodity_self_code" show-overflow-tooltip
													 width="100"/>
									<el-table-column label="商品名称" property="common_name" show-overflow-tooltip
													 width="120"/>
									<el-table-column label="规格" property="package_specification" show-overflow-tooltip
													 width="100"/>
									<el-table-column label="生产厂家" property="manufacture_name" show-overflow-tooltip
													 width="200"/>
									<el-table-column label="数量" property="out_quantity" show-overflow-tooltip
													 width="80"/>
									<el-table-column label="批号" property="batch_number" show-overflow-tooltip
													 width="100"/>
									<el-table-column :formatter="row => dateFormatting(row.out_time)" label="出库时间" property="out_time"
													 show-overflow-tooltip
													 width="150"/>
									<el-table-column label="操作" width="100">
										<template #default="scope">
											<el-button link size="small" type="primary"
													   @click.stop="handleOtherDetail({
													   		$$data:{recordId:scope.row.recordId}
													   }, '1')">
												查看
											</el-button>
											<el-button link size="small" type="warning"
													   @click.stop="handleDownload(scope.row, '1')">下载
											</el-button>
										</template>
									</el-table-column>
								</el-table>
							</div>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '8' || node.$$data.type === '10'"
						 class="tree-org-node__text node-label"
						 style="border-top: 6px solid #2AB0AE;">
						<div>
							<div style="display:flex;justify-content: space-between;padding:8px 20px 0 20px;
								background: #E5F6F6;">
								<p style="text-align: left;margin-bottom:8px;font-size:16px;color:#149D9B">
									{{ node.$$data.type === '8' ? '供应商' : '客户' }}
								</p>
							</div>
							<div style="padding:8px 20px 0 20px; ">
								<el-table :data="node.$$data.data" height="152px" style="width: 100%"
										  @wheel.native.stop="handleWheel">
									<el-table-column label="自编码" property="supplier_coding" show-overflow-tooltip
													 width="100"/>
									<el-table-column :label="`${node.$$data.type === '8' ? '供应商' : '客户'}名称`"
													 property="enterprise_name"
													 show-overflow-tooltip
													 width="120">
									</el-table-column>
									<el-table-column label="操作" width="100">
										<template #default="scope">
											<el-button link size="small" type="primary"
													   @click.stop="handleOtherDetail({
													   		$$data:{recordId:scope.row.recordId}
													   }, '1')">
												查看
											</el-button>
											<el-button link size="small" type="warning"
													   @click.stop="handleDownload(scope.row, '1')">
												下载
											</el-button>
										</template>
									</el-table-column>
								</el-table>
							</div>
						</div>
					</div>
					<div v-else-if="node.$$data.type === '11'" class="tree-org-node__text node-label"
						 style="border-top: 6px solid #2AB0AE;">
						<div>
							<div style="padding:8px 20px 0 20px;display:flex;justify-content: space-between;
								align-items:center;background: #E5F6F6;">
								<p style="text-align: left;margin-bottom:8px;font-size:16px;color:#149D9B">运输记录</p>
							</div>
							<div style="padding:8px 20px 20px 20px;">
								<!-- 运输记录卡片列表 -->
								<div v-for="(record, index) in node.$$data.data" :key="index"
									 class="transport-record-card"
									 style="margin-bottom: 16px; border: 1px solid #e4e7ed; border-radius: 8px; background: #fff;">
									<!-- 卡片头部 -->
									<div style="padding: 16px; border-bottom: 1px solid #f0f0f0;">
										<div style="display: flex; justify-content: space-between; align-items: center;">
											<div style="display: flex; align-items: center; gap: 20px;">
												<div>
													<span style="color: #909399; font-size: 14px;">运单号：</span>
													<span style="color: #303133; font-weight: 500;">{{ record.order_no || '--' }}</span>
												</div>
												<div>
													<span style="color: #909399; font-size: 14px;">送达时间：</span>
													<span style="color: #303133;">{{ dateFormatting(record.delivery_time) }}</span>
												</div>
											</div>
											<div style="display: flex; gap: 8px;">
												<el-button
													size="small"
													type="primary"
													@click.stop="toggleVerificationReport(record, index)"
													style="padding: 6px 12px;">
													{{ record.showVerificationReport ? '收起验证报告' : '验证报告' }}
												</el-button>
												<el-button
													size="small"
													type="success"
													link
													@click.stop="handleOtherDetail({$$data:{recordId:record.recordId}}, '1')">
													查看详情
												</el-button>
												<el-button
													size="small"
													type="warning"
													link
													@click.stop="handleDownload(record, '1')">
													下载
												</el-button>
											</div>
										</div>
									</div>

									<!-- 验证报告详情 -->
									<div v-if="record.showVerificationReport"
										 style="padding: 16px; background: #f8f9fa;">
										<div style="margin-bottom: 12px;">
											<span style="color: #409eff; font-weight: 500; font-size: 16px;">验证报告</span>
										</div>
										<el-table
											:data="record.verificationReports || []"
											size="small"
											style="width: 100%;"
											:show-header="true">
											<el-table-column label="档案名称" property="archive_name" show-overflow-tooltip/>
											<el-table-column label="档案号" property="archive_no" show-overflow-tooltip/>
											<el-table-column label="操作" width="120">
												<template #default="scope">
													<el-button
														link
														size="small"
														type="primary"
														@click.stop="handleVerificationDetail(scope.row)">
														查看
													</el-button>
													<el-button
														link
														size="small"
														type="warning"
														@click.stop="handleVerificationDownload(scope.row)">
														下载
													</el-button>
												</template>
											</el-table-column>
										</el-table>
										<div v-if="!record.verificationReports || record.verificationReports.length === 0"
											 style="text-align: center; color: #909399; padding: 20px;">
											暂无验证报告数据
										</div>
									</div>
								</div>

								<!-- 无数据提示 -->
								<div v-if="!node.$$data.data || node.$$data.data.length === 0"
									 style="text-align: center; color: #909399; padding: 40px;">
									暂无运输记录数据
								</div>
							</div>
						</div>
					</div>
					<div v-else class="tree-org-node__text node-label" style="padding:14px">
						<el-button link size="small" style="color: #333" type="primary">{{ node.label }}</el-button>
					</div>
				</template>
			</vue3-tree-org>
		</div>
		<el-dialog v-model="open" :before-close="close" title="批号" width="400">
			<el-input
				v-model="batchSearchStr"
				placeholder="请输入需要检索的批号"
				prefix-icon="Search"
				style="width: 100%;margin-bottom: 10px"
				@input="filterBatchNumber"
			/>
			<el-table :data="filterBatchNumberList" height="200px" style="width: 100%">
				<el-table-column width="55">
					<template #default="scope">
						<el-radio v-model="batchNumberVal" :value="scope.row.batch_number" size="large"
								  @change="changeBatchNumber(scope.row)"></el-radio>
					</template>
				</el-table-column>
				<el-table-column label="批号" property="batch_number"></el-table-column>
			</el-table>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="close">
						取消
					</el-button>
					<el-button type="primary" @click="batchNumberSubmit">
						确定
					</el-button>
				</div>
			</template>
		</el-dialog>
		<el-dialog v-if="openView" v-model="openView" append-to-body title="查看详情" top="8vh" width="92%">
			<viewFiles :receiveId="receiveId"/>
		</el-dialog>
	</div>
</template>
<script>
import retrievalList from '@/api/archive/retrieval';
import viewFiles from '@/views/archiveReception/view.vue';
import {saveAs} from 'file-saver';
import ExcelJS from "exceljs";
import moment from 'moment';
import {Minus, Plus} from '@element-plus/icons-vue';

export default {
	name: "baseTree",
	props: ['name', 'variety'],
	components: {
		viewFiles
	},
	data() {
		return {
			loading: false,
			openView: false,
			receiveId: {
				id: null
			},
			shopValue: '',
			batchSearchStr: '',
			batchNumberList: [],
			filterBatchNumberList: [],
			batchNumberVal: '',
			data: {},
			open: false,
			props: {id: 'id', pid: 'pid', label: 'label', expand: 'expand', children: 'children'},
			toolBar: {scale: true, restore: true, expand: true, zoom: true, fullscreen: true,},
			horizontal: false,
			collapsable: true,
			onlyOneNode: false,
			expandAll: true,
			disaled: false,
			style: {
				background: "#fff",
				color: "#000",
			},
		};
	},
	async created() {
		await this.init()
	},
	methods: {
		dateFormatting(date) {
			return date ? moment(date)?.format('YYYY-MM-DD') : '--'
		},
		async init() {
			this.loading = true
			const res = await retrievalList.graphListLikeNameNew({searchStr: this.name, type: this.variety})
			if (res.code === 200) {
				if (res.data.length) {
					this.data = res.data?.[0]
				} else {
					this.msgError('未查询到相关数据')
				}

			}
			this.loading = false
		},
		async reset() {
			this.data = {}
			this.receiveId = {}
			this.shopValue = ''
			this.batchNumberList = []
			this.batchNumberVal = ''
		},
		handleDownload(data, type) {
			console.log(data, type);
			retrievalList.fileBatchExport({recordIds: data.recordId}).then(res => {
				this.download(res, "application/zip", '档案信息导出数据包(请解压按照文件夹名称查看).zip')
			})
		},
		async handleExport(data, type) {

		},
		// async handleDetail(data, type) {
		//     console.log(data, type);
		//     if (data.$$data.type === '8' || data.$$data.type === '10') {
		//         const res = await retrievalList.graphFileListByMap({ id: data.$$data.id })
		//         if (res.code === 200) {
		//             this.receiveId = { id: res.data }
		//             this.openView = true
		//         } else {
		//             this.msgError('未查询到相关数据')
		//         }
		//     }
		// },
		async handleOtherDetail(data, type) {
			this.receiveId.id = data.$$data.recordId
			this.openView = true
			// const objParams = {
			//     1: 'recordId',
			//     2: 'manufacture',
			// }
			// const res = await retrievalList.graphFileListByMap({ id: data.$$data.recordId })
			// if (res.code === 200) {
			//     this.receiveId = { id: data.$$data.recordId }
			//     this.openView = true
			// } else {
			//     this.msgError('未查询到相关数据')
			// }
		},
		// 切换验证报告显示状态
		async toggleVerificationReport(record, index) {
			// 切换显示状态
			record.showVerificationReport = !record.showVerificationReport;

			// 如果是展开状态且还没有加载验证报告数据，则加载数据
			if (record.showVerificationReport && (!record.verificationReports || record.verificationReports.length === 0)) {
				try {
					this.loading = true;

					// TODO: 需要后端提供获取验证报告的API接口
					// 接口路径建议: /archive/transport/verification/list
					// 参数: { recordId: record.recordId, orderNo: record.order_no }
					// 返回格式: { code: 200, data: [{ archive_name: '', archive_no: '', id: '' }] }

					// 暂时使用模拟数据进行演示
					const mockData = [
						{
							id: '1',
							archive_name: '冷链运输验证报告A251M样品123',
							archive_no: 'ZL+ZZ4+4+2025-03-2',
							operation: '查看'
						},
						{
							id: '2',
							archive_name: '保温AB8报告123',
							archive_no: 'ZL+ZZ4+4+2025-03-3',
							operation: '查看'
						},
						{
							id: '3',
							archive_name: '设备12388报告123',
							archive_no: 'ZL+ZZ4+4+2025-03-4',
							operation: '查看'
						}
					];

					// 模拟API调用延迟
					await new Promise(resolve => setTimeout(resolve, 500));

					record.verificationReports = mockData;

					// 实际API调用代码（需要后端提供接口后启用）
					/*
					const res = await retrievalList.getVerificationReports({
						recordId: record.recordId,
						orderNo: record.order_no
					});

					if (res.code === 200 && res.data) {
						record.verificationReports = res.data;
					} else {
						record.verificationReports = [];
						this.msgError('暂无验证报告数据');
					}
					*/
				} catch (error) {
					console.error('获取验证报告失败:', error);
					record.verificationReports = [];
					this.msgError('获取验证报告失败');
				} finally {
					this.loading = false;
				}
			}
		},
		// 查看验证报告详情
		handleVerificationDetail(reportData) {
			// 这里可以打开验证报告详情弹窗或跳转到详情页面
			console.log('查看验证报告详情:', reportData);
			// 可以复用现有的详情查看逻辑
			this.receiveId.id = reportData.id || reportData.archive_id;
			this.openView = true;
		},
		// 下载验证报告
		handleVerificationDownload(reportData) {
			// 这里处理验证报告的下载逻辑
			console.log('下载验证报告:', reportData);
			// 可以复用现有的下载逻辑
			this.handleDownload(reportData, '2');
		},
		handleWheel(event) {
			event.stopPropagation();
		},
		// 关闭查看
		parentView() {
			this.openView = false;
		},
		async existenceRetrieval(list, txt) {
			let isHas = false
			if (list?.length) {
				for (let i = 0; i < list.length; i++) {
					if (list[i].label === txt) {
						isHas = true
						break
					}
				}
			} else {
				isHas = false
			}
			return isHas
		},
		async filterBatchNumber(newStr) {
			if (!newStr) {
				this.filterBatchNumberList = this.batchNumberList
			} else {
				//过滤批号列表
				this.filterBatchNumberList = this.batchNumberList.filter(item => item.batch_number.includes(newStr));
			}
		},
		async handleClick(type, data, index) {
			if (type === '4') {
				if (!data.$$data.batch_number) return this.msgError('请选择批号！')
				const val = await this.existenceRetrieval(data.$$data.children, '采购订单')
				if (val) return
				this.loading = true
				const res = await retrievalList.graphListByMap({
					id: data.$$data.id,
					batch_number: data.$$data.batch_number,
					commodity_id: data.$$data.id,
					type: '4'
				})
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}
			if (type === '5') {
				if (!data.$$data.batch_number) return this.msgError('请选择批号！')
				const val = await this.existenceRetrieval(data.$$data.children, '销售订单')
				if (val) return
				this.loading = true
				const res = await retrievalList.graphListByMap({
					id: data.$$data.id,
					batch_number: data.$$data.batch_number,
					commodity_id: data.$$data.id,
					type: '5'
				})
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}
			// 入库记录
			if (type === '6') {
				const val = await this.existenceRetrieval(data.$$data.children, '入库记录')
				if (val) return
				this.loading = true
				const res = await retrievalList.graphListByMap({
					id: data.$$data.id,
					order_code: data.$$data.order_code,
					type: '6'
				})
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}
			// 出库记录
			if (type === '9') {
				const val = await this.existenceRetrieval(data.$$data.children, '出库记录')
				if (val) return
				this.loading = true
				const res = await retrievalList.graphListByMap({
					id: data.$$data.id,
					doc_num: data.$$data.doc_num,
					type: '9'
				})
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}
			// 商品
			if (type === '7') {
				const val = await this.existenceRetrieval(data.$$data.children, '商品')
				if (val) return
				this.loading = true
				const params = {id: data.$$data.id, type: '7'}
				if (index === '1') {
					params.order_code = data.$$data.order_code
				}
				if (index === '2') {
					params.doc_num = data.$$data.doc_num
				}
				const res = await retrievalList.graphListByMap(params)
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}
			// 供应商
			if (type === '8') {
				const val = await this.existenceRetrieval(data.$$data.children, '供应商')
				if (val) return
				this.loading = true
				const res = await retrievalList.graphListByMap({
					id: data.$$data.id,
					supplier: data.$$data.supplier,
					type: '8'
				})
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}
			// 客户
			if (type === '10') {
				const val = await this.existenceRetrieval(data.$$data.children, '客户')
				if (val) return
				this.loading = true
				const res = await retrievalList.graphListByMap({
					id: data.$$data.id,
					customer_id: data.$$data.customer_id,
					type: '10',
					customer_name: data.$$data.customer_name
				})
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}
			// 运输记录
			if (type === '11') {
				const val = await this.existenceRetrieval(data.$$data.children, '运输记录')
				if (val) return
				this.loading = true
				const res = await retrievalList.graphListByMap({
					id: data.$$data.id,
					doc_num: data.$$data.doc_num,
					type: '11'
				})
				if (res.code === 200 && res.data.length) {
					if (Array.isArray(data.$$data.children)) {
						data.$$data.children.push(...res.data)
					} else {
						data.$$data.children = res.data
					}
				} else {
					this.msgError('暂无数据！')
				}
				// this.toggleExpand(this.data, true);
				this.loading = false
			}

		},
		// 商品change
		async changeShop(data, row) {
			this.loading = true
			//
			const res = await retrievalList.graphListByMap({
				package_specification: row.package_specification,
				parentId: data.pid,
				id: row.id,
				common_name: row.common_name,
				manufacture: row.manufacture,
				manufacture_name: row.manufacture_name,
				type: '2'
			})
			if (res.code === 200 && res.data.length) {
				this.data.children = [res.data[0]]
			} else {
				this.msgError('没有查询到相关数据！')
			}
			// this.toggleExpand(this.data, true);
			this.loading = false
		},
		// 打开批号弹窗
		async batchNumberClickOpen(data) {
			this.loading = true
			this.batchSearchStr = ''
			console.log(data);
			const res = await retrievalList.graphListByMap({commodity_id: data.id, type: '3'})
			if (res.code === 200) {
				this.batchNumberList = res.data?.[0].data
				this.filterBatchNumberList = this.batchNumberList;
			}
			this.loading = false
			this.open = true
		},
		// 批号确定
		batchNumberSubmit() {
			if (!this.batchNumberVal) return this.msgError('请选择批号！')
			this.data.children[0] = {...this.data.children[0], batch_number: this.batchNumberVal, children: []}
			this.open = false
		},
		close() {
			this.open = false
		},
		toggleExpand(data, val) {
			if (Array.isArray(data)) {
				data.forEach((item) => {
					item.expand = val
					if (item.children) {
						this.toggleExpand(item.children, val);
					}
				});
			} else {
				data.expand = val
				if (data.children) {
					this.toggleExpand(data.children, val);
				}
			}
		},
		//数据导出
		exportTableToExcel(data, headers, title) {
			// 创建一个新的工作簿和工作表
			const workbook = new ExcelJS.Workbook();
			const worksheet = workbook.addWorksheet(title);

			// 添加标题行
			worksheet.addRow(headers.map(header => header.title));

			// 设置标题行的样式
			const headerRow = worksheet.getRow(1);
			headerRow.eachCell(cell => {
				cell.font = {bold: true};
				cell.border = {
					top: {style: 'thin'},
					left: {style: 'thin'},
					bottom: {style: 'thin'},
					right: {style: 'thin'}
				};
				// 垂直居中和水平居中
				cell.alignment = {vertical: 'middle', horizontal: 'center'};
			});

			// 添加数据行
			data.forEach(rowData => {
				const row = worksheet.addRow(headers.map(header => {
					if (header.key.includes('time') || header.key.includes('date')) {
						return this.dateFormatting(rowData[header.key]);
					} else {
						return rowData[header.key];
					}
				}));
				row.eachCell(cell => {
					cell.border = {
						top: {style: 'thin'},
						left: {style: 'thin'},
						bottom: {style: 'thin'},
						right: {style: 'thin'}
					};
					// 垂直居中和左对齐
					cell.alignment = {vertical: 'middle', horizontal: 'left'};
				});
			});

			// 自动调整列宽
			worksheet.columns.forEach(column => {
				let maxLength = 0;
				column.eachCell((cell) => {
					if (cell.type === 3) {
						maxLength = Math.max(maxLength, cell.text ? cell.text.toString().length : 10);
					} else {
						maxLength = Math.max(maxLength, `${cell.value}`.toString().length);
					}
				});
				column.width = maxLength < 10 ? 10 : maxLength + 2;
			});

			// 导出 Excel 文件
			workbook.xlsx.writeBuffer().then(buffer => {
				const dataBlob = new Blob([buffer], {type: 'application/octet-stream'});
				saveAs(dataBlob, `${title}列表导出.xlsx`);
			}).catch(error => {
				console.error('Error generating Excel file:', error);
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.custom-content {
	display: flex;
	justify-content: flex-start;
	margin-bottom: 6px;
}

::v-deep .el-checkbox.el-checkbox--default {
	display: none;
}

::v-deep .tree-org-node__content .tree-org-node__text {
	padding: 0;
	border-radius: 10px 10px 0px 0px;
}

::v-deep .shopList {
	border-top: 6px solid #3A8CFF;

}

::v-deep .tree-org-node__expand {
	border: none;
	box-shadow: none;
}
::v-deep .tree-org-node__inner {
    border-radius: 10px 10px 0px 0px;
}

.expandTrue,
.expandFalse {
	width: 20px;
	height: 20px;
	background: #A0B5C7;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 运输记录卡片样式 */
.transport-record-card {
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.transport-record-card:hover {
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
	transform: translateY(-1px);
}

.transport-record-card .el-button {
	transition: all 0.2s ease;
}

.transport-record-card .el-button:hover {
	transform: scale(1.05);
}
</style>
