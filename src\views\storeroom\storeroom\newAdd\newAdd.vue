<template>
	<el-form ref="form" :model="form" :rules="rules" label-width="140px" style="padding-right: 20px;">
		<template v-if="filesType === '1'">
			<el-form-item label="档案库名称" prop="houseName">
				<el-input v-model="form.houseName" clearable placeholder="请输入档案库名称"/>
			</el-form-item>
			<el-form-item label="档案库编号" prop="houseNum">
				<el-input v-model="form.houseNum" clearable placeholder="请输入档案库编号"/>
			</el-form-item>
			<!-- <el-form-item label="所属区域" prop="recordHouseRegion">
				<el-input v-model="form.recordHouseRegion" placeholder="请输入所属区域" />
			</el-form-item> -->
			<el-form-item label="档案库面积" prop="houseArea">
				<el-input v-model="form.houseArea" clearable placeholder="请输入档案库面积"/>
			</el-form-item>
			<el-form-item label="上传结构图片" prop="uploadViewImgCoverUrlFile">
				<el-upload ref="qualityCodeRefCover" v-model:file-list="form.uploadViewImgCoverUrlFile" :accept="'image/gif, image/jpeg, image/png'" :action="uploadUrl"
						   :before-remove="beforeRemove" :class="[form.uploadViewImgCoverUrlFile.length===1?'hide-upload-btn':'']" :data="{ fileType: 'image', fjType: '档案库', zhType: '111' }" :disabled="modalType === 'detail'"
						   :headers='headers' :limit="1"
						   :on-change="uploadChange" :on-exceed="handleFileSizeOutSetting"
						   :on-preview="handlePictureCoverView"
						   :on-success="(res, file, filList) => handleUploadSuccessCover(res, file, filList, undefined, 3)"
						   class="upload-demo" list-type="picture-card">
					<slot>
						<el-icon>
							<el-icon-plus/>
						</el-icon>
					</slot>
					<template #file="{ file }">
						<div>
							<img :src="file.url" alt="" class="el-upload-list__item-thumbnail"/>
							<span class="el-upload-list__item-actions">
								<span class="el-upload-list__item-preview" @click="handlePictureCoverView(file)">
									<el-icon><el-icon-zoom-in/></el-icon>
								</span>
								<span v-if="!disabled" class="el-upload-list__item-delete"
									  @click="handleRemove(file, 1)">
									<el-icon><el-icon-delete/></el-icon>
								</span>
							</span>
						</div>
					</template>
				</el-upload>
			</el-form-item>
			<el-form-item label="上传平面图片" prop="uploadViewImgUrlFile">
				<el-upload ref="qualityCodeRefPlane" v-model:file-list="form.uploadViewImgUrlFile" :accept="'image/gif, image/jpeg, image/png'" :action="uploadUrl"
						   :before-remove="beforeRemove" :class="[form.uploadViewImgUrlFile.length===1?'hide-upload-btn':'']" :data="{ fileType: 'image', fjType: '档案库', zhType: '111' }" :disabled="modalType === 'detail'"
						   :headers='headers' :limit="1"
						   :on-exceed="handleFileSizeOutSetting"
						   :on-preview="handlePictureCardPreview" :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 3)"
						   class="upload-demo"
						   list-type="picture-card">
					<slot>
						<el-icon>
							<el-icon-plus/>
						</el-icon>
					</slot>
					<template #file="{ file }">
						<div>
							<img :src="file.url" alt="" class="el-upload-list__item-thumbnail"/>
							<span class="el-upload-list__item-actions">
								<span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
									<el-icon><el-icon-zoom-in/></el-icon>
								</span>
								<span v-if="!disabled" class="el-upload-list__item-delete"
									  @click="handleRemove(file, 2)">
									<el-icon><el-icon-delete/></el-icon>
								</span>
							</span>
						</div>
					</template>
				</el-upload>
			</el-form-item>
			<el-form-item label="管理员所属组织机构" prop="houseManagerOrg">
				<el-select
					v-model="houseManagerOrg"
					class="m-2"
					clearable
                    disabled
					filterable
					placeholder="请选择所属组织机构"
					size="default"
					@change="managerChange"
				>
					<el-option
						v-for="(item, index) in orgList"
						:key="index"
						:label="item.name"
						:value="item.id"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="档案库管理员" prop="users">
				<el-select
					v-model="form.houseManagerId"
					:disabled="houseManagerOrg == ''"
					:reserve-keyword="false"
					allow-create
					default-first-option
					filterable
                    clearable
					placeholder="请选择档案库管理员"
					size="default"
					style="width: 100%;"
				>
					<div style="padding: 10px">
						<el-input
							v-model="userInfo.value"
							:prefix-icon="Search"
							class="w-50 m-2"
							placeholder="输入档案库管理员"
							style="margin-bottom: 10px"
							@keydown.enter="getUser()"
						/>
						<el-option
							v-for="(item, index) in personList"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
						<el-pagination
							v-model:current-page="userInfo.pageNum"
							v-model:page-size="userInfo.pageSize"
							:total="userInfo.total"
							layout="->,total, prev, pager, next, jumper"
							style="margin-top: 10px"
							@current-change="handleCurrentChange"
						/>
					</div>
				</el-select>
			</el-form-item>
		</template>

		<template v-else-if="filesType === '2'">
			<el-form-item label="档案室名称" prop="roomName">
				<el-input v-model="form.roomName" clearable placeholder="请输入档案室名称"/>
			</el-form-item>
			<el-form-item label="档案室编号" prop="roomNum">
				<el-input v-model="form.roomNum" clearable placeholder="请输入档案室编号"/>
			</el-form-item>
			<el-form-item label="档案室面积" prop="roomArea">
				<el-input v-model="form.roomArea" clearable placeholder="请输入档案室面积"/>
			</el-form-item>
			<el-form-item label="上传封面图片" prop="uploadViewImgCoverUrlFile">
				<el-upload ref="qualityCodeRefCover" v-model:file-list="form.uploadViewImgCoverUrlFile" :accept="'image/gif, image/jpeg, image/png'" :action="uploadUrl"
						   :before-remove="beforeRemove" :class="[form.uploadViewImgCoverUrlFile.length===1?'hide-upload-btn':'']" :data="{ fileType: 'image', fjType: '档案库', zhType: '111' }" :disabled="modalType === 'detail'"
						   :headers='headers' :limit="1"
						   :on-exceed="handleFileSizeOutSetting" :on-preview="handlePictureCoverView"
						   :on-success="(res, file, filList) => handleUploadSuccessCover(res, file, filList, undefined, 3)"
						   class="upload-demo"
						   list-type="picture-card">
					<slot>
						<el-icon>
							<el-icon-plus/>
						</el-icon>
					</slot>
					<template #file="{ file }">
						<div>
							<img :src="file.url" alt="" class="el-upload-list__item-thumbnail"/>
							<span class="el-upload-list__item-actions">
								<span class="el-upload-list__item-preview" @click="handlePictureCoverView(file)">
									<el-icon><el-icon-zoom-in/></el-icon>
								</span>
								<span v-if="!disabled" class="el-upload-list__item-delete"
									  @click="handleRemove(file, 1)">
									<el-icon><el-icon-delete/></el-icon>
								</span>
							</span>
						</div>
					</template>
				</el-upload>
			</el-form-item>
			<el-form-item label="档案室平面图" prop="uploadViewImgUrlFile">
				<el-upload ref="qualityCodeRefRoomPlane" v-model:file-list="form.uploadViewImgUrlFile" :accept="'image/gif, image/jpeg, image/png'" :action="uploadUrl"
						   :before-remove="beforeRemove" :class="[form.uploadViewImgUrlFile.length===1?'hide-upload-btn':'']" :data="{ fileType: 'image', fjType: '仓库', zhType: '111' }" :disabled="modalType === 'detail'"
						   :headers='headers' :limit="1"
						   :on-exceed="handleFileSizeOutSetting" :on-preview="handlePictureCardPreview"
						   :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 3)"
						   class="upload-demo"
						   list-type="picture-card">
					<slot>
						<el-icon>
							<el-icon-plus/>
						</el-icon>
					</slot>
					<template #file="{ file }">
						<div>
							<img :src="file.url" alt="" class="el-upload-list__item-thumbnail"/>
							<span class="el-upload-list__item-actions">
								<span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
									<el-icon><el-icon-zoom-in/></el-icon>
								</span>
								<span v-if="!disabled" class="el-upload-list__item-delete"
									  @click="handleRemove(file, 3)">
									<el-icon><el-icon-delete/></el-icon>
								</span>
							</span>
						</div>
					</template>
				</el-upload>
			</el-form-item>
			<el-form-item label="管理员所属组织机构" prop="roomManagerOrg">
				<el-select
					v-model="roomManagerOrg"
					class="m-2"
                    disabled
					clearable
					filterable
					placeholder="请选择所属组织机构"
					size="default"
					@change="managerChange"
				>
					<el-option
						v-for="(item, index) in orgList"
						:key="index"
						:label="item.name"
						:value="item.id"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="档案室管理员" prop="users">
				<el-select
					v-model="form.roomManagerId"
					:disabled="roomManagerOrg == ''"
					:reserve-keyword="false"
					allow-create
					default-first-option
					filterable
                    clearable
					placeholder="请选择档案室管理员"
					size="default"
					style="width: 100%;"
				>
					<div style="padding: 10px">
						<el-input
							v-model="userInfo.value"
							:prefix-icon="Search"
							class="w-50 m-2"
							placeholder="输入档案室管理员"
							style="margin-bottom: 10px"
							@keydown.enter="getUser()"
						/>
						<el-option
							v-for="(item, index) in personList"
							:key="index"
							:label="item.name"
							:value="item.id"
						/>
						<el-pagination
							v-model:current-page="userInfo.pageNum"
							v-model:page-size="userInfo.pageSize"
							:total="userInfo.total"
							layout="->,total, prev, pager, next, jumper"
							style="margin-top: 10px"
							@current-change="handleCurrentChange"
						/>
					</div>
				</el-select>
			</el-form-item>
		</template>

		<template v-else-if="filesType === '3'">
			<el-form-item label="档案柜名称:" prop="containerName">
				<el-input v-model="form.containerName" clearable placeholder="请输入档案柜名称"/>
			</el-form-item>
			<el-form-item label="档案柜编号:" prop="containerNum">
				<el-input v-model="form.containerNum" clearable placeholder="请输入档案柜编号"/>
			</el-form-item>
			<el-form-item label="档案柜类型:" prop="containerType">
				<el-select v-model="form.containerType" clearable filterable placeholder="请选择档案柜类型" style="width: 100%;">
					<el-option label="固定栏位" value="1"/>
					<el-option label="不固定栏位" value="2"/>
				</el-select>
			</el-form-item>
			<el-form-item label="柜面类型:" prop="containerCounterType">
				<el-select v-model="form.containerCounterType" clearable filterable placeholder="请选择档柜面类型" style="width: 100%;">
					<el-option label="单面" value="1"/>
					<el-option label="双面" value="2"/>
				</el-select>
			</el-form-item>
			<el-form-item label="所属档案室区域:" prop="containerRegion">
				<el-input v-model="form.containerRegion" clearable placeholder="请输入所属档案室区域"/>
			</el-form-item>
			<el-form-item label="档案柜层数:" prop="containerFloors">
				<el-input v-model="form.containerFloors" clearable placeholder="请输入档案柜层数"/>
			</el-form-item>
			<el-form-item label="层起始位置:" prop="containerFloorsStart">
				<el-select v-model="form.containerFloorsStart" clearable filterable placeholder="请选择层起始位置" style="width: 100%;">
					<el-option label="从上到下" value="1"/>
					<el-option label="从下到上" value="2"/>
				</el-select>
			</el-form-item>
			<el-form-item label="档案柜节数:" prop="containerPitch">
				<el-input v-model="form.containerPitch" clearable placeholder="请输入档案柜节数"/>
			</el-form-item>
			<el-form-item label="节起始位置:" prop="containerPitchStart">
				<el-select v-model="form.containerPitchStart" clearable filterable placeholder="请选择节起始位置" style="width: 100%;">
					<el-option label="从左到右 " value="1"/>
					<el-option label="从右到左" value="2"/>
				</el-select>
			</el-form-item>
			<el-form-item v-if="this.storageNum" label="额定容量:" prop="containerUpStorageNum">
				<el-input v-model="form.containerUpStorageNum" clearable placeholder="请输入额定容量"/>
			</el-form-item>
		</template>

	</el-form>
	<div style="text-align: center; padding-top: 20px;">
		<el-button type="primary" @click="submitForm">确 定</el-button>
		<el-button @click="getRecordList">取 消</el-button>
	</div>

	<!-- 图片预览弹窗 -->
	<el-dialog v-model="uploadVisible" center title="图片预览" width="60%">
		<div style="text-align: center;">
			<el-image
				:preview-src-list="[uploadViewImgCoverUrl]"
				:src="uploadViewImgCoverUrl"
				fit="contain"
				preview-teleported
				style="max-width: 100%; max-height: 500px;"
			/>
		</div>
	</el-dialog>
</template>

<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
import tool from '@/utils/tool';
import {getCurrentInstance} from "vue";
import {node} from "@/api/model/systemDeploy/auditAndFlow";
import {ElMessage} from "element-plus";
import {getOrganizationInfo} from "@/utils/permission";

export default {
	name: 'newAdd',
	props: ['dialogCreate', 'filesType', 'ID', 'mainID', 'parentId', 'parentTargetId', 'viewType'],
	data() {
		return {
            org:getOrganizationInfo(), // 当前组织机构
			rules: {
				houseName: [
					{required: true, message: '请输入档案库名称', trigger: 'blur'}
				],
				houseNum: [
					{required: true, message: '请输入档案库编号', trigger: 'blur'},
					{pattern: /^[a-zA-Z0-9-]*$/, message: '不允许输入中文', trigger: 'blur'}
				],
				houseArea: [
					{required: true, message: '请输入档案库面积', trigger: 'blur'},
					{pattern: /^[0-9]*$/, message: '档案库面积数字值', trigger: 'blur'},
                    {
                        validator: (_rule, data, callback) => {
                            if(data > 0){
                                callback();
                            } else {
                                callback(new Error("档案库面积必须大于0"));
                            }
                        }
                    , trigger: 'change'
                    }
                    //

				],
                uploadViewImgCoverUrlFile: [
					{required: true, message: '请上传结构图片', trigger: 'change',type:'array'},
				],
                uploadViewImgUrlFile: [
					{required: true, message: '请上传平面图片', trigger: 'change',type:'array'}
				],
				houseManagerId: [
					{required: true, message: '请输入档案库管理员姓名', trigger: 'blur'}
				],
				roomName: [
					{required: true, message: '请输入档案室名称', trigger: 'blur'}
				],
				roomNum: [
					{required: true, message: '请输入档案室编号', trigger: 'blur'},
					{pattern: /^[a-zA-Z0-9-]*$/, message: '不允许输入中文', trigger: 'blur'}
				],
				roomArea: [
					{required: true, message: '请输入档案室面积', trigger: 'blur'},
					{pattern: /^[0-9]*$/, message: '档案室面积数字值', trigger: 'blur'},
                    {
                        validator: (_rule, data, callback) => {
                            if(data > 0){
                                callback();
                            } else {
                                callback(new Error("档案室面积必须大于0"));
                            }
                        }
                        , trigger: 'change'
                    },
					{
						validator: (_rule, data, callback) => {
							let wareArea = '';
							let roomArea = 0;

							storeroomData.queryById({
								id: this.mainID === '' ? this.parentTargetId : this.roomHouseId
							}).then(res => {
								if (res.code === 200) {
									wareArea = res.data.houseArea;
								}

								storeroomData.roomList({
									size: -1,
									page: 1,
									'roomHouse.id': this.mainID === '' ? this.parentTargetId : this.roomHouseId
								}).then(result => {
									if (result.code === 200) {
										result.data.records.forEach(item => {
											if (this.ID) {
												if (item.id === this.ID) {
													if (item.roomArea !== data) {
														roomArea = parseInt(roomArea) + parseInt(data);
													} else {
														roomArea = parseInt(roomArea) + parseInt(item.roomArea);
													}
												} else {
													roomArea = parseInt(roomArea) + parseInt(item.roomArea);
												}
											} else {
												roomArea = parseInt(roomArea) + parseInt(item.roomArea);
											}
										});

										if (!this.ID) {
											roomArea = parseInt(roomArea) + parseInt(data);
										}

										if (parseInt(wareArea) < parseInt(roomArea)) {
											callback(new Error("已超出的档案仓库设定面积"));
										} else {
											callback();
										}
									} else {
										callback(new Error("获取档案室列表失败"));
									}
								}).catch(error => {
									console.error('获取档案室列表失败:', error);
									callback(new Error("获取档案室列表失败"));
								});
							}).catch(error => {
								console.error('获取档案库信息失败:', error);
								callback(new Error("获取档案库信息失败"));
							});
						}, trigger: 'change'
					},
				],
				roomManagerId: [
					{required: true, message: '请输入档案室管理员姓名', trigger: 'blur'}
				],
				containerName: [
					{required: true, message: '请输入档案柜名称', trigger: 'blur'}
				],
				containerNum: [
					{required: true, message: '请输入档案柜编号', trigger: 'blur'},
					{pattern: /^[a-zA-Z0-9-]*$/, message: '不允许输入中文', trigger: 'blur'}
				],
				containerType: [
					{required: true, message: '请选择档案柜类型', trigger: 'blur'}
				],
				containerCounterType: [
					{required: true, message: '请选择档柜面类型', trigger: 'blur'}
				],
				containerRegion: [
					{required: true, message: '请输入所属档案室区域', trigger: 'blur'}
				],
				containerFloors: [
					{required: true, message: '请输入档案柜层数', trigger: 'blur'},
					{pattern: /^[0-9]*$/, message: '档案柜层数数字值', trigger: 'blur'},
                    {
                        validator: (_rule, data, callback) => {
                            if(data > 0){
                                callback();
                            } else {
                                callback(new Error("档案柜层数必须大于0"));
                            }
                        }
                        , trigger: 'change'
                    }
				],
				containerFloorsStart: [
					{required: true, message: '请选择层起始位置', trigger: 'blur'}
				],
				containerPitch: [
					{required: true, message: '请输入档案柜节数', trigger: 'blur'},
					{pattern: /^[0-9]*$/, message: '档案柜节数数字值', trigger: 'blur'},
                    {
                        validator: (_rule, data, callback) => {
                            if(data > 0){
                                callback();
                            } else {
                                callback(new Error("档案柜节数必须大于0"));
                            }
                        }
                        , trigger: 'change'
                    }
				],
				containerPitchStart: [
					{required: true, message: '请选择节起始位置', trigger: 'blur'}
				],
				containerUpStorageNum: [
					{required: true, message: '请输入额定容量', trigger: 'blur'},
					{pattern: /^[0-9]*$/, message: '额定容量数字值', trigger: 'blur'},
                    {
                        validator: (_rule, data, callback) => {
                            if(data > 0){
                                callback();
                            } else {
                                callback(new Error("额定容量必须大于0"));
                            }
                        }
                        , trigger: 'change'
                    }
				],
			},
			roomHouseId: '',
			parentInfo: {},
			form: {},
			// 图片上传
			uploadViewImgCoverUrl: '',
			uploadViewImgUrl: '',
			uploadVisible: false,
			modalType: '',
			uploadUrl: process.env.VUE_APP_API_UPLOAD,
			headers: {
				Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
				ContentType: 'multipart/form-data',
				clientType: "PC"
			},
			associationID: '',
			message: getCurrentInstance(),
			// 编辑时不显示额定容量
			storageNum: '1',
			orgList: [],
			personList: [],
			houseManagerOrg: '',
			roomManagerOrg: '',
			userInfo: {
				id: "",
				pageNum: 1,
				size: 10,
				total: 0,
				value: "",
				editStr: {},
			}
		}
	},
	watch: {
		// 监听文件类型变化，重置表单
		filesType: {
			handler(newVal, oldVal) {
				if (newVal !== oldVal) {
					this.form = this.initForm();
					this.houseManagerOrg = this.org?.id;
					this.roomManagerOrg = this.org?.id;
					this.personList = [];
					// 清除表单验证
					this.$nextTick(() => {
						if (this.$refs.form) {
							this.$refs.form.clearValidate();
						}
					});
				}
			},
			immediate: false
		}
	},
	created() {
		// 在组件创建时初始化表单数据
		this.form = this.initForm();
	},
	mounted() {
		if (this.filesType === '1' && this.ID) {
			this.queryById(this.ID);
		} else if (this.filesType === '2' && this.ID) {
			this.roomQueryById(this.ID);
		} else if (this.filesType === '3' && this.ID) {
			this.containerById(this.ID);
		}
		node.tissue().then((res) => {
			this.orgList = res.data.records;
		}).catch(error => {
			console.error('获取组织机构失败:', error);
			ElMessage.error('获取组织机构失败');
		});
	},
	methods: {
		// 初始化表单数据
		initForm() {
            this.houseManagerOrg = this.org?.id;
            this.roomManagerOrg = this.org?.id;
            this.managerChange(this.org?.id);
			return {
				// 档案库相关字段
				houseName: '',
				houseNum: '',
				houseArea: '',
				houseManagerId: '',
				uploadViewImgCoverUrlFile: [],
				uploadViewImgUrlFile: [],

				// 档案室相关字段
				roomName: '',
				roomNum: '',
				roomArea: '',
				roomManagerId:'',

				// 档案柜相关字段
				containerName: '',
				containerNum: '',
				containerType: '',
				containerCounterType: '',
				containerRegion: '',
				containerFloors: '',
				containerFloorsStart: '',
				containerPitch: '',
				containerPitchStart: '',
				containerUpStorageNum: ''
			};
		},

		handleFileSizeOutSetting() {
			ElMessage({
				message: '最多只能上传一张',
				type: 'success',
			})
		},


		getUser(id) {
			node.staff({
				"sysOrg.id": this.houseManagerOrg || this.roomManagerOrg,
				current: this.userInfo.pageNum,
				size: this.userInfo.pageSize,
				name: this.userInfo.value,
				id: id,
			}).then((res) => {
				this.userInfo.total = res.data.total;
				this.personList = res.data.records;
			}).catch(error => {
				console.error('获取人员列表失败:', error);
				ElMessage.error('获取人员列表失败');
			});
		},
		handleCurrentChange(val) {
			this.userInfo.pageNum = val;
			this.form.houseManagerId = '';
			this.form.roomManagerId = '';
			this.getUser(null);
		},
		managerChange(orgId) {
			this.form.houseManagerId = '';
			this.form.roomManagerId = '';
			node.staff({"sysOrg.id": orgId}).then((res) => {
				this.personList = res.data.records;
				this.userInfo.total = res.data.total;
			}).catch(error => {
				console.error('获取人员列表失败:', error);
				ElMessage.error('获取人员列表失败');
			});
		},
		handlePictureCardPreview(uploadFile) {
			this.uploadViewImgUrl = uploadFile.url
			this.uploadVisible = true
		},
		handlePictureCoverView(uploadFile) {
			this.uploadViewImgCoverUrl = uploadFile.url
			this.uploadVisible = true
		},
		handleUploadSuccess(res, _file, fileList, _index, type) {
			if (res.code === 200) {
				if (type === 3) {
					fileList.forEach(file => {
						file.url = res.data.url;
					})
					this.$refs.form.clearValidate('uploadViewImgUrlFile');
				}
			}
		},
		handleUploadSuccessCover(res, _file, fileList, _index, type) {
			if (res.code === 200) {
				if (type === 3) {
					fileList.forEach(file => {
						file.url = res.data.url;
					})
					this.$refs.form.clearValidate('uploadViewImgCoverUrlFile');
				}
			}
		},
		beforeRemove() {
			return this.$confirm(`确定要删除图片么?`, '提示', {
				type: 'warning',
			}).then(() => {
				return true
			}).catch(() => {
				return false
			})
		},
		handleRemove(file, _index) {
			switch (_index) {
				case 1:
					this.$refs.qualityCodeRefCover.handleRemove(file);
					break;
				case 2:
					this.$refs.qualityCodeRefPlane.handleRemove(file);
					break;
				case 3:
					this.$refs.qualityCodeRefRoomPlane.handleRemove(file);
					break;
			}
		},
		submitForm() {
			if (this.filesType === '1') {
				if (this.ID) {
					this.$refs.form.validate((valid) => {
						if (valid) {
							let submitData = {
								id: this.ID,
								houseName: this.form.houseName,
								houseNum: this.form.houseNum,
								houseCoverUrl: this.form.uploadViewImgCoverUrlFile[0].url,
								houseArea: this.form.houseArea,
								housePlaneUrl: this.form.uploadViewImgUrlFile[0].url,
								houseManager: {
									id: this.form.houseManagerId
								}
							}
							storeroomData.submitSave(submitData).then(async res => {
								if (res.code === 200) {
									this.associationID = res.data.id;
									await this.hostSave(this.associationID);
									this.$emit('openNewAdd');
								}
							}).catch(error => {
								console.error('保存档案库失败:', error);
								ElMessage.error('保存档案库失败');
							})
						} else {
							return false;
						}
					});

				} else {
					this.$refs.form.validate((valid) => {
						if (valid) {
							let submitData = {
								id: this.ID,
								houseName: this.form.houseName,
								houseNum: this.form.houseNum,
								houseCoverUrl: this.form.uploadViewImgCoverUrlFile.length > 0 ? this.form.uploadViewImgCoverUrlFile[0].response.data.url : '',
								houseArea: this.form.houseArea,
								housePlaneUrl: this.form.uploadViewImgUrlFile.length > 0 ? this.form.uploadViewImgUrlFile[0].response.data.url : '',
								houseManager: {
									id: this.form.houseManagerId
								}
							}
							storeroomData.submitSave(submitData).then(async res => {
								if (res.code === 200) {
									this.associationID = res.data.id;
									await this.hostSave(this.associationID);
									this.$emit('openNewAdd');
								}
							}).catch(error => {
								console.error('保存档案库失败:', error);
								ElMessage.error('保存档案库失败');
							})
						} else {
							return false;
						}
					});
				}
			} else if (this.filesType === '2') {
				if (this.ID) {
					this.$refs.form.validate((valid) => {
						if (valid) {
							let submitData = {
								id: this.ID,
								roomNum: this.form.roomNum,
								roomName: this.form.roomName,
								roomHouse: {
									id: this.roomHouseId
								},
								roomArea: this.form.roomArea,
								roomPlaneUrl: this.form.uploadViewImgUrlFile[0].url,
								roomCoverUrl: this.form.uploadViewImgCoverUrlFile[0].url,
								roomManager: {
									id: this.form.roomManagerId
								}
							}
							storeroomData.roomSave(submitData).then(async res => {
								if (res.code === 200) {
									this.parentInfo.id = this.parentId;
									this.associationID = res.data.id;
									await this.hostSave(this.associationID, this.parentInfo.id);
									this.$emit('openNewAdd');
								}
							}).catch(error => {
								console.error('保存档案室失败:', error);
								ElMessage.error('保存档案室失败');
							})
						} else {
							return false;
						}
					});

				} else {
					this.$refs.form.validate((valid) => {
						if (valid) {
							let submitData = {
								id: this.ID,
								roomNum: this.form.roomNum,
								roomName: this.form.roomName,
								roomArea: this.form.roomArea,
								roomHouse: {
									id: this.parentTargetId
								},
								roomCoverUrl: this.form.uploadViewImgCoverUrlFile.length > 0 ? this.form.uploadViewImgCoverUrlFile[0].response.data.url : '',
								roomPlaneUrl: this.form.uploadViewImgUrlFile.length > 0 ? this.form.uploadViewImgUrlFile[0].response.data.url : '',
								roomManager: {
									id: this.form.roomManagerId
								}
							}
							storeroomData.roomSave(submitData).then(async res => {
								if (res.code === 200) {
									this.parentInfo.id = this.parentId;
									this.associationID = res.data.id;
									await this.hostSave(this.associationID, this.parentInfo.id);
									this.$emit('openNewAdd');
								}
							}).catch(error => {
								console.error('保存档案室失败:', error);
								ElMessage.error('保存档案室失败');
							})
						} else {
							return false;
						}
					});
				}
			} else if (this.filesType === '3') {
				if (this.ID) {
					this.$refs.form.validate((valid) => {
						if (valid) {
							if (this.ID) {
								let submitData = {
									id: this.ID,
									admsRoomDTO: {
										id: this.parentTargetId
									},
									containerNum: this.form.containerNum,
									containerName: this.form.containerName,
									containerType: this.form.containerType,
									containerCounterType: this.form.containerCounterType,
									containerRegion: this.form.containerRegion,
									containerFloors: this.form.containerFloors,
									containerFloorsStart: this.form.containerFloorsStart,
									containerPitch: this.form.containerPitch,
									containerPitchStart: this.form.containerPitchStart,
									containerUpStorageNum: this.form.containerUpStorageNum
								}
								storeroomData.containerSave(submitData).then(async res => {
									if (res.code === 200) {
										this.parentInfo.id = this.parentId;
										this.associationID = res.data.id;
										await this.hostSave(this.associationID, this.parentInfo.id);
										this.$emit('openNewAdd');
									}
								}).catch(error => {
									console.error('保存档案柜失败:', error);
									ElMessage.error('保存档案柜失败');
								})
							} else {
								let submitData = {
									admsRoomDTO: {
										id: this.parentTargetId
									},
									containerNum: this.form.containerNum,
									containerName: this.form.containerName,
									containerType: this.form.containerType,
									containerCounterType: this.form.containerCounterType,
									containerRegion: this.form.containerRegion,
									containerFloors: this.form.containerFloors,
									containerFloorsStart: this.form.containerFloorsStart,
									containerPitch: this.form.containerPitch,
									containerPitchStart: this.form.containerPitchStart,
									containerUpStorageNum: this.form.containerUpStorageNum
								}
								storeroomData.containerSave(submitData).then(async res => {
									if (res.code === 200) {
										this.parentInfo.id = this.parentId;
										this.associationID = res.data.id;
										await this.hostSave(this.associationID, this.parentInfo.id);
										this.$emit('openNewAdd');
									}
								}).catch(error => {
									console.error('保存档案柜失败:', error);
									ElMessage.error('保存档案柜失败');
								})
							}
						} else {
							return false;
						}
					});
				} else {
					this.$refs.form.validate((valid) => {
						if (valid) {
							let submitData = {
								// id: this.ID,
								admsRoomDTO: {
									id: this.parentTargetId
								},
								containerNum: this.form.containerNum,
								containerName: this.form.containerName,
								containerType: this.form.containerType,
								containerCounterType: this.form.containerCounterType,
								containerRegion: this.form.containerRegion,
								containerFloors: this.form.containerFloors,
								containerFloorsStart: this.form.containerFloorsStart,
								containerPitch: this.form.containerPitch,
								containerPitchStart: this.form.containerPitchStart,
								containerUpStorageNum: this.form.containerUpStorageNum
							}
							storeroomData.containerSave(submitData).then(async res => {
								if (res.code === 200) {
									this.parentInfo.id = this.parentId;
									this.associationID = res.data.id;
									await this.hostSave(this.associationID, this.parentInfo.id);
									this.$emit('openNewAdd');
								}
							}).catch(error => {
								console.error('保存档案柜失败:', error);
								ElMessage.error('保存档案柜失败');
							})
						} else {
							return false;
						}
					});
				}
			}
		},
		// 主表新增
		async hostSave(data, parentId) {
			let name = '';
			if (this.filesType === '1') {
				name = this.form.houseName;
				let hostData = {
					id: this.mainID,
					name: name,
					recordHouseInfoTargetId: data,
					recordHouseInfoType: 1,
					recordHouseInfoRemark: '档案库新增',
				}
				storeroomData.hostSave(hostData).then(res => {
					if (res.code === 200) {
						this.$emit('getDataList');
					}
				}).catch(error => {
					console.error('保存主表数据失败:', error);
					ElMessage.error('保存主表数据失败');
				})
			} else if (this.filesType === '2') {
				name = this.form.roomName;
				let hostData2 = {
					id: this.mainID,
					name: name,
					parent: {
						id: parentId ? parentId : "",
					},
					recordHouseInfoTargetId: data,
					recordHouseInfoType: 2,
					recordHouseInfoRemark: '档案库新增',
				}
				storeroomData.hostSave(hostData2).then(res => {
					if (res.code === 200) {
						this.$emit('getDataList');
					}
				}).catch(error => {
					console.error('保存主表数据失败:', error);
					ElMessage.error('保存主表数据失败');
				})
			} else if (this.filesType === '3') {
				name = this.form.containerName;
				let hostData3 = {
					id: this.mainID,
					name: name,
					parent: {
						id: parentId ? parentId : "",
					},
					recordHouseInfoTargetId: data,
					recordHouseInfoType: 3,
					recordHouseInfoRemark: '档案库新增',
				}
				storeroomData.hostSave(hostData3).then(res => {
					if (res.code === 200) {
						this.$emit('getDataList');
					}
				}).catch(error => {
					console.error('保存主表数据失败:', error);
					ElMessage.error('保存主表数据失败');
				})
			}

		},
		// 修改档案库
		async queryById(id) {
			let res = await storeroomData.queryById({id});
			if (res.code === 200) {
				if (this.filesType === '1') {
					this.form.houseNum = res.data?.houseNum;
					this.form.houseName = res.data?.houseName;
					this.form.houseRegion = res.data?.houseRegion;
					this.form.houseArea = res.data?.houseArea;
					if (res.data.housePlaneUrl) {
						this.form.uploadViewImgUrlFile.push({
							'url': res.data.housePlaneUrl,
						});
					}
					if (res.data.houseCoverUrl) {
						this.form.uploadViewImgCoverUrlFile.push({
							'url': res.data.houseCoverUrl,
						});
					}
					this.houseManagerOrg = res.data.houseManager?.sysOrg?.id;
					this.getUser(null);
					this.form.houseManagerId = res.data.houseManager?.id;
				}
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
		},

		// 修改档案室
		async roomQueryById(id) {
			let res = await storeroomData.roomQueryById({id});
			if (res.code === 200) {
				if (this.filesType === '2') {
					this.form.roomNum = res.data.roomNum;
					this.form.roomName = res.data.roomName;
					this.roomHouseId = res.data.roomHouse.id;
					this.form.roomArea = res.data.roomArea;
					if (res.data.roomPlaneUrl) {
						this.form.uploadViewImgUrlFile.push({
							'url': res.data.roomPlaneUrl,
						});
					}
					if (res.data.roomCoverUrl) {
						this.form.uploadViewImgCoverUrlFile.push({
							'url': res.data.roomCoverUrl,
						});
					}
					this.roomManagerOrg = res.data.roomManager?.sysOrg?.id;
					this.getUser(null);
					this.form.roomManagerId = res.data.roomManager.id;
				}
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
		},

		// 修改档案柜
		async containerById(id) {
			let res = await storeroomData.containerById({id});
			if (res.code === 200) {
				if (this.filesType === '3') {
					this.form.containerNum = res.data.containerNum;
					this.form.containerName = res.data.containerName;
					this.form.containerType = res.data.containerType;
					this.form.containerCounterType = res.data.containerCounterType;
					this.form.containerRegion = res.data.containerRegion;
					this.form.recordHouseRegion = res.data.recordHouseRegion;
					this.form.containerFloors = res.data.containerFloors;
					this.form.containerFloorsStart = res.data.containerFloorsStart;
					this.form.containerPitch = res.data.containerPitch;
					this.form.containerPitchStart = res.data.containerPitchStart;
					this.storageNum = res.data.containerUpStorageNum;
				}
			} else {
				this.$Response.errorNotice(res, "查询失败");
			}
		},
		// 关闭弹窗
		getRecordList() {
			// 重置表单到初始状态
			this.form = this.initForm();

			// 重置其他相关状态
			this.houseManagerOrg = this.org?.id;
			this.roomManagerOrg = this.org?.id;
			this.userInfo = {
				id: "",
				pageNum: 1,
				size: 10,
				total: 0,
				value: "",
				editStr: {},
			};
			this.personList = [];

			// 清除表单验证
			if (this.$refs.form) {
				this.$refs.form.clearValidate();
			}

			this.$emit('openNewAdd', false);
		},
	}
}
</script>

<style scoped>
/* 图片上传预览图标样式 */
.el-upload-list__item-preview {
	cursor: pointer;
	margin-right: 10px;
}

.el-upload-list__item-preview:hover {
	color: #409eff;
}

/* 图片预览弹窗样式 */
.el-dialog__body {
	padding: 20px;
}

/* 隐藏上传按钮样式 */
.hide-upload-btn .el-upload--picture-card {
	display: none;
}
</style>
