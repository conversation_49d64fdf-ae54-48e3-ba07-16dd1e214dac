<template>
	<el-container>
		<el-main class="noPadding">
            <categoryTree :is-the-first-level-clickable="true"  @clickNode="clickEven"/>
		</el-main>
		<el-footer style="display: flex;justify-content: flex-end">
			<el-button type="primary" @click="() => collect()">确定</el-button>
		</el-footer>
    </el-container>
</template>

<script setup>
import collectList from '@/api/archive/archiveReception/collect';
import {defineEmits, defineProps, getCurrentInstance, ref} from 'vue'

const {proxy} = getCurrentInstance();
const emit = defineEmits(["childEvent"]);
const props = defineProps({
    handList: {
        type: Array
    }
})
const openStatus = ref(false);
// 点击树结构的id
const clickEvenId = ref([])

function clickEven(val) {
    clickEvenId.value = val;
}

function collect() {
	let idStr = props.handList.map((v) => v.id);
	openStatus.value = true;
    //拼接的数组字符串，接口传参
	let ids = idStr.join(",");
    if (clickEvenId.value.dataType === '1') {
        collectList.infoOperate({
			infoIds: ids,
			controlGroup: {
				id: clickEvenId.value.id
			}
		}).then(res => {
            if (res.code === 200) {
                emit("childMove");
				openStatus.value = false;
                proxy.msgSuccess('移动成功');
            }
        }).catch(() => {
			openStatus.value = false;
            proxy.msgError('移动失败');
        })
    } else {
        collectList.infoOperate({
			infoIds: ids,
			controlCategory: {
				id: clickEvenId.value.id
			}
		}).then(res => {
            if (res.code === 200) {
                emit("childMove");
				openStatus.value = false;
                proxy.msgSuccess('移动成功');
            }
        }).catch(() => {
			openStatus.value = false;
            proxy.msgError('移动失败');
        })
    }
}
</script>

<style scoped></style>

