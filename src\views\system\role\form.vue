<template>
	<el-dialog
		v-model="visible"
		:title="titleName"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form
			ref="dialogForm"
			v-loading="dialogLoading"
			:disabled="mode == 'view'"
			:model="form"
			:rules="rules"
			label-width="100px"
		>
			<el-form-item label="所属机构" prop="sysOrg.name">
				<el-input
					v-model="form.sysOrg.name"
					clearable
					disabled
					placeholder="请输入所属机构"
				></el-input>
				<!-- <el-button type="text" style="padding-bottom: 0px" @click="openSysOrgDialog"
					>选择组织机构</el-button
				  > -->
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '角色名称不能为空', trigger: 'blur' }]"
				label="角色名称"
				prop="name"
			>
				<el-input v-model="form.name" clearable placeholder="请输入角色名称"></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '英文名称不能为空', trigger: 'blur' }]"
				label="英文名称"
				prop="enName"
			>
				<el-input v-model="form.enName" clearable placeholder="请输入英文名称"></el-input>
			</el-form-item>
			<el-form-item
				:rules="[{ required: true, message: '角色类型不能为空', trigger: 'blur' }]"
				label="角色类型"
				prop="roleType"
			>
				<el-input
					v-model="form.roleType"
					clearable
					placeholder="请输入角色类型"
				></el-input>
			</el-form-item>
			<el-form-item v-if="tianTunId === form.sysOrg.id" label="是否系统数据" prop="sys">
				<el-switch v-model="form.sys"/>
			</el-form-item>
			<el-form-item label="共享角色" prop="common">
				<el-switch v-model="form.common"/>
			</el-form-item>
			<el-form-item label="是否可用" prop="useable">
				<el-switch v-model="form.useable"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button
				v-if="mode != 'view'"
				:loading="isSubmiting"
				type="primary"
				@click="submit()"
			>保 存
			</el-button
			>
		</template>
		<sysOrgSelect
			v-if="sysOrgSelectDialog"
			ref="sysOrgSelected"
			:isMultiple="false"
			:selectChange="orgSelectChange"
			draggable
			@closed="sysOrgSelectDialog = false"
		></sysOrgSelect>
	</el-dialog>
</template>

<script>
import {getTiantunId} from "@/utils/permission";

export default {
	props: {
		//回调函数
		callback: {type: Function},
	},
	data() {
		return {
            tianTunId: getTiantunId(),
			//表单类型
			mode: "add",
			//表单标题
			titleName: "",
			//是否显示或隐藏表单弹框
			visible: false,
			//提交中
			isSubmiting: false,
			//弹框加载中
			dialogLoading: false,
			//表单数据
			form: {},
			//
			sysOrgSelectDialog: false,
		};
	},
	mounted() {
	},
	methods: {
		/*
		 * 添加视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:20:15
		 */
		addView(sysOrg) {
			//设置标题
			this.titleName = "添加";
			this.mode = "add";
			//显示表单
			this.visible = true;
			//释放提交按钮状态
			this.isSubmiting = false;
			//释放弹框加载
			this.dialogLoading = false;
			if (this.$ObjectUtils.isEmpty(sysOrg)) {
				//初始化表单数据结构
				this.form.sysOrg = {id: "", name: ""};
			} else {
				this.form.sysOrg = sysOrg;
			}
		},
		/*
		 * 编辑视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:20:35
		 */
		async editView(formData) {
			//设置标题
			this.titleName = "编辑";
			this.mode = "edit";
			//显示表单
			this.visible = true;
			//释放提交按钮
			this.isSubmiting = false;
			//页面加载中
			this.dialogLoading = true;
			//重新查询数据权限
			var res = await this.$API.sysRoleService.queryById(formData.id);
			if (res.code == 200) {
				//设置表单数据
				this.form = res.data;
			} else {
				this.$Response.errorNotice(res, "查询失败");
				//锁定提交按钮
				this.isSubmiting = true;
			}
			//释放页面加载中
			this.dialogLoading = false;
		},
		/*
		 * 查看视图
		 * @author: 路正宁
		 * @date: 2023-03-24 13:21:14
		 */
		view(formData) {
			//设置标题
			this.titleName = "查看";
			this.mode = "view";
			//显示表单
			this.visible = true;
			//释放提交按钮状态
			this.isSubmiting = false;
			//释放弹框加载
			this.dialogLoading = false;
			//设置表单数据
			this.form = formData;
		},
		/*
		 * 表单提交
		 * @author: 路正宁
		 * @date: 2023-03-24 14:11:20
		 */
		async submit() {
			//表单校验
			var valid = await this.$refs.dialogForm.validate().catch(() => {
			});
			if (!valid) {
				return false;
			}
			//锁定提交按钮
			this.isSubmiting = true;
			var res = await this.$API.sysRoleService.save(this.form);
			if (res.code == 200) {
				//关闭页面
				this.visible = false;
				this.$message.success("操作成功");
				//回调函数
				this.callback(res.data, this.mode);
			} else {
				this.$Response.errorNotice(res, "保存失败");
			}
			//释放提交按钮
			this.isSubmiting = false;
		},
		/*
		 * 打开组织机构选择框
		 * @author: 路正宁
		 * @date: 2023-04-03 10:27:55
		 */
		openSysOrgDialog() {
			var datas = [];
			datas.push(this.form.sysOrg);
			this.sysOrgSelectDialog = true;
			this.$nextTick(() => {
				this.$refs.sysOrgSelected.selecteds(datas);
			});
		},
		/*
		 * 组织机构弹框回调事件
		 * @author: 路正宁
		 * @date: 2023-04-03 10:02:56
		 */
		orgSelectChange(orgList) {
			if (orgList.length > 0) {
				this.form.sysOrg = orgList[0];
			} else {
				this.form.sysOrg = {
					id: "",
					name: "",
				};
			}
		},
	},
};
</script>

<style></style>
