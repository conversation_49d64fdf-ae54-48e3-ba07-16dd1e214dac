/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2024-12-23 13:40:43
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2024-12-23 14:36:03
 * @FilePath: \archive-manage-front\vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const {defineConfig} = require("@vue/cli-service");

module.exports = defineConfig({
	//设置为空打包后不分更目录还是多级目录
	publicPath: "",
	//build编译后存放静态文件的目录
	//assetsDir: "static",

	// build编译后不生成资源MAP文件
	productionSourceMap: process.env.NODE_ENV !== 'production',

	//开发服务,build后的生产模式还需nginx代理
	devServer: {
		allowedHosts: "all",
		open: false, //运行后自动打开浏览器
		port: process.env.VUE_APP_PORT, //挂载端口
		proxy: {
			//文件资源服务
			"/resource": {
				target: process.env.VUE_APP_API_fileUrl,
				ws: false,
				pathRewrite: {
					"^/resource": "/resource",
				},
			},
			"/archive-dev": {
				target: process.env.VUE_APP_API_fileUrl,
				ws: false,
				pathRewrite: {
					"^/archive-dev": "/archive-dev",
				},
			},
			"/archive-test": {
				target: process.env.VUE_APP_API_fileUrl,
				ws: false,
				pathRewrite: {
					"^/archive-test": "/archive-test",
				},
			},
			"/archive-pro": {
				target: process.env.VUE_APP_API_fileUrl,
				ws: false,
				pathRewrite: {
					"^/archive-pro": "/archive-pro",
				},
			},
			//报表服务
			"/report": {
				target: process.env.VUE_APP_REPORT,
				ws: false,
				pathRewrite: {
					"^/report": "/report",
				},
			},
			//认证服务
			['/']: {
				target: process.env.VUE_APP_API,
				ws: false,
				pathRewrite: {
					['^/']: ''
				},
			},
		},
	},
	lintOnSave: false, // 取消 eslint 验证
	css: {
		loaderOptions: {
			css: {},
			postcss: {}
		}
	},
	chainWebpack: (config) => {
		// 移除 prefetch 插件
		config.plugins.delete("preload");
		config.plugins.delete("prefetch");
		config.resolve.alias.set("vue-i18n", "vue-i18n/dist/vue-i18n.cjs.js");
		config.module.rule('properties')
			.test(/\.properties$/)
			.use('raw-loader')
			.loader('raw-loader')
			.end();
	},
	configureWebpack: {
		//性能提示
		performance: {
			hints: false,
		},
		optimization: {
			splitChunks: {
				chunks: "all",
				automaticNameDelimiter: "~",
				name: "scuiChunks",
				cacheGroups: {
					//第三方库抽离
					vendor: {
						name: "modules",
						test: /[\\/]node_modules[\\/]/,
						priority: -10,
					},
					elicons: {
						name: "elicons",
						test: /[\\/]node_modules[\\/]@element-plus[\\/]icons-vue[\\/]/,
					},
					tinymce: {
						name: "tinymce",
						test: /[\\/]node_modules[\\/]tinymce[\\/]/,
					},
					echarts: {
						name: "echarts",
						test: /[\\/]node_modules[\\/]echarts[\\/]/,
					},
					xgplayer: {
						name: "xgplayer",
						test: /[\\/]node_modules[\\/]xgplayer.*[\\/]/,
					},
					codemirror: {
						name: "codemirror",
						test: /[\\/]node_modules[\\/]codemirror[\\/]/,
					},
				},
			},
			realContentHash: false,
			minimize: process.env.NODE_ENV === 'production',
		},
	},
});
