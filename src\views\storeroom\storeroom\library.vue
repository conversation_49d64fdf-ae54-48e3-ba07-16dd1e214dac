<template>
	<div class="library-container">
		<!-- 加载状态 -->
		<div v-if="loading" class="loading-container">
			<el-skeleton :rows="3" animated />
		</div>

		<!-- 卡片网格 -->
		<div v-else class="cards-grid">
			<el-card
				v-for="(item) in storeroomList"
				:key="item.id"
				class="library-card"
				shadow="hover"
				@click="addShelves(item)"
			>
				<!-- 图片区域 -->
				<div class="card-image">
					<img
						:src="item.houseCoverUrl"
						:alt="item.houseName"
						class="warehouse-image"
						@error="handleImageError"
						@load="handleImageLoad"
					>
					<!-- 默认图片 -->
					<div v-if="!item.imageLoaded" class="default-image">
						<el-icon size="48" color="#909399"><Picture /></el-icon>
						<span class="default-text">暂无图片</span>
					</div>
					<div class="image-overlay">
						<el-button type="primary" size="small" icon="View">查看详情</el-button>
					</div>
				</div>

				<!-- 信息区域 -->
				<div class="card-content">
					<h3 class="warehouse-name">{{ item.houseName || '暂无名称' }}</h3>
					<div class="warehouse-info">
						<div class="info-item">
							<span class="label">编号：</span>
							<span class="value">{{ item.houseNum || '暂无' }}</span>
						</div>
						<div class="info-item">
							<span class="label">面积：</span>
							<span class="value">{{ item.houseArea || '暂无' }} ㎡</span>
						</div>
						<div class="info-item">
							<span class="label">管理员：</span>
							<span class="value">{{ item.houseManager?.name || '暂无' }}</span>
						</div>
					</div>
				</div>
			</el-card>
		</div>

		<!-- 空状态 -->
		<div v-if="!loading && storeroomList.length === 0" class="empty-state">
			<el-empty description="暂无档案库数据" :image-size="120">
			</el-empty>
		</div>

		<!-- 分页器 -->
		<div class="pagination-wrapper">
			<pagination
				:total="total"
				v-model:page="queryParams.current"
				v-model:limit="queryParams.size"
				@pagination="selectList"
			/>
		</div>

		<!-- 可视化弹窗 -->
		<el-dialog
			:title="titleDialog"
			v-model="openDialog"
			fullscreen
			append-to-body
			destroy-on-close
			class="visual-dialog"
		>
			<VisualWarehouseDiagram
				:warehouseId="warehouseId"
				class="visual-content"
			/>
		</el-dialog>
	</div>
</template>
<script>
import storeroomData from "@/api/archive/storeroom/storeroom";
import VisualWarehouseDiagram from "@/views/storeroom/storeroom/VisualWarehouseDiagram.vue";
import {getOrganizationInfo} from "@/utils/permission";
import { Picture } from '@element-plus/icons-vue';

export default {
	name: 'library',
	components: {VisualWarehouseDiagram, Picture},
	props: {
		parentDataInfo: {}
	},
	data() {
		return {
			activeName: 'first',
			total: 0,
			storeroomList: [],
			houseInfoUrl: '',
			queryParams: {
				current: 1,
				size: 10
			},
			titleDialog: '可视化查看',
			openDialog: false,
			warehouseId: '',
			loading: false
		}
	},
    mounted() {
		this.selectList();
	},
	methods: {
		addShelves(data) {
			this.$emit('libraryView', data);
		},
		openVisualBox(data) {
			this.warehouseId = data.id;
			this.titleDialog = "可视化查看";
			this.openDialog = true;
		},
		// 处理图片加载错误
		handleImageError(event) {
			// 隐藏原始图片，显示默认图片
			event.target.style.display = 'none';
			// 为当前项添加图片加载失败标记
			const item = this.storeroomList.find(item =>
				item.houseCoverUrl === event.target.src
			);
			if (item) {
				this.$set(item, 'imageLoaded', false);
			}
		},
		// 处理图片加载成功
		handleImageLoad(event) {
			// 为当前项添加图片加载成功标记
			const item = this.storeroomList.find(item =>
				item.houseCoverUrl === event.target.src
			);
			if (item) {
				this.$set(item, 'imageLoaded', true);
			}
		},
		async selectList() {
			this.loading = true;
			try {
				const result = await storeroomData.detailsList({
					...this.queryParams,
					'org.id': getOrganizationInfo()?.id
				});

				if (result.code === 200) {
					// 为每个项添加图片加载状态标记
					this.storeroomList = result.data.records.map(item => ({
						...item,
						imageLoaded: true // 初始状态为未加载
					}));
					this.total = result.data.total;
				}
			} catch (error) {
				console.error('获取档案库列表失败:', error);
				this.$message.error('获取数据失败');
			} finally {
				this.loading = false;
			}
		}
	}
}
</script>

<style scoped>
.cards-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(225px, 1fr));
	gap: 10px;
}

.library-card {
	border-radius: 5px;
	overflow: hidden;
	transition: all 0.3s ease;
	cursor: pointer;
	border: none;
}

.library-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

::v-deep .library-card .el-card__body{
    padding: 15px;
}

.card-image {
	position: relative;
	height: 150px;
	overflow: hidden;
	background: #f5f7fa;
}

.warehouse-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.library-card:hover .warehouse-image {
	transform: scale(1.05);
}

.default-image {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #f5f7fa;
	color: #909399;
}

.default-text {
	margin-top: 8px;
	font-size: 12px;
	color: #909399;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.library-card:hover .image-overlay {
	opacity: 1;
}

.warehouse-name {
	margin: 15px 0 15px 0;
	font-size: 18px;
	font-weight: 600;
	color: #303133;
	line-height: 1.4;
}

.warehouse-info {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.label {
	font-size: 14px;
	color: #606266;
	font-weight: 500;
    flex-shrink: 0;
}

.value {
	font-size: 14px;
	color: #303133;
	font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.empty-state {
	padding: 60px 20px;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	text-align: center;
}

.pagination-wrapper {
    margin-top: 10px;
	display: flex;
	justify-content: right;
	padding: 10px 10px;
	background: #fff;
	border-radius: 5px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.visual-dialog :deep(.el-dialog__body) {
	padding: 0;
	height: calc(100vh - 120px);
}

.visual-content {
	width: 100%;
	height: 100%;
}
/* 响应式设计 */
@media (max-width: 1680px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 5px;
    }
}
@media (max-width: 768px) {
	.cards-grid {
		grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
		gap: 16px;
	}

	.library-container {
		padding: 16px;
	}

	.page-header {
		padding: 16px;
	}
}

@media (max-width: 480px) {
	.cards-grid {
		grid-template-columns: 1fr;
	}
}
</style>
