<template>
	<el-container class="bodyBox">
		<el-main v-if="open" class="noPadding retrieval">
			<div class="searchBox">
				<div style="display: flex;justify-content: center;margin-bottom: 30px">
					<h1 style="font-size: 2em">全文检索</h1>
				</div>
				<div>
					<!--					<el-text :class="searchType ? 'choseClass' : 'noChoseClass'"-->
					<!--							 @click="searchType = !searchType">-->
					<!--						全文检索-->
					<!--					</el-text>-->
					<!--					<el-text :class="!searchType ? 'choseClass' : 'noChoseClass'"-->
					<!--							 @click="searchType = !searchType">-->
					<!--						知识图谱-->
					<!--					</el-text>-->
				</div>
				<el-input v-model="name"
						  class="inputStyle"
						  placeholder="请输入关键字进行检索, 多个关键字请用空格隔开"
						  prefix-icon="Search"
						  size="large"
						  @keyup.enter="handleQuery">
					<template #append>
						<el-button size="large" style="border-radius: 0 10px 10px 0;" type="primary"
								   @click="handleQuery">
							查询
						</el-button>
					</template>
				</el-input>
			</div>
		</el-main>
		<el-main v-show="!open" class="noPadding">
			<el-container>
				<el-header height="auto" style="background: none;padding: 10px">
					<el-card class="cardBox">
						<div style="display: flex;flex-flow: row;align-items: center;justify-content: space-between;
							align-content: center;flex-direction: row;flex-wrap: nowrap;">
							<div>
								<el-button icon="RefreshLeft" type="danger" @click="returnBack(true)">
									返回
								</el-button>
								<el-divider direction="vertical" style="height: 2em"/>
								<el-input v-model="name" placeholder="请输入关键字进行检索, 多个关键字请用空格隔开"
										  style="width: 28vw" @keyup.enter="handleQuery"/>
								<el-button icon="Search" style="margin-left: 20px;" type="primary" @click="handleQuery">
									查询
								</el-button>
								<el-button icon="Refresh" plain @click="resetQuery">
									重置
								</el-button>
							</div>
							<div>
								<el-badge :class="startShake ? 'badgeShake' : ''" :value="addOne">
									<el-button :icon="addOne <= 0 ? 'ShoppingCart' : 'ShoppingCartFull'" type="primary"
											   @click="borrowingCar">
										借阅车
									</el-button>
								</el-badge>
							</div>
						</div>
					</el-card>
				</el-header>
				<el-main style="padding: 10px">
					<fullTextSearch v-show="!open && searchType" ref="fullTextSearchRef" :searchString="name"
									@returnBack="returnBack"
									@shakeStatusChange="shakeStatusChange"/>
					<relationShip v-if="!open && !searchType" ref="relationShipRef" :searchString="name"
								  @returnBack="returnBack"/>
				</el-main>
			</el-container>
		</el-main>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onMounted, ref,reactive,toRefs,defineOptions } from 'vue'
// 命名
defineOptions({
    name: "archivalRetrieval", // 这里改成自己的组件名
})
// 查询全宗和门类
// 检索
import retrievalList from '@/api/archive/retrieval';
import tool from '@/utils/tool';
import router from '@/router';
import {ElMessage} from 'element-plus'
// 档案信息
import fullTextSearch from './fullTextSearch.vue'
import relationShip from './relationShip.vue'
//档案标签

const {proxy} = getCurrentInstance();
const addOne = ref(0)
// 显示搜索框或者显示检索列表
const open = ref(true)
const startShake = ref(false)
const searchType = ref(true);
// 检索
const name = ref('');
const fullTextSearchRef = ref(null)
const relationShipRef = ref(null)
// 分页
const data = reactive({
    queryParams:{
        current: 1,
        size: -1,
    }
})
const {queryParams} = toRefs(data);
onMounted(() => {
	borrowingCarList();
});

function handleQuery() {
	open.value = false;
	if (searchType.value) {
		fullTextSearchRef.value.handleQuery();
	}
	if (!searchType.value) {
		relationShipRef.value.showGraph();
	}
}

function resetQuery() {
	name.value = '';
	if (searchType.value) {
		// fullTextSearchRef.value.resetQuery();
	}
	if (!searchType.value) {
		relationShipRef.value.resetQuery();
	}
}

// 返回页面
function returnBack(isFirst) {
	open.value = !open.value;
	searchType.value = isFirst ? true : !searchType.value;
	name.value = '';
    fullTextSearchRef.value.resetQuery();
}

// 查詢借阅车数量
function borrowingCarList() {
	retrievalList.carList({
		current: queryParams.value.current,
		sizez: queryParams.value.size,
		'person.id': tool.data.get('USER_INFO').id,
	}).then(res => {
		if (res.code === 200) {
			addOne.value = res.data.total;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})

}

// 借阅车
function borrowingCar() {
	console.log(addOne.value);
	if (addOne.value > 0) {
		router.replace({path: "/borrowingCar"})
	} else {
		ElMessage({
			message: '请将需借阅的档案加入借阅车！',
			type: 'warning',
		})
	}
}

function shakeStatusChange() {
	startShake.value = true;
	borrowingCarList();
	setTimeout(() => {
		startShake.value = false;
	}, 1000)
}
</script>
<style scoped>
.bodyBox {
	background-image: url('@/assets/images/searchBackgroud.png');
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
}

.retrieval {
	display: flex;
	justify-content: center;
	background: none;
	padding-top: 20vh;
}

:deep(.el-form-item--default) {
	margin-bottom: 15px !important;
}

.details {
	background-color: #409eff;
	height: 30px;
	width: 80px;
	border-bottom-left-radius: 10px;
	border-top-right-radius: 10px;
}

.borrowing_car {
	position: absolute;
	right: 25px;
	bottom: 200px;
	/* background-color: #e49740; */
	height: 40px;
	width: 120px;
	border-radius: 5px;
	display: flex;
}

.borrowing_car div {
	color: #fff;
	font-size: 16px;
	line-height: 40px;
	margin-left: 10px;
}

.badgeShake :deep(.el-badge__content) {
	animation: twinkle 0.6s;
	animation-iteration-count: 3;
}

@keyframes twinkle {
	from {
		opacity: 1.0;
	}
	50% {
		opacity: 0.8;
		background-color: yellow;
		color: black;
	}
	to {
		opacity: 1.0;
	}
}

.searchBox {
	display: flex;
	flex-wrap: nowrap;
	flex-direction: column;
	align-items: stretch;
	width: 50%;
}

:deep(.searchBox .el-input__wrapper) {
	border-radius: 10px 0 0 10px;
}

:deep(.el-input-group__append) {
	border-radius: 0 10px 10px 0 !important;
}

.cardBox {
	width: 100%;
	height: 100%;
}

.choseClass {
	color: black;
	margin: 10px 5px;
	font-size: 16px;
	font-weight: bold;
	display: inline-block;
}

.noChoseClass {
	color: #A8ABB2;
	margin: 10px 5px;
	font-size: 12px;
	font-weight: bold;
	display: inline-block;
}

.inputStyle {
	height: 50px;
	width: 100%;

	.el-input-group__append {
		border-radius: 0 10px 10px 0 !important;
	}

	.el-input-group__append button.el-button {
		border-color: transparent !important;
		background-color: #2A76F8 !important;
		color: white !important;
		height: 100%;
		width: 102px;
	}
}
</style>
