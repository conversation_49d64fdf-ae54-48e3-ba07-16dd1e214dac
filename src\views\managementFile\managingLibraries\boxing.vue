
<template>
    <el-container v-loading="loading">
        <el-main v-if="openTree" ref="main" class="noPadding" style="">
			<div style="margin-bottom: 10px;">
				<div style="display: flex;">
					<!--  -->
					<el-form ref="formList" :inline="true" :model="form" label-position="right" label-width="auto">
						<el-form-item label="档案盒名称" prop="boxName" style="margin: 0;padding-right: 0;">
							<el-input v-model="form.boxName" placeholder="请输入档案盒名称"/>
						</el-form-item>
						<el-form-item style="margin: 0;padding-left: 12px;">
							<el-button icon="Search" type="primary" @click="() => getList()">查询</el-button>
							<el-button icon="RefreshRight" plain @click="() => resetQuery()">重置</el-button>
						</el-form-item>
					</el-form>
				</div>
            </div>
            <el-button :icon="Plus" style="margin-bottom: 10px;" type="primary"
                @click="() => collectBoxingAdd()">新增</el-button>

			<el-table :data="receiveData" border @selection-change="handleSelectionChange">
				<el-table-column align="center" min-width="30" type="selection"/>
				<el-table-column align="center" label="档案盒名称" min-width="120" prop="boxName" show-overflow-tooltip/>
				<el-table-column align="center" label="档案盒规格" min-width="120" prop="boxSpecification" show-overflow-tooltip/>
				<el-table-column align="center" label="年份" min-width="120" prop="boxYear" show-overflow-tooltip/>
				<el-table-column align="center" label="容量" min-width="120" prop="boxSize" show-overflow-tooltip/>
				<el-table-column align="center" label="装盒人" min-width="120" prop="boxIntoPerson.name" show-overflow-tooltip/>
				<el-table-column align="center" label="备注" min-width="120" prop="boxRemark" show-overflow-tooltip/>
			</el-table>

            <!-- 分页和操作按钮区域 -->
            <div style="margin-top: 16px; padding: 0 16px;">
                <!-- 分页组件 -->
                <div style="display: flex; justify-content: flex-end; margin-bottom: 16px;">
                    <pagination
                        v-model:limit="queryParams.size"
                        v-model:page="queryParams.current"
                        :total="total"
                        @pagination="getList()"
                    />
                </div>
                <!-- 操作按钮 -->
                <div style="display: flex; justify-content: flex-end; gap: 8px;">
                    <el-button plain @click="() => cancellation(1)">取消</el-button>
                    <el-button type="primary" @click="() => handleQuery()">确定</el-button>
                </div>
            </div>
        </el-main>
        <!-- 新增 -->
		<el-main v-if="!openTree" append-to-body style="padding: 0" width="1000px">
			<el-divider style="margin: 0 0 10px 0;"/>
			<div style="margin-bottom: 10px;">
				<el-form ref="formRef" :error="errorBox" :inline="true" :model="formBox" :rules="rules"
						 label-width="auto">
					<el-form-item label="盒编号:" prop="boxNum" style="width: 46%;">
						<el-input v-model="formBox.boxNum" placeholder="请输入盒编号" @blur="testVerify"/>
					</el-form-item>
					<el-form-item label="档案盒名称:" prop="boxName" style="width: 46%;">
						<el-input v-model="formBox.boxName" placeholder="请输入档案盒名称"/>
					</el-form-item>
					<el-form-item label="盒规格:" prop="boxSpecification" style="width: 46%;">
						<el-input v-model="formBox.boxSpecification" placeholder="请输入盒规格"/>
					</el-form-item>
					<el-form-item label="全宗:" prop="boxGroupId" style="width: 46%;">
                        <el-select v-model="formBox.boxGroupId" placeholder="请选择全宗" style="width: 100%;">
                            <el-option v-for="item in fourConfig.group" :key="item.id" :label="item.recordGroupName"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="门类:" prop="boxCategoryId" style="width: 46%;">
                        <el-tree-select v-model="formBox.boxCategoryId" :data="fourConfig.category" :props="{ value: 'id', label: 'name' }"
										check-strictly highlight-current placeholder="请选择门类"
										style="width: 100%">
							<template #default="{ data }">
								<span style="float: left;">{{ data.name }}</span>
							</template>
						</el-tree-select>
					</el-form-item>
					<el-form-item label="年份:" prop="boxYear" style="width: 46%;">
						<el-date-picker v-model="formBox.boxYear" format="YYYY" placeholder="请选择年份"
										style="width: 100%"
										type="year" value-format="YYYY"/>
					</el-form-item>
					<el-form-item label="月份:" prop="boxMonth" style="width: 46%;">
						<el-date-picker v-model="formBox.boxMonth" format="MM" placeholder="请选择月份"
										style="width: 100%"
										type="month" value-format="MM"/>
					</el-form-item>
					<el-form-item label="盒内数量:" prop="boxSize" style="width: 46%;">
						<el-input v-model="formBox.boxSize" placeholder="请输入盒内数量"/>
					</el-form-item>
					<el-form-item label="档案盒保留期限:" prop="boxRetentionPeriod" style="width: 46%;">
						<el-input v-model="formBox.boxRetentionPeriod" placeholder="请输入档案盒保留期限">
							<template #append>
								<span>年</span>
							</template>
						</el-input>
					</el-form-item>
					<el-form-item label="装盒人:" prop="boxIntoPersonId" style="width: 46%;">
						<el-input v-model="formBox.boxIntoPersonId" disabled placeholder="请输入装盒人"/>
					</el-form-item>
					<el-form-item label="备注:" prop="boxRemark" style="width: 95.5%;">
						<el-input v-model="formBox.boxRemark" placeholder="请输入备注" type="textarea"/>
					</el-form-item>
				</el-form>
                <!-- 操作按钮区域 -->
                <div style="display: flex; justify-content: flex-end; margin-top: 24px; padding-top: 16px; border-top: 1px solid #ebeef5;">
                    <div style="display: flex; gap: 12px;">
                        <el-button plain @click="() => cancellation()">取消</el-button>
                        <el-button type="primary" @click="() => add()">确定</el-button>
                    </div>
                </div>
            </div>
        </el-main>
    </el-container>
</template>

<script setup>
import tool from "@/utils/tool";
// 查询全宗和门类
import completeManagement from '@/api/archive/systemConfiguration/completeManagement'
import category from '@/api/archive/categoryManagement/category';

import {defineProps, getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import {Plus} from '@element-plus/icons-vue'
import libraries from '@/api/archive/managementFile/managingLibraries';
import {getOrganizationInfo} from "@/utils/permission";

const emit = defineEmits(["childEvent"]);
const props = defineProps({
	handList: {
		type: Array
	},
	clickEvenId: {
		type: Array
	}
})
const data = reactive({
	form: {
		boxName: ''
	},
	queryParams: {
		current: 1,
		size: 10,
	},
	fourConfig: {},
})
// 加载
const loading = ref(true)
// 表单
const formRef = ref(null);
const total = ref(0)
const {form, queryParams, fourConfig} = toRefs(data)
const {proxy} = getCurrentInstance()
// 接收库List
const receiveData = ref([])
// 点击查询列表
const openTree = ref(false)
// 选中的数据
const handData = ref([])
const errorBox = ref('')
// 新增表单
const formBox = ref({
	boxYear: formatDate(new Date()),
	boxMonth: formatDate(new Date()),
})
// 表单校验
const rules = reactive({
	boxNum: [
		{required: true, message: '请输入盒编号', trigger: 'blur'},
		{validator: validatePass, trigger: 'blur'}
	],
	boxName: [
		{required: true, message: '请输入档案盒名称', trigger: 'blur'},
		{validator: validatePass, trigger: 'blur'}
	],
	boxSpecification: [{required: true, message: '请输入盒规格', trigger: 'blur'},],
	boxGroupId: [{required: true, message: '请选择全宗', trigger: 'change'},],
	boxCategoryId: [{required: true, message: '请选择门类', trigger: 'change'},],
	boxYear: [{required: true, message: '请选择年份', trigger: 'change'},],
	boxSize: [
		{required: true, message: '请输入盒内数量', trigger: 'blur'},
		{pattern: /^[0-9]*$/, message: '只能为数字', trigger: 'blur'}
	],
	boxRetentionPeriod: [
		{required: true, message: '请输入档案盒保留年限', trigger: 'blur'},
		{pattern: /^[0-9]*$/, message: '只能为数字', trigger: 'blur'}
	],
	boxIntoPersonId: [{required: true, message: '请输入装盒人', trigger: 'blur'},],
})

onMounted(() => {
	getList();
});


// 选中表格
function handleSelectionChange(val) {
	handData.value = val;
}

// 校验参数
function validatePass(rule, value, callback) {
	let data = {};
	data.boxNum = rule.field === "boxNum" ? value : null;
	data.boxName = rule.field === "boxName" ? value : null;
	if (value === '' || value === null) {
		callback(new Error('请输入数据！'))
	}
	libraries.findListConditions(data).then(res => {
		if (res.code === 200) {
			if (res.data.length > 0) {
				callback(new Error('该数据已存在请重新输入！'))
            } else {
                callback()
            }
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

// 装盒
function handleQuery() {
	let ids = props.handList.map((v) => v.id);
    //拼接的数组字符串，接口传参
	let idStr = ids.join(",");
    console.log(handData.value);
    libraries.infoOperate({
        box: {
            id: handData.value[0].id,
        },
		boxStatus: '1',
        infoIds: idStr
    }).then(res => {
        if (res.code === 200) {
            proxy.msgSuccess('装盒成功');
            emit("childMove", '', '1');
        }
    }).catch(() => {
        proxy.msgError('装盒失败');
    })
}

// 重置
function resetQuery() {
	form.value = {
		boxName: '',
	};
	getList();
}

// 格式化日期
function formatDate(date) {
    const year = date.getFullYear();
    return `${year}`;
}

// 新增
function collectBoxingAdd() {
    formBox.value.boxIntoPersonId = tool.data.get("USER_INFO").name
    emit("childMove", '新增');
    getManagement();
    dataInfo(props.handList);
    // 进入新增时年份默认为当前年份
    openTree.value = false;
}

// 取消新增
function cancellation(val) {
    getList();
    emit("childMove", '装盒', val);
    openTree.value = true;
}

// 验证档案盒名称和编号是否存在
function testVerify(val) {
    libraries.list({
        current: 1,
        size: -1,
        boxNum: val,
        'org.id': getOrganizationInfo()?.id,
    }).then(res => {
        if (res.code === 200) {
			if (res.data.records === '') {
				errorBox.value = '该名称已存在请重新输入！'
				console.log(errorBox.value, '----');
			}
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

// 确定提交新增
function add() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            libraries.save({
				'controlGroup': {
					id: formBox.value.boxGroupId
				},
				'controlCategory': {
					id: formBox.value.boxCategoryId
				},
				'boxIntoPerson': {
					id: tool.data.get("USER_INFO").id
				},
				boxMonth: formBox.value.boxMonth,
				boxRetentionPeriod: formBox.value.boxRetentionPeriod,
				boxNum: formBox.value.boxNum,
				boxName: formBox.value.boxName,
				boxRemark: formBox.value.boxRemark,
				boxSize: formBox.value.boxSize,
				boxSpecification: formBox.value.boxSpecification,
				boxYear: formBox.value.boxYear
			}).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess('新增成功');
					openTree.value = true;
					formRef.value.resetFields();
					getList();
				}
			}).catch((error) => {
				console.log(error);
				formRef.value.resetFields();
				proxy.msgError('新增失败');
			})
        }
    })
}

// 进入时查询全部
function getList() {
    libraries.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		boxName: form.value.boxName,
	}).then(res => {
        if (res.code === 200) {
            receiveData.value = res.data.records;
            loading.value = false;
            total.value = res.data.total;
            openTree.value = true;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

// 查询全宗
function getManagement() {
    completeManagement.getList(
        queryParams.value
    ).then(res => {
        if (res.code === 200) {
            fourConfig.value.group = res.data.records;
        }
    });
}

// 查询门类
function dataInfo() {
    category.list().then(res => {
        if (res.code === 200) {
            fourConfig.value.category = res.data;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}

</script>

<style scoped></style>
