import http from '@/utils/request';

export default {
    getList: function (inputForm) {
        return http.get('/archive/config/groupConfig/list', inputForm);
    },
    save: function (data) {
        return http.post('/archive/config/groupConfig/save', data);
    },
    delete: function (ids) {
        return http.delete('/archive/config/groupConfig/delete', ids);
    },
    queryById: function (inputForm) {
        return http.get('/archive/config/groupConfig/queryById', inputForm);
    },
    // 全宗下拉
    getGroupSelect: function (inputForm) {
        return http.get('/archive/config/groupConfig/group-select', inputForm);
    }
};
