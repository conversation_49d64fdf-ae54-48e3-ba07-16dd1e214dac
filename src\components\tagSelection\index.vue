<template>
	<div class="tag-selection-container">
		<el-button
			v-if="showButton"
			class="tag-selection-button"
			plain
			style="width: 200px"
			type="primary"
			@click="openTagsChose = true"
			>选择标签</el-button
		>
		<!-- 已选择标签的回显 -->
		<div v-if="selectedTags.length > 0" class="selected-tags">
			<template v-for="tag in tagsList">
				<el-tag
					v-if="selectedTags.includes(tag.id)"
					:key="tag.id"
					:closable="showButton"
					:effect="judgeTheme(tag.tagTheme).type"
					:round="judgeShape(tag.tagRound).type"
					:type="judgeColor(tag.tagColor).type"
					@close="removeTag(tag.id)"
				>
					{{ tag.tagName }}
				</el-tag>
			</template>
		</div>
		<tagsListDialog
			v-if="openTagsChose"
			:choseTagsId="selectedTagStr"
			:dialogIsOpen="openTagsChose"
			@choseTagMethodReturn="choseTagMethodReturn"
		/>
	</div>
</template>

<script setup>
import { ref, getCurrentInstance, onBeforeMount, watch } from "vue";
import tagsListDialog from "@/views/archiveReception/common/tagsListDialog.vue";
import tagsManagement from "@/api/archive/tagsManagement";
const { proxy } = getCurrentInstance();

const props = defineProps({
	selectedTagStr: {
		type: String,
		default: () => "",
	},
	showButton: {
		type: Boolean,
		default: false,
	},
});
const emit = defineEmits(["update:selectedTagStr"]);

const openTagsChose = ref(false);
//标签信息集合
const tagsList = ref([]);
const selectedTags = ref([]);
onBeforeMount(() => {
	getTagsList();
});
// 监听标签变化
watch(
	() => props.selectedTagStr,
	(newVal) => {
		if (newVal) {
			selectedTags.value = newVal.split(",").filter((id) => id);
		} else {
			selectedTags.value = [];
		}
	},
	{ immediate: true }
);

function choseTagMethodReturn(data) {
	selectedTags.value = data;
	emit("update:selectedTagStr", data.join(","));
	openTagsChose.value = false;
}

function removeTag(tagId) {
	const tagIds = selectedTags.value.filter((id) => id !== tagId);
	emit("update:selectedTagStr", tagIds.toString());
}
// 获取标签列表
function getTagsList() {
	tagsManagement
		.getList({
			current: 1,
			size: -1,
		})
		.then((res) => {
			if (res.code === 200) {
				tagsList.value = res.data.records;
			}
		})
		.catch(() => {
			proxy.msgError("查询失败");
		});
}

// 判断主题
function judgeTheme(type) {
	if (type === "1") {
		return { label: "深色", type: "dark" };
	} else if (type === "2") {
		return { label: "浅色", type: "light" };
	} else {
		return { label: "默认", type: "plain" };
	}
}

// 判断形状
function judgeShape(type) {
	if (type === "1") {
		return { label: "圆角", type: false };
	} else if (type === "2") {
		return { label: "椭圆", type: true };
	} else {
		return { label: "圆角", type: false };
	}
}

// 判断颜色
function judgeColor(type) {
	if (type === "1") {
		return { label: "蓝色", type: "" };
	} else if (type === "2") {
		return { label: "绿色", type: "success" };
	} else if (type === "3") {
		return { label: "灰色", type: "info" };
	} else if (type === "4") {
		return { label: "红色", type: "danger" };
	} else if (type === "5") {
		return { label: "橙色", type: "warning" };
	} else {
		return { label: "蓝色", type: "" };
	}
}
</script>

<style scoped>
.tag-selection-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}
.selected-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
}
</style>
