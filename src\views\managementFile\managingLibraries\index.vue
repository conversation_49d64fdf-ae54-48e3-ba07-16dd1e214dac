<template>
	<el-container>
        <DragLayout type="row">
            <template #first>
                <el-aside class="w-full h-full p-el">
                    <el-card class="h-full w-full" body-class="h-full w-full p-0">
                        <categoryTree :is-the-first-level-clickable="true" :default-expand-all="true" @clickNode="clickEven"/>
                    </el-card>
                </el-aside>
            </template>
            <template #second>
                <el-main ref="main" v-loading="openTree" class="p-el h-full">
                    <el-tabs v-model="chooseTab" style="box-shadow: var(--el-box-shadow-light);width: 100%;height: 100%"
                             type="border-card" @tab-change="tabChange">
                        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.name"
                                     style="width: 100%;height: 100%;display: flex;flex-direction: row;flex-wrap: wrap;
							 align-items: stretch;row-gap: 10px">
                            <el-container>
                                <el-header height="auto">
                                    <el-form ref="formList" :inline="true" :model="form" label-position="right"
                                             label-width="auto">
                                        <el-form-item label="档案名称" prop="name" style="margin-bottom: 0">
                                            <el-input v-model="form.name" placeholder="请输入档案名称"/>
                                        </el-form-item>
                                        <el-form-item label="档案号" prop="numFormat" style="margin-bottom: 0">
                                            <el-input v-model="form.numFormat" placeholder="请输入档案号"/>
                                        </el-form-item>
                                        <el-form-item style="margin-bottom: 0">
                                            <el-button icon="Search" type="primary" @click="handleQuery()">
                                                查询
                                            </el-button>
                                            <el-button icon="RefreshRight" plain @click="resetQuery()">
                                                重置
                                            </el-button>
                                        </el-form-item>
                                    </el-form>
                                </el-header>
                                <el-main style="padding: 10px 10px 0 10px">
                                    <el-container>
                                        <el-header height="auto" style="padding: 0 0 10px 0">
                                            <div
                                                style="width:100%;display: flex;justify-content:space-between;align-items:center;">
                                                <div>
                                                    <el-button v-if="item.name === 'first'" icon="TakeawayBox" plain
                                                               type="success"
                                                               @click="collectBoxing()">
                                                        装盒
                                                    </el-button>
                                                    <!--											<el-button :loading="returnLoading" icon="Back" plain type="warning"-->
                                                    <!--													   @click="collectReturn()">-->
                                                    <!--												退回-->
                                                    <!--											</el-button>-->
                                                    <el-button icon="Upload" plain type="primary" @click="collectUpload(index)">
                                                        导出
                                                    </el-button>
                                                </div>
                                                <div style="display: flex;align-items: center">
                                                    <div style="margin-right: 15px;" @click="getList">
                                                        <el-tooltip class="box-item" content="刷新" effect="dark"
                                                                    placement="top">
                                                            <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                                                <Refresh/>
                                                            </el-icon>
                                                        </el-tooltip>
                                                    </div>
                                                    <div @click="screen">
                                                        <el-tooltip class="box-item" content="全屏" effect="dark"
                                                                    placement="top">
                                                            <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                                                <FullScreen/>
                                                            </el-icon>
                                                        </el-tooltip>
                                                    </div>
                                                </div>
                                            </div>
                                        </el-header>
                                        <el-main style="padding: 0">
                                            <el-table ref="dataTableList" v-loading="loading" :data="receiveData" border
                                                      style="height: 100%;width: 100%"
                                                      @selection-change="handleSelectionChange">
                                                <el-table-column align="center" min-width="30" type="selection" width="50"/>
                                                <el-table-column align="left" label="档案名称" min-width="320" prop="name"/>
                                                <el-table-column align="center" label="档案号" min-width="320" prop="num"/>
                                                <el-table-column align="center" label="保留年限" prop="retentionPeriod"
                                                                 width="80">
                                                    <template #default="scope">
                                                        {{ reserve(scope.row.retentionPeriod) }}
                                                    </template>
                                                </el-table-column>
                                                <el-table-column align="center" label="控制等级" prop="controlStatus"
                                                                 width="110">
                                                    <template #default="scope">
                                                        {{ control(scope.row.controlStatus) }}
                                                    </template>
                                                </el-table-column>
                                                <el-table-column align="center" label="装盒状态" prop="boxStatus" width="80">
                                                    <template #default="scope">
                                                        {{ scope.row.boxStatus === "1" ? "已装盒" : "未装盒" }}
                                                    </template>
                                                </el-table-column>
                                                <el-table-column align="center" label="所属部门" prop="office.name" width="100">
                                                    <template #default="scope">
                                                        {{ scope.row.office ? scope.row.office.name : '暂无' }}
                                                    </template>
                                                </el-table-column>
                                                <el-table-column align="center" label="是否电子档案" prop="office.name"
                                                                 width="120">
                                                    <template #default="scope">
                                                        {{ scope.row.isElectronic === '1' ? '是' : '否' }}
                                                    </template>
                                                </el-table-column>
                                                <el-table-column align="center" label="档案归档时间" prop="archiveTime" sortable
                                                                 width="160">
                                                    <template #default="scope">
                                                        {{
                                                            (
                                                                scope.row.archiveTime ?
                                                                    moment(scope.row.archiveTime).format('YYYY-MM-DD HH:mm:ss')
                                                                    : undefined
                                                            ) || '暂无'
                                                        }}
                                                    </template>
                                                </el-table-column>
                                                <el-table-column align="center" class-name="small-padding fixed-width"
                                                                 fixed="right"
                                                                 label="操作" width="210">
                                                    <template #default="scope">
                                                        <div style="display: flex;flex-direction: row;flex-wrap: nowrap;
											align-items: center;width: 100%;height: 100%">
                                                            <el-button icon="View" link size="small" type="primary"
                                                                       @click="collectFile(scope.row)">
                                                                查看
                                                            </el-button>
<!--                                                            <el-button icon="Connection" link size="small"-->
<!--                                                                       type="primary" @click="connectionHistory(scope.row)">-->
<!--                                                                版本关联-->
<!--                                                            </el-button>-->
                                                            <el-dropdown>
                                                                <el-button icon="ArrowDown" link size="small" type="primary">
                                                                    更多
                                                                </el-button>
                                                                <template #dropdown>
                                                                    <el-dropdown-menu>
<!--                                                                        <el-dropdown-item>-->
<!--                                                                            <el-button icon="Connection" link size="small"-->
<!--                                                                                       type="primary"-->
<!--                                                                                       @click="associationList(scope.row)">-->
<!--                                                                                关联-->
<!--                                                                            </el-button>-->
<!--                                                                        </el-dropdown-item>-->
                                                                        <el-dropdown-item>
                                                                            <el-button icon="MessageBox" link size="small"
                                                                                       type="primary"
                                                                                       @click="viewHistory(scope.row)">
                                                                                历史版本
                                                                            </el-button>
                                                                        </el-dropdown-item>
                                                                        <!--																<el-dropdown-item>-->
                                                                        <!--																	<el-button icon="Clock" link size="small"-->
                                                                        <!--																			   type="success"-->
                                                                        <!--																			   @click="openDataHistory(scope.row)">-->
                                                                        <!--																		操作记录-->
                                                                        <!--																	</el-button>-->
                                                                        <!--																</el-dropdown-item>-->
                                                                    </el-dropdown-menu>
                                                                </template>
                                                            </el-dropdown>
                                                        </div>
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </el-main>
                                        <el-footer>
                                            <div style="display: flex;justify-content: flex-end">
                                                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
                                                            :page-sizes="[20,40,60,80]" :total="total" style="padding: 0"
                                                            @pagination="pagingMethod()"/>
                                            </div>
                                        </el-footer>
                                    </el-container>
                                </el-main>
                            </el-container>
                        </el-tab-pane>
                    </el-tabs>
                </el-main>
            </template>
        </DragLayout>
<!--		<el-aside style="border-right: 0;padding: 10px 0 10px 10px;background: none" width="21%">-->
<!--			<el-card :body-style="{ padding: '8px 0 0 0', height: '100%', width: '100%' }"-->
<!--					 style="height: 100%;width: 100%;">-->
<!--				<comDoorTable @clickChild="clickEven"/>-->
<!--			</el-card>-->
<!--		</el-aside>-->


		<!-- 装盒 -->
		<el-dialog v-if="openBoxing"
				   v-model="openBoxing"
				   :title="title"
				   append-to-body width="1000px">
			<Boxing :clickEvenId="clickEvenId" :handList="handList" @childMove="parentMove"/>
		</el-dialog>

		<!-- 查看 -->
		<el-dialog v-if="openView"
				   v-model="openView"
				   :title="title"
				   append-to-body
				   top="10vh"
				   width="90%">
			<viewFiles :receiveId="receiveId" @childMove="parentView"/>
		</el-dialog>

		<!-- 历史版本 -->
		<el-dialog v-if="openHistory"
				   v-model="openHistory"
				   :title="title"
				   append-to-body
				   top="9vh"
				   width="720px">
			<History :recordId="rowId" :recordVersion="rowVersion"/>
		</el-dialog>

		<!--操作记录-->
		<el-dialog v-model="openControlHistory" align-center append-to-body title="操作记录" width="42%">
			<div>
				<LogQuery ref="logRef3"/>
			</div>
		</el-dialog>

		<!-- 关联 -->
		<association :dataType="dataType" :openAssociation="openAssociation" :relevance="relevance"
					 :title="title" @childRelevance="parentRelevance"/>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue';
import {FullScreen, Refresh} from '@element-plus/icons-vue';
import collectList from '@/api/archive/archiveReception/collect';
import LogQuery from "@/components/detailsForm/logQuery.vue";
import tool from '@/utils/tool';
import moment from 'moment';
// 装盒弹窗
import Boxing from './boxing.vue';
import History from './history.vue';
import association from '../../archiveReception/receive/association.vue'
// 查看弹窗
import viewFiles from '../view.vue';
import receiveList from "@/api/archive/archiveReception/receive";
import DragLayout from "@/components/DragLayout/index.vue";
const data = reactive({
	queryParams: {
		current: 1,
		size: 20,
	}
});
const total = ref(0);
const {queryParams} = toRefs(data);
const {proxy} = getCurrentInstance();
// 接收库List
const receiveData = ref([]);
// 点击数据List
const handList = ref([]);
// 点击查询列表
const openTree = ref(false);
const loading = ref(false);
// 头部查询
const form = ref({
    boxStatus:2,
    isElectronic:0,
});
// 点击树结构的id
const clickEvenId = ref([]);
// 档案表格
// 有关联数据后弹窗显示关联数据
const RelevanceFileList = ref(false);
const openControlHistory = ref(false);
const relevanceList = ref([]);
// 关联数据
const relevance = ref({});
// 关联状态
const openAssociation = ref(false);
const openHistory = ref(false);
//点击后的行数据ID
const rowId = ref('');
const rowVersion = ref('');
// const returnLoading = ref(false);
// 查看receiveId
const receiveId = ref('');
const openView = ref(false);
// 操作流程
const logRef3 = ref(null);
const chooseTab = ref('first');
const tabList = ref([
	{
		"label": "未装盒",
		"name": "first"
	},
	{
		"label": "已装盒",
		"name": "second"
	}
]);

onMounted(() => {
	getList();
});

//档案批量导出
function collectUpload(tableId) {
	let selectIds = handList.value.map(item => item.id);
	console.log(selectIds);
	//文件名称
	let fileName = '档案信息导出数据包(请解压按照文件夹名称查看).zip';
	if (selectIds.length > 0) {
		loading.value = true;
		collectList.fileBatchExport({
			recordIds: selectIds.join(',')
		}).then((response) => {
			let blob = new Blob([response], {type: "application/zip"});
			console.log(blob);
			let url = window.URL.createObjectURL(blob);
			const link = document.createElement("a"); // 创建a标签
			link.href = url;
			link.download = fileName; // 重命名文件
			link.click();
			URL.revokeObjectURL(url); // 释放内存
			loading.value = false;
		}).catch(response => {
			console.log(response);
			proxy.msgError('导出失败');
			loading.value = false;
		})
	} else {
		proxy.msgError('请选择要导出的数据');
	}
}

function tabChange() {
	if (chooseTab.value === 'first') {
		form.value.boxStatus = 2;
		form.value.isElectronic = 0;
	} else if (chooseTab.value === 'second') {
		form.value.boxStatus = 1;
		form.value.isElectronic = 1;
	}
	handleQuery();
}

function parentRelevance() {
	openAssociation.value = false;
	// clickEven(receiveDataList.value);
}

//点击事件
function clickEven(val) {
	clickEvenId.value = val;
	clickEvenList(val);
}

//全屏
function screen() {
	let element = document.documentElement;
	tool.screen(element);
}

//数据查询
function handleQuery() {
	openTree.value = true;
    getList()
	// collectList.getArchiveList({
	// 	current: queryParams.value.current,
	// 	size: queryParams.value.size,
	// 	name: form.value.name,
	// 	boxStatus: form.value.boxStatus,
	// 	isElectronic: form.value.isElectronic,
	// 	numFormat: form.value.numFormat,
	// 	archiveStatus: 1,
	// 	delFlag: 0
	// }).then(res => {
	// 	if (res.code === 200) {
	// 		receiveData.value = res.data.records;
	// 		total.value = res.data.total;
	// 		openTree.value = false;
	// 	}
	// }).catch((error) => {
	// 	openTree.value = false;
	// 	proxy.msgError(error, '查询失败');
	// })
}

// 重置
function resetQuery() {
	form.value = [];
	clickEvenList(clickEvenId.value);
}

// 分页
function pagingMethod() {
	if (clickEvenId.value.length > 0) {
		clickEvenList(clickEvenId.value);
	} else {
		handleQuery()
	}
}

// 点击树结构查询表格
function clickEvenList(val) {
	openTree.value = true;
    form.value['controlGroup.id'] = undefined;
    form.value['controlCategory.id'] = undefined;
	if (val.dataType === '1') {
        form.value['controlGroup.id'] = val.id
	} else {
        form.value['controlCategory.id'] = val.id
	}
    getList();
}

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

// 弹窗 标题
const title = ref('')

//数据类型
const dataType = ref('')

// 是否打开装盒
const openBoxing = ref(false)

// 装盒
function collectBoxing() {
	let box = handList.value.filter(item => item.boxStatus === '1');
	if (handList.value.length > 0 && box.length === 0) {
		openBoxing.value = true;
	} else if (handList.value.length === 0) {
		proxy.msgError('请先选择需要装盒的数据！');
	} else if (box.length > 0) {
		let strings = box.map(item => item.name);
		proxy.msgError(strings.join(',') + '等档案已装盒！');
	}
}

//查看历史版本
function viewHistory(val) {
	title.value = '历史版本';
	rowId.value = val.id;
	rowVersion.value = val.version;
	openHistory.value = true;
}

// 装盒标题
function parentMove(val, type) {
	if (type == '1') {
		openBoxing.value = false;
		getList();
	} else {
		title.value = val;
	}
}

// 保留年限
function reserve(val) {
	if (val === 'Y') {
		return '永久'
	} else if (val === 'D5') {
		return '5年'
	} else if (val === 'D10') {
		return '10年 '
	} else if (val === 'D20') {
		return '20年'
	} else if (val === 'D30') {
		return '30年'
	} else {
		return '暂无'
	}
}

// 控制等级
function control(val) {
	if (val === '1') {
		return '公开'
	} else if (val === '2') {
		return '公司内部开放'
	} else if (val === '3') {
		return '部门内部开放 '
	} else if (val === '4') {
		return '控制'
	} else {
		return '暂无'
	}
}
//
// // 退回
// function collectReturn() {
// 	if (handList.value.length > 0) {
// 		returnLoading.value = true;
// 		proxy.$confirm('是否要退回吗?', '提示', {
// 			type: 'warning',
// 			confirmButtonText: "确定",
// 			cancelButtonText: "取消",
// 		}).then(() => {
// 			let idStr = handList.value.map((v) => v.id);
// 			//拼接的数组字符串，接口传参
// 			let ids = idStr.join(",");
// 			let data = {};
// 			data.archiveStatus = 0;
// 			data.infoIds = ids;
// 			collectList.infoOperate(data).then(res => {
// 				if (res.code === 200) {
// 					proxy.msgSuccess('退回成功');
// 					getList();
// 					returnLoading.value = false;
// 				} else {
// 					proxy.msgError('退回失败');
// 					returnLoading.value = false;
// 				}
// 			}).catch(() => {
// 				proxy.msgError('退回失败');
// 				returnLoading.value = false;
// 			})
// 		}).catch(() => {
// 			proxy.msgError('退回失败');
// 			returnLoading.value = false;
// 		})
// 	} else {
// 		proxy.msgError('请先选择退回数据！');
// 	}
// }

function collectFile(val) {
	title.value = '查看';
	receiveId.value = val;
	openView.value = true;
}

// //关联文件
// function associationList(val) {
// 	receiveList.queryById({
// 		current: 1,
// 		size: -1,
// 		fileOriginalId: val.id
// 	}).then(res => {
// 		if (res.code === 200) {
// 			if (res.data.records.length === 0) {
// 				openAssociation.value = true;
// 				title.value = '关联文件';
// 				dataType.value = 'info';
// 				relevance.value = val;
// 			} else {
// 				proxy.msgError('该条数据已有关联文件！');
// 				getRelevanceFileList(res.data.records[0])
// 			}
// 		}
// 	}).catch(() => {
// 		proxy.msgError('查询失败');
// 	})
// }

// //查看操作记录
// function openDataHistory(rowData) {
// 	openControlHistory.value = true;
// 	// basicInfoList.value.forEach(label => {
// 	// 	if (label.name === '操作记录' || label.name === '供应商审核记录' || label.name.includes('审核记录')) {
// 	// 		let recordData = label.dataValue ? JSON.parse(label.dataValue) : [];
// 	// 		recordData.forEach(record => {
// 	// 			records.push({
// 	// 				name: record.reviewer,
// 	// 				updateDate: record.review_time,
// 	// 				remark: record.remark,
// 	// 				reviewIdea: record.review_idea
// 	// 			});
// 	// 		});
// 	// 	}
// 	// });
// 	// logRef3.value.timeFns(null);
// }

// //关联版本历史文件
// function connectionHistory(val) {
// 	receiveList.queryById({
// 		current: 1,
// 		size: -1,
// 		fileOriginalId: val.id
// 	}).then(res => {
// 		if (res.code === 200) {
// 			if (res.data.records.length === 0) {
// 				openAssociation.value = true;
// 				title.value = '关联版本';
// 				dataType.value = 'version';
// 				relevance.value = val;
// 			} else {
// 				proxy.msgError('该条数据已有关联文件！');
// 				getRelevanceFileList(res.data.records[0])
// 			}
// 		} else {
// 			proxy.msgError(res.msg);
// 		}
// 	}).catch(() => {
// 		proxy.msgError('查询失败');
// 	})
// }

//获取关联文件列表
function getRelevanceFileList(val) {
	relevanceList.value = val;
	RelevanceFileList.value = true;
	title.value = '已关联数据';
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 进入时查询全部
function getList() {
	openTree.value = true;
	collectList.getArchiveList({
		current: queryParams.value.current,
		size: queryParams.value.size,
        ...form.value,
		archiveStatus: 1,
		delFlag: 0
	}).then(res => {
		if (res.code === 200) {
			receiveData.value = res.data.records;
			total.value = res.data.total;
			openTree.value = false;
		}
	}).catch(() => {
		openTree.value = false;
		proxy.msgError('查询失败');
	})
}
</script>

<style scoped>
:deep(.el-tabs__content) {
	width: 100%;
	height: 100%;
	padding: 10px;
}
</style>
