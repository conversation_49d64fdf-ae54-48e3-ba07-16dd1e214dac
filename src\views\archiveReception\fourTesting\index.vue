<template>
    <el-container>
		<el-aside style="border-right: 0;background: none" width="21%" class="p-el">
			<el-card class="h-full w-full" body-class="h-full w-full p-0">
                <categoryTree :is-the-first-level-clickable="true"  @clickNode="clickEven"/>
			</el-card>
		</el-aside>
		<el-main ref="main" class="p-el">
			<el-card :body-style="{ height: '100%', width: '100%', padding: '15px 15px 0 15px' }"
					 style="height: 100%;width: 100%">
				<el-container>
					<el-main style="padding: 0">
						<el-table :data="receiveData" border style="height: 100%;width: 100%">
							<!--                        <el-table-column align="center" min-width="30" type="selection" />-->
							<el-table-column align="center" label="档案四性检查项数量" prop="recordDetectionItemCount"/>
							<el-table-column align="center" label="档案四性检查结果" prop="recordDetectionResult"/>
							<el-table-column align="center" class-name="small-padding fixed-width" label="操作"
											 width="250px">
								<template #default="scope">
									<el-button icon="Document" link type="primary" @click="reportView(scope.row)">
										查看检测报告
									</el-button>
									<el-button icon="Refresh" link type="danger" @click="resettingResult(scope.row)">
										重置检测结果
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
										:total="total"
										style="padding: 0" @pagination="getList()"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 查看检测报告 -->
		<el-dialog v-if="open" v-model="open" :title="title" append-to-body style="min-height: 600px;" width="1300px">
			<report :receiveId="receiveId"></report>
		</el-dialog>
    </el-container>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import report from './report.vue'
import classificationManagement from "@/api/archive/classificationManagement"
import detection from "@/api/archive/archiveReception/fourTesting"

const { proxy } = getCurrentInstance();
const menuList = ref([])
// 树状列表查询menuLoading
const menuLoading = ref(false);
// 查询树状列表
function treeData() {
	menuLoading.value = true;
    classificationManagement.treeData().then(res => {
        if (res.code === 200) {
            menuList.value = res.data;
			menuLoading.value = false;
        }
    }).catch(() => {
        proxy.msgError('查询失败');
    })
}
// 接收库List
const receiveData = ref([])
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    }
})
const receiveId = ref()
const { queryParams } = toRefs(data)
const total = ref(0)
// 点击查询列表
const openTree = ref(false)
//查看弹窗状态
const open = ref(false);
// 查看标题
const title = ref('');
// 树形表点击id
const listId = ref('');

onMounted(() => {

});

// 查看数据
function reportView(data) {
    receiveId.value = data;
    open.value = true;
    title.value = '查看';
}

// 关联数据
function resettingResult(data) {
    proxy.$confirm('是否重置检测结果?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        let result = {};
        result.recordOriginal = data.recordOriginal;
        detection.save(result).then(res => {
            if (res.code === 200) {
                proxy.msgSuccess('重置成功')
            }
        }).catch(() => {
            proxy.msgError('重置失败');
        })
    }).catch(() => {
        console.log(111);
    })
}

function clickEven(val) {
    listId.value = val;
	getList();
}

function getList() {
    if (listId.value.dataType === '1') {
        detection.list({
            current: queryParams.value.current,
            size: queryParams.value.size,
            admsInfo: {
                groupId: listId.value.id
            }
        }).then(res => {
            if (res.code === 200) {
                openTree.value = true;
                receiveData.value = res.data.records;
                total.value = res.data.total
            }
        }).catch(() => {
            proxy.msgError('查询失败');
        })
    } else {
        detection.list({
            current: queryParams.value.current,
            size: queryParams.value.size,
            admsInfo: {
                categoryId: listId.value.id
            }
        }).then(res => {
            if (res.code === 200) {
                openTree.value = true;
                receiveData.value = res.data.records;
                total.value = res.data.total
            }
        }).catch(() => {
            proxy.msgError('查询失败');
        })
    }
}
</script>

<style scoped>
.my-label {
    background: var(--el-color-success-light-9);
}

.my-content {
    background: var(--el-color-danger-light-9);
}
</style>
