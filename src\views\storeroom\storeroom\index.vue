<template>
    <el-container>
        <DragLayout type="row">
            <template #first>
                <el-aside class="w-full h-full p-el" v-loading="menuloading">
                    <el-container class="sidebar-container">
                        <div class="sidebar-header">
                            <div class="header-actions">
                                <el-button type="primary" size="small" icon="el-icon-plus" @click="addLibrary('1', '1', '1')">新增档案库</el-button>
                                <el-button :icon="multipleSelectionState ? 'Check' : 'Select'" :type="multipleSelectionState ? 'primary' : 'default'" size="small" @click="switchToMultipleSelections">
                                    {{ multipleSelectionState ? '隐藏多选' : '显示多选' }}
                                </el-button>
                                <el-button v-if="multipleSelectionState" icon="Delete" size="small" type="danger" @click="delMenu"> 批量删除 </el-button>
                                <el-button :type="isUnfold ? 'success' : 'warning'" size="small" @click="unfoldClose()"
                                    ><el-icon :class="{ rotated: isUnfold }" class="unfold-icon">
                                        <arrow-down />
                                    </el-icon>
                                    {{ isUnfold ? '展开' : '收起' }}
                                </el-button>
                            </div>
                        </div>
                        <el-main class="noPadding">
                            <el-tree ref="menu" class="menu" node-key="id" :data="menuList" :props="menuProps" draggable :expand-on-click-node="false" check-strictly :default-expand-all="false" highlight-current :show-checkbox="multipleSelectionState" @node-click="menuClick">
                                <template #default="{ node, data }">
                                    <span class="tree-node">
                                        <div class="node-content">
                                            <el-icon v-if="node.level === 1" class="node-icon">
                                                <MessageBox />
                                            </el-icon>
                                            <el-icon v-else-if="node.level === 2" class="node-icon">
                                                <Files />
                                            </el-icon>
                                            <el-icon v-else class="node-icon">
                                                <Guide />
                                            </el-icon>
                                            <span class="node-label">{{ node.label }}</span>
                                            <div class="node-tags">
                                                <el-tag v-if="node.level === 1" size="small" type="primary">库</el-tag>
                                                <el-tag v-else-if="node.level === 2" size="small" type="success">室</el-tag>
                                                <el-tag v-else-if="node.level === 3" size="small" type="warning">柜</el-tag>
                                            </div>
                                        </div>

                                        <span class="node-actions">
                                            <el-button icon="Plus" size="small" type="primary" v-if="node.level < 3" title="添加" @click.stop="addLibrary('0', node, data)"></el-button>
                                            <el-button icon="Edit" type="warning" size="small" @click.stop="queryById(node, data)" title="编辑"></el-button>
                                            <el-button icon="Delete" plain size="small" title="删除" type="danger" @click.stop="delLibrary(data)" />
                                        </span>
                                    </span>
                                </template>
                            </el-tree>
                        </el-main>
                    </el-container>
                </el-aside>
            </template>
            <template #second>
                <el-container>
                    <el-main ref="main" class="h-full p-el" v>
                        <!-- 档案库展示 -->
                        <div v-if="detailsOpen" class="h-full">
                            <library ref="libraryRef" :key="componentKey" @libraryView="libraryView" :parentDataInfo="parentDataInfo"></library>
                        </div>
                        <el-card v-if="openShelves || openContainer || openStorey" body-class="p-el h-full" class="h-full">
                            <el-button v-if="detailsOpen === false" icon="Back" plain size="default" style="margin: 10px 0 0 20px" type="primary" @click="returnWarehouseView">返回仓库总览 </el-button>
                            <!-- 档案室展示 -->
                            <div v-if="openShelves" class="h-full">
                                <shelves :key="componentKey" @shelvesView="shelvesView" :parentDataInfo="parentDataInfo"></shelves>
                            </div>
                            <!-- 柜展示 -->
                            <div v-if="openContainer">
                                <container :key="componentKey" @handleView="handleView" :parameters="parameters"></container>
                            </div>
                            <!-- 格子展示 -->
                            <div v-if="openStorey">
                                <lattice :key="componentKey" :latticeView="latticeView"></lattice>
                            </div>
                        </el-card>
                    </el-main>
                    <!-- 新增 、修改-->
                    <el-dialog :title="title" v-model="openAdd" width="800px" append-to-body v-show="openAdd">
                        <newAdd v-if="openAdd" :filesType="filesType" :ID="ID" :mainID="mainID" :parentId="parentId" :parentTargetId="parentTargetId" @openNewAdd="openNewAdd" @getDataList="getDataList"></newAdd>
                    </el-dialog>
                    <!-- <el-dialog title="密集柜详情" v-model="openJoint" width="1000px" append-to-body v-if="openJoint">
                        <el-table :data="tableData" style="width: 100%" row-key="id" lazy :load="load" height="650"
                            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                            <el-table-column prop="name1" label="密集柜编码">
                            </el-table-column>
                            <el-table-column prop="name2" label="提名/盒名">
                            </el-table-column>
                            <el-table-column prop="name3" label="档号/盒名">
                            </el-table-column>
                            <el-table-column prop="name4" label="上架类型">
                            </el-table-column>
                            <el-table-column prop="name5" label="存址">
                            </el-table-column>
                        </el-table>
                    </el-dialog> -->
                </el-container>
            </template>
        </DragLayout>
    </el-container>
</template>

<script>
import storeroomData from '@/api/archive/storeroom/storeroom';
import shelves from './shelves';
import VisualWarehouseDiagram from './VisualWarehouseDiagram';
import container from './container';
import lattice from './lattice';
import newAdd from './newAdd/newAdd';
import library from './library';
import { getOrganizationInfo } from '@/utils/permission';
import DragLayout from '@/components/DragLayout/index.vue';
import { MessageBox, Files, ArrowDown, Guide } from '@element-plus/icons-vue';

// import { node } from "@/api/model/systemDeploy/auditAndFlow";
export default {
    name: 'storeroom',
    components: {
        DragLayout,
        shelves,
        container,
        lattice,
        newAdd,
        library,
        VisualWarehouseDiagram,
        MessageBox,
        Files,
        ArrowDown,
        Guide
    },

    data() {
        return {
            multipleSelectionState: false,
            isUnfold: true,
            // 右侧仓库展示
            detailsOpen: true,
            componentKey: 0,
            //父级关联Id
            parentTargetId: '',
            //父级ID
            parentId: '',
            mainID: '',
            ID: '',
            title: '',
            // 新增弹窗
            openAdd: false,
            // 分页
            queryParams: {
                current: 1,
                size: 10
            },
            total: 0,
            // 库房数据
            storeroomList: [],
            parentDataInfo: [],
            // 所属区域
            houseRegion: '',
            // 菜单加载中
            menuloading: false,
            // 菜单列表
            menuList: [],
            menuProps: {
                label: (data) => {
                    return data.name;
                }
            },
            // 柜子查看展示
            openStorey: false,
            // 货架展示
            openShelves: false,
            latticeView: '',
            // 货架
            list: [
                {
                    name: '1',
                    name1: '2',
                    name2: '3',
                    name3: '4',
                    name4: '5',
                    name5: '6',
                    name6: '7',
                    name7: '8',
                    name8: '9',
                    name9: '10',
                    name10: '10'
                },
                {
                    name: '1',
                    name1: '2',
                    name2: '3',
                    name3: '4',
                    name4: '5',
                    name5: '6',
                    name6: '7',
                    name7: '8',
                    name8: '9',
                    name9: '10',
                    name10: '10'
                }
            ],
            // 货柜form
            form: {},
            // 货柜编辑弹窗
            open: false,
            // 展示货柜页
            openContainer: false,
            // 档案柜参数
            parameters: [],
            // 密集柜详情
            openJoint: false,

            //查询表单
            searchForm: {},
            // 档案库区分
            filesType: ''
        };
    },
    watch: {
        //   //菜单过滤，顶部搜索框
        //   menuFilterText(val) {
        //     this.$refs.menu.filter(val);
        //   },
    },
    mounted() {
        this.getDataList();
        this.detailsOpen = true;
    },
    methods: {
        // 切换多选
        switchToMultipleSelections() {
            this.multipleSelectionState = !this.multipleSelectionState;
        },
        /**
         * 展开关闭
         */
        unfoldClose() {
            const nodes = this.$refs.menu.store._getAllNodes();
            nodes.forEach((node) => {
                node.expanded = this.isUnfold;
            });

            this.isUnfold = !this.isUnfold;
        },
        returnWarehouseView() {
            this.parentDataInfo = {};
            this.detailsOpen = true;
            this.openShelves = false;
            this.openContainer = false;
            this.openStorey = false;
        },
        // 左侧树结构点击事件
        menuClick(data, node, type) {
            this.detailsOpen = false;
            this.componentKey += 1;
            switch (data.recordHouseInfoType || type) {
                case '1':
                    this.parentDataInfo = data;
                    this.openShelves = true;
                    this.detailsOpen = false;
                    this.openContainer = false;
                    this.openStorey = false;
                    break;
                case '2':
                    this.parameters = data;
                    this.detailsOpen = false;
                    this.openShelves = false;
                    this.openContainer = true;
                    this.openStorey = false;
                    break;
                case '3':
                    this.latticeView = data.recordHouseInfoTargetId;
                    this.detailsOpen = false;
                    this.openShelves = false;
                    this.openContainer = false;
                    this.openStorey = true;
                    break;
            }
        },

        // 新增库房
        addLibrary(type, node, data) {
            if (data.recordHouseInfoType === '1') {
                this.ID = '';
                this.mainID = '';
                this.filesType = '2';
                this.parentId = data.id;
                this.title = '新增档案室';
                this.parentTargetId = data.recordHouseInfoTargetId;
                this.openAdd = true;
            } else if (data === '1') {
                this.ID = '';
                this.mainID = '';
                this.filesType = '1';
                this.title = '新增档案库';
                this.openAdd = true;
            } else if (data.recordHouseInfoType === '2') {
                this.ID = '';
                this.mainID = '';
                this.filesType = '3';
                this.parentId = data.id;
                this.title = '新增档案柜';
                this.parentTargetId = data.recordHouseInfoTargetId;
                this.openAdd = true;
            }
        },

        //右侧点击查看库房详情
        libraryView(data) {
            this.menuClick(data, null, '1');
        },
        // 右侧点击查看档案室详情
        shelvesView(data) {
            this.menuClick(data, null, '2');
        },

        // 查看柜详情
        handleView(data) {
            this.latticeView = data.id;
            this.detailsOpen = false;
            this.openShelves = false;
            this.openContainer = false;
            this.openStorey = true;
        },

        // 查看节
        viewJoint() {
            this.openJoint = true;
        },

        // 库房查询
        getDataList() {
            this.openAdd = false;
            this.menuloading = true;
            //初始化数据列表
            this.menuList = [];
            this.mainID = '';
            storeroomData.treeData({ orgId: getOrganizationInfo()?.id }).then((res) => {
                if (res.code === 200) {
                    this.menuList = res.data;
                    this.menuloading = false;
                }
            });
        },

        /*
         * 删除档案库
         * @author: saya
         * @date: 2023-03-23 17:46:37
         */
        async delMenu() {
            let _this = this;
            //获取选中的节点
            let CheckedNodes = _this.$refs.menu.getCheckedNodes();
            if (CheckedNodes.length === 0) {
                _this.$message.warning('请选择需要删除的项');
                return false;
            }
            //删除操作确认
            await _this
                .$confirm('确认删除已选择的菜单吗？', '提示', {
                    type: 'warning',
                    confirmButtonText: '删除',
                    confirmButtonClass: 'el-button--danger',
                    cancelButtonText: '取消',
                    cancelButtonClass: 'el-button--primary el-button--large'
                })
                .then(() => {
                    _this.menuloading = true;
                    const ids = CheckedNodes.map((item) => item.id);
                    storeroomData
                        .deleteInfo({ ids: ids.toString() })
                        .then((res) => {
                            if (res.code === 200) {
                                //在列表中移除已删除的菜单项
                                CheckedNodes.forEach((item) => {
                                    let node = _this.$refs.menu.getNode(item);
                                    //移除菜单项
                                    _this.$refs.menu.remove(item);
                                    if (node.isCurrent) {
                                        //当前删除的是当前编辑的菜单，则清空编辑表单页面

                                    }
                                });
                                _this.detailsOpen = false;
                                _this.getDataList();
                                _this.returnWarehouseView();
                                _this.$refs['libraryRef'].selectList();
                                _this.$message.success('删除成功');
                            } else if (res.code === 500) {
                                _this.$message.error(res.msg);
                            }
                        })
                        .catch((error) => {
                            _this.$Response.errorNotice(error, '删除失败');
                        })
                        .finally(() => {
                            _this.menuloading = false;
                        });
                })
                .catch(() => {
                    //error为cancel
                });
        },
        /**
         * 删除
         * @param data
         */
        delLibrary(data) {
            let _this = this;
            //删除操作确认
            // 删除逻辑
            this.$confirm('确认删除此的门类吗？', '提示', {
                type: 'warning',
                confirmButtonText: '删除',
                confirmButtonClass: 'el-button--danger',
                cancelButtonText: '取消',
                cancelButtonClass: 'el-button--primary el-button--large'
            })
                .then(async () => {
                    _this.menuloading = true;
                    storeroomData
                        .deleteInfo({ ids: data.id })
                        .then((res) => {
                            if (res.code === 200) {
                                //当前删除的是当前编辑的菜单，则清空编辑表单页面
                                _this.detailsOpen = false;
                                _this.getDataList();
                                _this.returnWarehouseView();
                                _this.$refs['libraryRef'].selectList();
                                _this.msgSuccess('删除成功');
                            } else if (res.code === 500) {
                                _this.msgError(res.msg);
                            }
                        })
                        .catch((error) => {
                            _this.msgError(error, '删除失败');
                        })
                        .finally(() => {
                            _this.menuloading = false;
                        });
                })
                .catch(() => {
                    this.msgInfo('已取消删除');
                });
        },

        /*
         * 根据Id获取仓库配置数据
         * @author: saya
         */
        queryById(node, data) {
            switch (data.recordHouseInfoType) {
                case '1':
                    this.mainID = data.id;
                    this.ID = data.recordHouseInfoTargetId;
                    this.filesType = data.recordHouseInfoType;
                    this.title = '修改档案库';
                    this.openAdd = true;
                    break;
                case '2':
                    this.mainID = data.id;
                    this.parentId = data.parentId;
                    this.ID = data.recordHouseInfoTargetId;
                    this.filesType = data.recordHouseInfoType;
                    this.title = '修改档案室';
                    this.openAdd = true;
                    break;
                case '3':
                    this.mainID = data.id;
                    this.parentId = data.parentId;
                    this.ID = data.recordHouseInfoTargetId;
                    this.filesType = data.recordHouseInfoType;
                    this.title = '修改档案柜';
                    this.openAdd = true;
                    break;
            }
        },
        // 关闭新增弹窗
        openNewAdd() {
            this.detailsOpen = false;
            this.getDataList();
            this.returnWarehouseView();
            this.$refs['libraryRef'].selectList();
            this.openAdd = false;
        }
    }
};
</script>

<style scoped>
.sidebar-container {
    background: #ffffff;
    border-radius: 10px;
    box-shadow: var(--el-box-shadow-light);
    padding: 0;
    transition: box-shadow 0.2s;
    display: flex;
    flex-direction: column;
}
.sidebar-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    color: white;
    border-radius: 8px 8px 0 0;
}

.header-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}
.header-actions .el-button + .el-button {
    margin-left: 0;
}
.unfold-icon {
    transition: transform 0.3s ease;
    margin-right: 2px;
}

.unfold-icon.rotated {
    transform: rotate(180deg);
}
.menu {
    background: #fff;
    padding: 12px 10px;
    min-height: 400px;
    transition: box-shadow 0.2s;
}
.menu:deep(.el-tree-node__content) {
    border-radius: 4px;
    margin-bottom: 2px;
    padding: 0 8px;
    transition: background 0.2s;
}
.menu:deep(.el-tree-node__content:hover) {
    background: #f0f7ff;
}

.menu:deep(.el-tree-node__label) {
    display: flex;
    flex: 1;
    height: 100%;
}

.tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.node-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.node-icon {
    margin-right: 8px;
    color: #909399;
}

.node-label {
    font-size: 14px;
    margin-right: 8px;
}
.node-tags {
    display: flex;
    gap: 5px;
}

.node-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    margin-right: 5px;
}

.tree-node:hover .node-actions {
    opacity: 1;
}
.node-actions .el-button + .el-button {
    margin-left: 0;
}
</style>
