<template>
	<div class="archive-info-form">
        <div v-if="isShowTitle" class="header-title">档案信息</div>
		<el-form
			ref="formRef"
			:model="formData"
			:rules="rules"
			label-width="120px"
			class="archive-form"
		>
			<el-form-item label="档案年份:" prop="year">
				<el-date-picker
					v-model="formData.year"
                    :disabled="disabledObj.year"
                    style="width: 100%"
					clearable
					format="YYYY"
					placeholder="请选择档案年份"
					type="year"
				/>
			</el-form-item>
			<el-form-item label="档案月份:" prop="month">
				<el-date-picker
                    style="width: 100%"
					v-model="formData.month"
					clearable
                    :disabled="disabledObj.month"
					format="MM"
					placeholder="请选择档案月份"
					type="month"
				/>
			</el-form-item>
			<el-form-item
				label="保留年限:"
				prop="retentionPeriod"
			>
				<el-select
					v-model="formData.retentionPeriod"
                    :disabled="disabledObj.retentionPeriod"
					clearable
					placeholder="请选择保留年限"
					style="width: 100%"
				>
					<el-option label="永久" value="Y" />
					<el-option label="5年" value="D5" />
					<el-option label="10年" value="D10" />
					<el-option label="20年" value="D20" />
					<el-option label="30年" value="D30" />
				</el-select>
			</el-form-item>
			<el-form-item
				label="保密密级:"
				prop="protectLevel"
			>
				<el-select
					v-model="formData.protectLevel"
                    :disabled="disabledObj.protectLevel"
					clearable
					placeholder="请选择保密密级"
					style="width: 100%"
				>
					<el-option label="公开" value="GK" />
					<el-option label="限制" value="KZ" />
					<el-option label="秘密" value="MOM" />
					<el-option label="机密" value="JM" />
					<el-option label="绝密" value="UM" />
				</el-select>
			</el-form-item>
			<el-form-item
				label="控制等级:"
				prop="controlStatus"
			>
				<el-select
					v-model="formData.controlStatus"
                    :disabled="disabledObj.controlStatus"
					clearable
					placeholder="请选择控制等级"
					style="width: 100%"
					@change="controlGroupChange"
				>
					<el-option label="公开" value="1" />
					<el-option label="公司内部开放" value="2" />
					<el-option label="部门内部开放" value="3" />
					<el-option
						:disabled="!formData?.org?.id"
						label="控制"
						value="4"
					>
						<el-text v-if="formData?.org?.id">控制</el-text>
						<el-tooltip
							v-else
							content="请先选择组织机构"
							effect="dark"
							placement="right"
						>
							<el-text type="info">控制</el-text>
						</el-tooltip>
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item
				label="所属机构:"
				prop="org.id"
			>
				<el-select
					v-model="formData.org.id"
                    :disabled="disabledObj.org"
					clearable
					placeholder="请选择所属机构"
					style="width: 100%"
					@change="departmentList"
				>
					<el-option v-for="item in institution" :key="item.id" :label="item.name" :value="item.id"/>
				</el-select>
			</el-form-item>
			<el-form-item
				v-if="formData.controlStatus === '4'"
				label="可查看人员:"
				prop="ruleUserId"
			>
				<el-select
					v-model="formData.ruleUserId"
                    :disabled="disabledObj.ruleUserId"
					clearable
					collapse-tags
					collapse-tags-tooltip
					multiple
					placeholder="请选择可查看人员"
					style="width: 100%"
				>
					<el-option v-for="item in userByOrg" :key="item.id" :label="item.name" :value="item.id"/>
				</el-select>
			</el-form-item>
			<el-form-item
				label="所属部门:"
				prop="office.id"
			>
				<el-select
					v-model="formData.office.id"
                    :disabled="disabledObj.office"
					clearable
					placeholder="请选择所属部门"
					style="width: 100%"
				>
					<el-option v-for="item in department" :key="item.id" :label="item.name" :value="item.id"/>
				</el-select>
			</el-form-item>
			<el-form-item
				label="是否全电文档:"
				prop="isElectronic"
			>
				<el-select
					v-model="formData.isElectronic"
                    :disabled="disabledObj.isElectronic"
					clearable
					placeholder="请选择是否全电文档"
					style="width: 100%"
				>
					<el-option label="否" value="0" />
					<el-option label="是" value="1" />
				</el-select>
			</el-form-item>
			<el-form-item label="档案标签:" prop="tagInfo">
				<tagSelection
					:selected-tag-str="formData.tagInfo"
					:show-button="!disabledObj.tagInfo"
					@update:selected-tag-str="returnToTheSelectionTabs"
				/>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup>
import { ref, watch,onBeforeMount,getCurrentInstance ,defineExpose} from "vue";
import tagSelection from "@/components/tagSelection/index.vue";
import sysOrgService from "@/api/model/sys/sysOrgService";
import sysOfficeService from "@/api/model/sys/sysOfficeService";
import {node} from "@/api/model/systemDeploy/auditAndFlow";
const { proxy } = getCurrentInstance();
const props = defineProps({
	modelValue: {
		type: Object,
		default: () => ({
			year: "", // 档案年份
			month: "", // 档案月份
			retentionPeriod: "", // 保留年限
			protectLevel: "", // 保密等级
			controlStatus: "", // 控制等级
			ruleUserId: [], // 可查看人员
			org: { id: "" }, // 所属机构
			office: { id: "" }, // 所属部门
			isElectronic: "", // 是否全电文档
			tagInfo: "", // 档案标签
		}),
	},
    disabledObj:{
        type: Object,
        default: () => ({
            year: false,
            month: false,
            retentionPeriod:false,
            protectLevel: false,
            controlStatus:false,
            ruleUserId: false,
            org: false,
            office: false,
            isElectronic: false,
            tagInfo: false,
        }),
    },
    isShowTitle:{
        type:Boolean,
        default:true
    }
});

const formRef = ref(null);

const institution = ref([]);// 所属机构
const department = ref([]); // 部门列表
const userByOrg = ref([]);// 用户列表

// 表单数据
const formData = ref({
	year: props.modelValue.year || "", // 档案年份
	month: props.modelValue.month || "", // 档案月份
	retentionPeriod: props.modelValue.retentionPeriod || "", // 保留年限
	protectLevel: props.modelValue.protectLevel || "", // 保密等级
	controlStatus: props.modelValue.controlStatus || "", // 控制等级
	ruleUserId: props.modelValue.ruleUserId || [], // 可查看人员
	org: props.modelValue.org || { id: "" }, // 所属机构
	office: props.modelValue.office || { id: "" }, // 所属部门
	isElectronic: props.modelValue.isElectronic || "", // 是否全电文档
	tagInfo: props.modelValue.tagInfo || '', // 档案标签
});

// 表单验证规则
const rules = ref({
	year: [{ required: true, message: "请选择档案年份", trigger: "change" }],
	month: [{ required: true, message: "请选择档案月份", trigger: "change" }],
	retentionPeriod: [
		{ required: true, message: "请选择保留年限", trigger: "change" },
	],
	protectLevel: [
		{ required: true, message: "请选择保密密级", trigger: "change" },
	],
	controlStatus: [
		{ required: true, message: "请选择控制等级", trigger: "change" },
	],
	org: {
		id: [{ required: true, message: "请选择所属机构", trigger: "change" }],
	},
	office: {
		id: [{ required: true, message: "请选择所属部门", trigger: "change" }],
	},
	isElectronic: [
		{ required: true, message: "请选择是否全电文档", trigger: "change" },
	],
});

// 监听modelValue
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            Object.assign(formData.value, newVal);
            controlGroupChange(formData.value?.controlStatus)
        }
    }
);
// 设置某一项为非必填
function setRequired(item, required) {
    if (rules.value[item] && Array.isArray(rules.value[item])) {
        rules.value[item][0].required = required;
    } else{
        rules.value[item].id[0].required = required;
    }
}


/**
 * 返回标签
 * @param data
 */
function returnToTheSelectionTabs(data){
    formData.value.tagInfo = data;
}

// 表单验证方法
const validateForm = async () => {
    return await formRef.value?.validate();
};

// 重置表单
const resetForm = () => {
	formRef.value?.resetFields();
	formData.value.tagInfo = '';
};

// 暴露方法给父组件
defineExpose({
    validateForm,
	resetForm,
    getFormData,
    setRequired
});
onBeforeMount(() => {
    institutionList(); // 查询机构信息
    departmentList(); // 查询部门信息
    controlGroupChange(formData.value?.controlStatus)
});
function getFormData(){
    return formData.value
}

//指定档案可查看人员
function controlGroupChange(chooseValue) {
    if (chooseValue === '4') {
        node.staff({"sysOrg.id": formData.value?.org?.id}).then((res) => {
            userByOrg.value = res.data.records;
        });
    }
}

// 查询机构信息
function institutionList() {
    sysOrgService
        .list({
            current: 1,
            size: -1,
        })
        .then((res) => {
            if (res.code === 200) {
                institution.value = res.data.records;
            }
        })
        .catch(() => {
            proxy.msgError("查询失败");
        });
}

// 根据组织机构查询部门
function departmentList() {
    sysOfficeService
        .list({
            current: 1,
            size: -1,
            "sysOrg.id": formData.value?.org?.id,
            "pparent.id": 0,
        })
        .then((res) => {
            if (res.code === 200) {
                department.value = res.data.records;
            }
        })
        .catch(() => {
            proxy.msgError("查询失败");
        });
}
</script>

<style scoped>
.archive-info-form {
	background: #fff;
	padding: 15px;
	border-radius: 8px;
}
.header-title{
    font-size: 16px;
	font-weight: bold;
	margin-bottom: 20px;
}

.archive-form {
	margin-top: 20px;
}

</style>
