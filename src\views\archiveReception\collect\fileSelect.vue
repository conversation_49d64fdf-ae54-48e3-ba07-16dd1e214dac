<template>
	<el-container>
		<el-main class="noPadding">
			<div v-if="!open" class="h-full w-full">
                <dataClassificationTree :default-expand-all="true" height="500px"  @clickNode="menuClick"/>
			</div>
			<div v-if="open" class="h-full w-full">
				<el-container>
					<el-main class="noPadding">
                        <categoryTree :is-the-first-level-clickable="true" height="500px"  @clickNode="clickEven"/>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<el-button type="primary" @click="() => collect()">确定</el-button>
						</div>
					</el-footer>
				</el-container>
			</div>
		</el-main>
	</el-container>
</template>
<script setup>
import {defineEmits, getCurrentInstance, ref} from 'vue'
import dataClassificationTree from "@/components/dataClassificationTree/index.vue";

const {proxy} = getCurrentInstance()
const emit = defineEmits(["menuPropsList", "clickEvenListAdd", "changeTitle"]);
// 点击树结构的id
const clickEvenListAdd = ref({})
// 是否选择分类
const open = ref(false);
const menuPropsList = ref({})

// 点击查询分类列表
function menuClick(data) {
	if (data && data.parentId !== '0' && !data.children || data.children.length === 0) {
		open.value = true;
		menuPropsList.value = data;
		emit("changeTitle", "选择门类");
		// emit("menuPropsList",data);
	} else {
		proxy.msgError('表单只能选择最后一级');
	}
}

function clickEven(val) {
    if(!val.children || val.children.length === 0){
        clickEvenListAdd.value = val;
    }else{
        proxy.msgError('门类只能选择最后一级');
    }

}

function collect() {
    if(menuPropsList.value?.id && clickEvenListAdd.value?.id){
        emit("menuPropsList", menuPropsList.value);
        emit("clickEvenListAdd", clickEvenListAdd.value);
    } else {
        return proxy.msgError('请选择门类');
    }


}
</script>

<style scoped></style>
