<template>
    <div class="category-management">
        <DragLayout type="row">
            <template #first>
                <el-aside class="h-full w-full p-el">
                    <div class="sidebar-container">
                        <div class="sidebar-header">
                            <div class="header-actions">
                                <el-button :icon="multipleSelectionState ? 'Check' : 'Select'" :type="multipleSelectionState ? 'primary' : 'default'" size="small" @click="switchToMultipleSelections">
                                    {{ multipleSelectionState ? '隐藏多选' : '显示多选' }}
                                </el-button>
                                <el-button v-if="multipleSelectionState" icon="Delete" size="small" type="danger" @click="batchDeletion"> 批量删除 </el-button>
                                <el-button :type="isUnfold ? 'success' : 'warning'" size="small" @click="unfoldClose()">
                                    <el-icon :class="{ rotated: isUnfold }" class="unfold-icon">
                                        <arrow-down />
                                    </el-icon>
                                    {{ isUnfold ? '展开' : '收起' }}
                                </el-button>
                            </div>
                        </div>
                        <div v-loading="menuLoading" class="sidebar-content">
                            <el-tree ref="menu" :data="menuList" :default-expand-all :expand-on-click-node="false" :props="menuProps" :show-checkbox="multipleSelectionState" check-strictly class="category-tree" draggable highlight-current node-key="id" @node-click="menuClick">
                                <template #default="{ node, data }">
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <el-icon v-if="data.isGroup" class="node-icon">
                                                <Notebook />
                                            </el-icon>
                                            <el-icon v-else class="node-icon">
                                                <Postcard />
                                            </el-icon>
                                            <span class="node-label">{{ node.label }}</span>
                                            <div class="node-tags">
                                                <el-tag v-if="data.isGroup" size="small" type="primary">全宗</el-tag>
                                                <el-tag :type="data.openFlag === '1' ? 'success' : 'danger'" size="small">{{ selectDictLabel(openFlagOptions,data.openFlag) }}</el-tag>
                                            </div>
                                        </div>
                                        <div v-if="determineWhetherItIsEditable(data)" class="node-actions">
                                            <el-button icon="Plus" plain size="small" title="添加子门类" type="primary" @click.stop="add(node, data)" />
                                            <template v-if="!data.isGroup">
                                                <el-button icon="Edit" plain size="small" title="编辑门类" type="warning" @click.stop="queryById(node, data)" />
                                                <el-button icon="Delete" plain size="small" title="删除门类" type="danger" @click.stop="delMenu(data)" />
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </el-tree>
                        </div>
                    </div>
                </el-aside>
            </template>
            <template #second>
                <el-main ref="main" class="h-full w-full p-el">
                    <div class="main-container">
                        <div v-if="!details" class="empty-state">
                            <el-empty description="请选择左侧门类进行操作">
                                <template #image>
                                    <el-icon class="empty-icon"><FolderOpened /></el-icon>
                                </template>
                            </el-empty>
                        </div>
                        <el-main v-else ref="main" class="detail-container noPadding">
                            <el-tabs v-model="activeName">
                                <el-tab-pane label="详情" name="first">
                                    <div class="detail-content">
                                        <CategoryForm ref="categoryDetail" :disabled="true" :info="categoryInfo" :show-actions="false" mode="view" />
                                    </div>
                                </el-tab-pane>
                                <el-tab-pane v-if="childrenDataList?.length > 0" label="下级门类" name="second">
                                    <el-table :data="childrenDataList" border>
                                        <el-table-column align="center" label="所属全宗" prop="recordGroupId">
                                            <template #default="scope">
                                                {{ findGroupName(scope.row.recordGroupId) }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column align="center" label="门类名称" prop="name" />
                                        <el-table-column align="center" label="门类编号" prop="num" />
                                        <el-table-column align="center" label="门类整理方式" prop="recordCategoryType" />
                                        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" min-width="100px">
                                            <template #default="scope">
                                                <el-button link v-if="determineWhetherItIsEditable(scope.row)" type="primary" @click="handleAdd(scope.row)"><img src="@/assets/icons/update.png" style="margin-right: 5px" />编辑 </el-button>
                                                <el-button link type="danger" v-if="determineWhetherItIsEditable(scope.row)" @click="handleDelete(scope.row)"><img src="@/assets/icons/delete.png" style="margin-right: 5px" />删除 </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-tab-pane>
                            </el-tabs>
                        </el-main>
                    </div>
                </el-main>
            </template>
        </DragLayout>

        <!-- 编辑弹窗 -->
        <el-dialog v-model="open" :close-on-click-modal="false" :title="title" class="category-dialog" width="80%" @opened="onDialogOpened">
            <CategoryForm ref="categoryEdit" :disabled="false" :info="categoryInfo" mode="edit" @submit-success="handleSubmitSuccess" @submit-error="handleSubmitError" />
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="handleCancel">取消</el-button>
                    <el-button :loading="submitLoading" type="primary" @click="handleConfirmation"> 确定 </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, getCurrentInstance, nextTick } from 'vue';
import { Notebook, FolderOpened, Postcard, ArrowDown } from '@element-plus/icons-vue';
import DragLayout from '@/components/DragLayout/index.vue';
import CategoryForm from './CategoryForm.vue';
import category from '@/api/archive/categoryManagement/category';
import completeManagement from '@/api/archive/systemConfiguration/completeManagement';

import { isTianTun, getOrganizationInfo } from '@/utils/permission';
import { recursiveSort } from '@/utils';
const { proxy } = getCurrentInstance();
// 响应式数据
const multipleSelectionState = ref(false);
const isUnfold = ref(true);
const details = ref(false);
const open = ref(false);
const submitLoading = ref(false);
const currentNode = ref(null);
const menu = ref(null); // 树形控件
const menuList = ref([]); // 树形数据
const menuLoading = ref(false); // 树形数据加载状态
const childrenDataList = ref([]); // 下级门类
const menuProps = {
    label: (data) => {
        return data.name;
    }
};
const activeName = ref('first');
const completeList = ref([]); // 全宗列表
// 门类数据
const categoryInfo = ref({});

// 计算属性
const title = computed(() => {
    return categoryInfo.value.id ? '编辑门类' : '新增门类';
});
const categoryEdit = ref(null);
const categoryDetail = ref(null);
const openFlagOptions = ref([]);

// 切换多选
const switchToMultipleSelections = () => {
    multipleSelectionState.value = !multipleSelectionState.value;
};
/**
 * 展开关闭
 */
const unfoldClose = () => {
    const nodes = menu.value.store._getAllNodes();
    nodes.forEach((node) => {
        node.expanded = isUnfold.value;
    });

    isUnfold.value = !isUnfold.value;
};
/*
 * 门类点击
 * @author: saya
 * @date: 2023-03-23 11:23:10
 */
const menuClick = async (data) => {
    if (!data.isGroup) {
        currentNode.value = data;
        details.value = true;
        await getChildrenList(data.name, data.groupId, data.id);
        categoryInfo.value = data;
        nextTick(() => {
            if (categoryDetail.value && typeof categoryDetail.value.loadDetail === 'function') {
                categoryDetail.value.loadDetail();
            }
        });
    }
};
const getChildrenList = async (name, recordGroupId, parentId) => {
    try {
        const res = await category.getCategoryList({
            recordGroupId,
            'parent.id': parentId,
            current: 1,
            size: -1
        });
        if (res.code === 200) {
            childrenDataList.value = res?.data?.records || [];
        }
    } catch (e) {
        proxy.msgError(`获取${name}子门类列表失败`);
    }
};

const handleConfirmation = async () => {
    submitLoading.value = true;
    // 提交逻辑
    nextTick(() => {
        categoryEdit.value.handleSubmit();
    });
};

const handleSubmitSuccess = () => {
    open.value = false;
    submitLoading.value = false;
    // 刷新列表
    getCategoryList();
    // 刷新右侧详情
    if (currentNode.value) {
        menuClick(currentNode.value);
    }
};

const handleSubmitError = () => {
    submitLoading.value = false;
};

const handleCancel = () => {
    open.value = false;
};

// 添加...
const add = async (node, data) => {
    const groupId = data.dataType === '' ? data.id : data.groupId;
    // 添加逻辑
    //组装基本门类数据
    let newMenuName = '未命名' + (data ? (data.children ? data.children.length + 1 : '1') : menuList.value.length + 1);
    let newMenuData = {
        //父级ID
        parent: {
            id: data ? data.id : '0'
        },
        // 名称
        name: newMenuName,
        // 档案门类编号
        num: '',
        // 全宗ID
        recordGroupId: groupId,
        // 整理方式
        recordCategoryType: '未填写',
        // 排序
        sort: data ? (data.children ? data.children.length + 1 : '1') : this.menuList.length + 1,
        openFlag: data.openFlag, // 数据权限 1:公共 2：私有
        org: getOrganizationInfo()
    };
    //页面加载中
    menuLoading.value = true;
    // 保存门类方法
    let res = await category.save(newMenuData);
    if (res.code === 200) {
        // this.getCategoryList();
        proxy.msgSuccess('添加成功');
    } else {
        proxy.msgError(res, '添加失败');
    }
    //释放页面加载
    menuLoading.value = false;
    newMenuData.id = res.data.id;
    //动态追加添加后的门类节点
    menu.value.append(newMenuData, node);
    //设置门类列表当前节点的焦点
    menu.value.setCurrentKey(newMenuData.id);
    //设置表单数据
    await getCategoryList();
};

const queryById = (node, data) => {
    // 查询逻辑
    categoryInfo.value = data;
    open.value = true;
};
/**
 * 右边下级门类编辑
 * @return {*}
 */
const handleAdd = async (data) => {
    open.value = true;
    categoryInfo.value = data;
};
/**
 * 右侧下级门类删除
 * @return {*}
 */
const handleDelete = (data) => {
    proxy
        .$confirm('确认删除菜单吗？', '提示', {
            type: 'warning',
            confirmButtonText: '删除',
            confirmButtonClass: 'el-button--danger'
        })
        .then(async () => {
            // //页面加载
            menuLoading.value = true;
            //调用删除接口
            let res = await category.delete({ ids: data.id });
            menuLoading.value = false;
            if (res.code === 200) {
                //在列表中移除已删除的菜单项
                childrenDataList.value.forEach((item) => {
                    // //移除菜单项
                    if (data.id === item.id) {
                        childrenDataList.value.splice(childrenDataList.value.indexOf(item), 1);
                    }
                });
                await getCategoryList();
                proxy.msgSuccess('删除成功！');
            } else {
                proxy.msgError('删除失败！');
            }
        })
        .catch(() => {
            proxy.msgInfo('已取消删除');
        });
};
/**
 * 弹窗渲染结束
 */
const onDialogOpened = () => {
    nextTick(() => {
        if (categoryEdit.value && typeof categoryEdit.value.loadDetail === 'function') {
            categoryEdit.value.loadDetail();
        }
    });
};

const delMenu = (data) => {
    // 删除逻辑
    proxy
        .$confirm('确认删除此的门类吗？', '提示', {
            type: 'warning',
            confirmButtonText: '删除',
            confirmButtonClass: 'el-button--danger',
            cancelButtonText: '取消',
            cancelButtonClass: 'el-button--primary el-button--large'
        })
        .then(async () => {
            // //页面加载
            menuLoading.value = true;
            //调用删除接口
            let res = await category.delete({ ids: data.id });
            menuLoading.value = false;
            if (res.code === 200) {
                proxy.msgSuccess('删除成功！');
                await getCategoryList();
                details.value = false;
            } else {
                proxy.msgError('删除失败！');
            }
        })
        .catch(() => {
            proxy.msgInfo('已取消删除');
        });
};

const batchDeletion = () => {
    // 批量删除逻辑
    //获取选中的节点
    let CheckedNodes = menu.value.getCheckedNodes();
    if (CheckedNodes.length === 0) {
        proxy.msgWarning('请选择需要删除的项');
        return false;
    }
    // 判断待删除像中是否有公开数据
    let isPublic = CheckedNodes.some((item) => item.openFlag === '1');
    if (!isTianTun() && isPublic) {
        proxy.msgWarning('公开数据不可删除');
        return false;
    }
    // 删除逻辑
    proxy
        .$confirm('确认删除此的门类吗？', '提示', {
            type: 'warning',
            confirmButtonText: '删除',
            confirmButtonClass: 'el-button--danger',
            cancelButtonText: '取消',
            cancelButtonClass: 'el-button--primary el-button--large'
        })
        .then(async () => {
            // //页面加载
            menuLoading.value = true;
            //请求参数处理删除id参数
            let reqData = CheckedNodes.map((item) => item.id).join(',');
            //调用删除接口
            let res = await category.delete({ ids: reqData });
            menuLoading.value = false;
            if (res.code === 200) {
                //在列表中移除已删除的门类项
                CheckedNodes.forEach((item) => {
                    let node = menu.value.getNode(item);
                    //移除门类项
                    menu.value.remove(item);
                    proxy.msgSuccess('删除成功！');
                    getCategoryList();
                    multipleSelectionState.value = false;
                    if (node.isCurrent) {
                        //当前删除的是当前编辑的门类，则清空编辑表单页面
                        details.value = false;
                    }
                });
            } else {
                proxy.msgError('删除失败！');
            }
        })
        .catch(() => {
            proxy.msgInfo('已取消删除');
        });
};
// 判断权限
const determineWhetherItIsEditable = (data) => {
    if (isTianTun()) {
        return true;
    } else {
        // 私有编辑
        if (data?.openFlag === '2') {
            return true;
        }
        return false;
    }
};
const findGroupName = (groupId) => {
    return completeList.value.filter((group) => group.id === groupId).map((item) => item.recordGroupName)[0];
};
const getList = () => {
    completeManagement
        .getList({
            current: 1,
            size: -1
        })
        .then((res) => {
            if (res.code === 200) {
                completeList.value = res.data.records;
            }
        });
};
/**
 * 获取全宗以及门类列表
 * @returns {Promise<void>}
 */
const getCategoryList = async () => {
    menuLoading.value = true;

    try {
        const result = await category.getCategoryTree({
            orgId: getOrganizationInfo()?.id
        });

        if (result.code !== 200) {
            proxy.msgError(result.msg || '获取全宗门类树列表失败');
            return;
        }

        const treeData = result.data;
        if (!treeData?.length) {
            menuList.value = [];
            return;
        }
        treeData.forEach((item) => {
            item.isGroup = true;
            item.disabled = true;
        });
        // 排序处理
        menuList.value = recursiveSort(treeData, 'sort');
    } catch (error) {
        console.error('获取门类数据失败:', error);
        proxy.msgError('系统门类数据查询失败');
        menuList.value = [];
    } finally {
        menuLoading.value = false;
    }
};

onMounted(async () => {
    openFlagOptions.value = await proxy.getDictList("data_permission_open_flag");
    // 初始化逻辑
    await getCategoryList();
    getList();
});
</script>

<style scoped>
.category-management {
    height: 100%;
    background: #f5f7fa;
}

.sidebar-container {
    height: 100%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    /*background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);*/
    color: white;
    border-radius: 8px 8px 0 0;
}

.header-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.unfold-icon {
    transition: transform 0.3s ease;
    margin-right: 2px;
}

.unfold-icon.rotated {
    transform: rotate(180deg);
}

.sidebar-content {
    flex: 1;
    padding: 5px;
    overflow: auto;
}

.category-tree {
    background: transparent;
}

.category-tree :deep(.el-tree-node__content) {
    height: 40px;
    border-radius: 6px;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.category-tree :deep(.el-tree-node__content:hover) {
    background: #f0f9ff;
}

.category-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background: #e6f7ff;
    color: #1890ff;
}

.tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    width: 100%;
}

.node-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.node-icon {
    margin-right: 8px;
    color: #909399;
}

.node-label {
    font-size: 14px;
    margin-right: 8px;
}
.node-tags {
    display: flex;
    gap: 5px;
}

.node-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    margin-right: 5px;
}

.tree-node:hover .node-actions {
    opacity: 1;
}
.sidebar-container .el-button + .el-button {
    margin-left: 0;
}

.main-container {
    height: 100%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.empty-icon {
    font-size: 80px;
    color: #c0c4cc;
}

.detail-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}
.detail-container ::v-deep .el-tabs:first-child {
    margin-left: 10px;
}

.detail-title .el-icon {
    margin-right: 8px;
}

.detail-content {
    flex: 1;
    padding: 20px;
    overflow: auto;
}

.category-dialog :deep(.el-dialog__body) {
    padding: 0;
}

.dialog-footer {
    text-align: right;
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar-header {
        padding: 12px 16px;
    }

    .header-actions {
        flex-direction: column;
    }
}
</style>
