import http from "@/utils/request"
//系统配置门类管理配置
export default {
	list: function (params) {
		return http.get(
			'/archive/config/categoryConfig/treeData',
			params
		)
	},
	save: function (data) {
		return http.post(
			'/archive/config/categoryConfig/save',
			data
		)
	},
	queryById: function (params) {
		return http.get(
			'/archive/config/categoryConfig/queryById',
			params
		)
	},
	delete: function (params) {
		return http.delete(
			'/archive/config/categoryConfig/delete',
			params
		)
	},
	groupList: function (params) {
		return http.get(
			'/archive/config/categoryConfig/treeDataByGroup',
			params
		)
	},
	authorityGroupList: function (params) {
		return http.get(
			'/archive/config/categoryConfig/treeAuthorityDataByGroup',
			params
		)
	},
	getFieldsByCategory: function (params) {
		return http.get(
			'/archive/config/categoryConfig/fieldsById',
			params
		)
	},
	configMenuList: function (params) {
		return http.post(
			'/sys/categoryMenu/configMenu',
			params
		)
	},
	categoryMenuList: function (params) {
		return http.get(
			'/sys/categoryMenu/list',
			params
		)
	},
    // 查询全宗门类树
    getCategoryTree: function (params) {
        return http.get(
                '/archive/config/categoryConfig/getCategoryTree',
                params
        )
    },
    // 查询门类列表
    getCategoryList: function (params) {
        return http.get(
                '/archive/config/categoryConfig/getCategoryList',
                params
        )
    },
    // 门类下拉
    getCategorySelect: function (params) {
        return http.get(
                '/archive/config/categoryConfig/catrgory-tree',
                params
        )
    },
}
