<template>
	<el-container>
        <DragLayout type="row">
            <template #first>
                <el-main v-if="openTree" ref="main" class="p-el h-full">
                    <el-card  class="box-card h-full" body-class="h-full w-full p-0">
                        <categoryTree :is-the-first-level-clickable="true" :default-expand-all="true" @clickNode="clickEven"/>
                    </el-card>
                </el-main>
            </template>
            <template #second>
                <el-container>
                    <el-main v-if="openTree" ref="main" class="p-el h-full">
                        <el-card  class="box-card h-full">
                            <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
                                <el-tab-pane label="上架" name="1">
                                    <div style="display: flex;margin-bottom: 20px;">
                                        <el-form ref="formList" :inline="true" :model="form" label-position="right"
                                                 label-width="auto">
                                            <el-form-item label="档案盒名称：" prop="name" style="margin: 0;padding-right: 10px;">
                                                <el-input v-model="form.name" placeholder="请输入档案盒名称"/>
                                            </el-form-item>
                                            <el-form-item style="margin: 0;padding-left: 10px;">
                                                <el-button :icon="Search" type="primary" @click="() => handleQuery('up')">
                                                    查询
                                                </el-button>
                                                <el-button :icon="RefreshRight" plain @click="() => resetQuery()">
                                                    重置
                                                </el-button>
                                            </el-form-item>
                                        </el-form>
                                    </div>
                                    <el-divider style="margin: 0 0 10px 0"></el-divider>
                                    <div style="margin-bottom: 10px;display: flex;justify-content:flex-end;align-items:center;">
                                        <div>
                                    <span style="margin-right: 15px;" @click="getList">
                                        <el-tooltip class="box-item" content="刷新" effect="light" placement="top">
                                            <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                                <Refresh/>
                                            </el-icon>
                                        </el-tooltip>
                                    </span>
                                            <span @click="screen">
                                        <el-tooltip class="box-item" content="全屏" effect="light" placement="top">
                                            <el-icon :size="20" color="#409EFC"
                                                     style="cursor:pointer;"><el-icon-full-screen/></el-icon>
                                        </el-tooltip>
                                    </span>
                                        </div>
                                    </div>
                                    <el-table :data="extensionList" border @selection-change="handleSelectionChange">
                                        <!-- <el-table-column align="center" min-width="30" type="selection" width="50" /> -->
                                        <el-table-column align="center" label="档案盒名称" prop="boxName"/>
                                        <el-table-column align="center" label="档案盒规格" prop="boxSpecification"/>
                                        <el-table-column align="center" label="年份" prop="boxYear"/>
                                        <el-table-column align="center" label="容量" prop="boxSize"/>
                                        <el-table-column align="center" label="装盒人" prop="boxIntoPerson.name"/>
                                        <el-table-column align="center" label="备注" min-width="120" prop="boxRemark" show-overflow-tooltip/>
                                        <el-table-column align="center" fixed="right" label="操作" min-width="80px">
                                            <template #default="scope">
                                                <el-button icon="Upload" link size="small" type="primary" @click="collectFile(scope.row)">上架</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    <div class="el-page">
                                        <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
                                                    style="padding: 22px" @pagination="paginationing()"/>
                                    </div>
                                </el-tab-pane>

                                <el-tab-pane label="下架" name="2">
                                    <div style="display: flex;margin-bottom: 20px;">
                                        <el-form ref="formList" :inline="true" :model="form" label-position="right"
                                                 label-width="auto">
                                            <el-form-item label="档案盒：" prop="name" style="margin: 0;padding-right: 10px;">
                                                <el-input v-model="form.name" placeholder="请输入档案盒名称"/>
                                            </el-form-item>
                                            <el-form-item style="margin: 0;padding-left: 10px;">
                                                <el-button :icon="Search" type="primary" @click="() => handleQuery('down')">查询
                                                </el-button>
                                                <el-button :icon="RefreshRight" plain @click="() => resetQuery()">重置
                                                </el-button>
                                            </el-form-item>
                                        </el-form>
                                    </div>
                                    <el-divider style="margin: 0 0 10px 0"></el-divider>
                                    <div style="margin-bottom: 10px;display: flex;justify-content:flex-end;align-items:center;">
                                        <div>
                                    <span style="margin-right: 15px;" @click="getList">
                                        <el-tooltip class="box-item" content="刷新" effect="light" placement="top">
                                            <el-icon :size="20" color="#409EFC" style="cursor:pointer;">
                                                <Refresh/>
                                            </el-icon>
                                        </el-tooltip>
                                    </span>
                                            <span @click="screen">
                                        <el-tooltip class="box-item" content="全屏" effect="light" placement="top">
                                            <el-icon :size="20" color="#409EFC"
                                                     style="cursor:pointer;"><el-icon-full-screen/></el-icon>
                                        </el-tooltip>
                                    </span>
                                        </div>
                                    </div>
                                    <el-table :data="destructionLifa" border @selection-change="handleSelectionChange">
                                        <!-- <el-table-column align="center" min-width="30" type="selection" width="50" /> -->
                                        <el-table-column align="center" label="盒名称" prop="boxName"/>
                                        <el-table-column align="center" label="档案盒规格" prop="boxSpecification"/>
                                        <el-table-column align="center" label="年份" prop="boxYear"/>
                                        <el-table-column align="center" label="容量" prop="boxSize"/>
                                        <el-table-column align="center" label="装盒人" prop="boxIntoPerson.name"/>
                                        <el-table-column align="center" label="备注" min-width="120" prop="boxRemark" show-overflow-tooltip/>
                                        <el-table-column align="center" fixed="right" label="操作" min-width="80px">
                                            <template #default="scope">
                                                <el-button icon="Download" link size="small" type="primary" @click="offShelf(scope.row)">下架</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    <div class="el-page">
                                        <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
                                                    style="padding: 22px" @pagination="paginationing()"/>
                                    </div>
                                </el-tab-pane>
                            </el-tabs>

                        </el-card>
                    </el-main>

                    <!-- 查看 -->
                    <el-dialog v-if="openView" v-model="openView" :title="title" append-to-body width="1300px">
                        <storeroom :receiveId="receiveId" style="height: 650px;" @openNewAdd="openNewAdd"></storeroom>
                    </el-dialog>
                </el-container>
            </template>
        </DragLayout>

	</el-container>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, toRefs} from 'vue'
import {Refresh, RefreshRight, Search} from '@element-plus/icons-vue'
import frameList from '@/api/archive/storeroom/upDownFrame';
import tool from '@/utils/tool';
// 查看弹窗
import storeroom from './storeroom.vue';
import {getOrganizationInfo} from "@/utils/permission";
import DragLayout from "@/components/DragLayout/index.vue";

const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	}
})
const activeName = ref('1')
const total = ref(0)
const {queryParams} = toRefs(data)
const {proxy} = getCurrentInstance()
// 接收库List
const extensionList = ref([]) //延期
const destructionLifa = ref([]) //销毁
// 点击查询列表
const openTree = ref(false)

// 点击树结构的id
const clickEvenId = ref([])

function clickEven(val) {
	clickEvenId.value = val;
	clickEvenList(val);
}

//全屏
function screen() {
	var element = document.documentElement;
	tool.screen(element);
}

// 切换tab时掉查询接口
const handleClick = (tabName) => {
	activeName.value = tabName;
	getList(tabName);
}

// 头部查询
const form = ref([])

function handleQuery(boxStatus) {
	frameList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		boxName: form.value.name,
		controlType: boxStatus,
        'org.id': getOrganizationInfo()?.id,
	}).then(res => {
		if (res.code === 200) {
			if (activeName.value == '1') {
				extensionList.value = res.data.records;
			} else if (activeName.value == '2') {
				destructionLifa.value = res.data.records;
			}

			total.value = res.data.total;
			openTree.value = true;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

// 重置
function resetQuery() {
	form.value = [];
	clickEvenList(clickEvenId.value);
}

// 分页
function paginationing() {
	if (clickEvenId.value.length > 0) {
		clickEvenList(clickEvenId.value);
	} else {
		getList(activeName.value)
	}
}

// 点击树结构查询表格
function clickEvenList(val) {
	let boxType = '';
	if (activeName.value == '1') {
		boxType = 'up';
	} else {
		boxType = 'down';
	}
	if (val.dataType === '1') {
		frameList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlGroup.id': val.id,
			controlType: boxType,
		}).then(res => {
			if (res.code === 200) {
				if (activeName.value == '1') {
					extensionList.value = res.data.records;
				} else if (activeName.value == '2') {
					destructionLifa.value = res.data.records;
				}
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	} else {
		frameList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlCategory.id': val.id,
			controlType: boxType,
		}).then(res => {
			if (res.code === 200) {
				if (activeName.value == '1') {
					extensionList.value = res.data.records;
				} else if (activeName.value == '2') {
					destructionLifa.value = res.data.records;
				}
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	}
}

const handList = ref([])

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

// 上架
const receiveId = ref('')
const openView = ref(false)

function collectFile(val) {
	title.value = '查看';
	receiveId.value = val;
	openView.value = true;
}

// 关闭弹窗
function openNewAdd() {
	openView.value = false;
	getList('1');
}

// 下架
function offShelf(val) {
	proxy.$confirm('确定需要下架吗?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		frameList.boxControl({
			storage: {
				id: val.storage.id
			},
			controlType: '2',
			id: val.id
		}).then(res => {
			if (res.code === 200) {
				proxy.msgSuccess('下架成功')
				getList()
			}
		}).catch(() => {
			proxy.msgError('下架失败');
		})
	}).catch(() => {
		console.log(111);
	})
}


// 弹窗标题
const title = ref('')

// 进入时查询全部
function getList(val) {
	let boxType = '';
	if (val == '1') {
		boxType = 'up';
	} else {
		boxType = 'down';
	}
	frameList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		controlType: boxType,
	}).then(res => {
		if (res.code === 200) {
			if (activeName.value == '1') {
				extensionList.value = res.data.records;
			} else if (activeName.value == '2') {
				destructionLifa.value = res.data.records;
			}
			total.value = res.data.total;
			openTree.value = true;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

getList('1')
</script>

<style scoped></style>
