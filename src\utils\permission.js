import tool from '@/utils/tool';

/**
 * 是否含有不限分类，有则表示全部允许通过
 */
export function permissionAll() {
	const allPermissions = "*/*/*"
	let permissions = tool.data.get("PERMISSIONS");
	return permissions.includes(allPermissions);
}

/**
 * 比对两组数据是否一致
 * @param news
 * @param old
 * @returns {boolean}
 */
export function judementSameArr(news, old) {
	// console.log(news)
	// console.log(old)
	let count = 0;
	const leng = news.length;
	for (let i in news) {
		for (let j in old) {
			if (news[i] === old[j]) {
				count++;
				// console.log(news[i])
			}
		}
	}
	// console.log('相同的数量', count)
	return count === leng;
}

export function permission(data) {
	let permissions = tool.data.get("PERMISSIONS");
	if(!permissions){
		return false;
	}
	let isHave = permissions.includes(data);
	return isHave;
}

export function rolePermission(data) {
	let userInfo = tool.data.get("USER_INFO");
	if(!userInfo){
		return false;
	}
	let role = userInfo.role;
	if(!role){
		return false;
	}
	let isHave = role.includes(data);
	return isHave;
}

/**
 * 获取组织机构信息
 * 从缓存中获取登录信息USER_INFO，并取出sysOrg中的id和name
 * @returns {Object|null} 返回组织机构信息 {id: string, name: string} 或 null
 */
export function getOrganizationInfo() {
	try {
        const orgKey = tool.data.get("orgKey");
        const Organization = tool.data.get("Organization");
		if (!Organization || !Organization.length) {
			return null;
		}

		return {
			id: Organization[orgKey].id || '',
			name: Organization[orgKey].name || ''
		};
	} catch (error) {
		console.error('获取组织机构信息失败:', error);
		return null;
	}
}
/**
 * 判断是否是超级管理员
 * 从缓存中取出ROLE_LIST数据，取出ROLE_LIST中每一个角色的roleType，并判断此数据中是否包含superadmin
 * @returns {boolean} 是否是超级管理员，true为是，false为否
 */
export function isSuperAdmin() {
	try {
		// 获取角色列表
		const roleList = tool.data.get("ROLE_LIST");
		if (!roleList || !Array.isArray(roleList)) {
			return false;
		}

		// 检查是否包含superadmin角色
		const hasSuperAdmin = roleList.some(role => {
			return role.roleType && role.roleType.includes('superadmin');
		});

		return hasSuperAdmin; // 返回是否包含superadmin
	} catch (error) {
		console.error('判断是否是超级管理员失败:', error);
		return false; // 出
    }
}
/**
 * 判断是否是天囤下的账号
 * 从缓存中取出USER_INFO数据，判断sysOrg.id 是否为753188220495929344
 * @returns {boolean} true为是，false为否
 */
export function isTianTun() {
    try {
        const userOrg = getOrganizationInfo();
        if (!userOrg || !userOrg.id) {
            return false;
        }

        return userOrg.id === getTiantunId();
    } catch (error) {
        console.error('判断是否是天囤下的账号失败:', error);
        return false;
    }
}
export function getTiantunId(){
    return '753188220495929344';
}

/**
 * 判断是否有系统数据权限
 * 从缓存中取出ROLE_LIST数据，取出ROLE_LIST中每一个角色的roleType，并判断此数据中是否包含sys为true
 * @returns {boolean} 是否是超级管理员，true为是，false为否
 */
export function isSystemDataPermission() {
    try {
        // 获取角色列表
        const roleList = tool.data.get("ROLE_LIST");
        if (!roleList || !Array.isArray(roleList)) {
            return false;
        }

        // 检查是否包含sys角色
        const hasSystemData = roleList.some(role => {
            return role.sys;
        });

        return hasSystemData; // sys为true
    } catch (error) {
        console.error('判断是否是超级管理员失败:', error);
        return false; // 出
    }
}
