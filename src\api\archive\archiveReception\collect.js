import http from "@/utils/request"

export default {
	//  收集库-档案列表
	list: function (params) {
		return http.get(
			'/archive/info/main/collect-list',
			params
		)
	},
    //  收集库-批量设置档案资质信息
    batchConfig: function (data) {
        return http.post(
                '/archive/info/main/batchConfig',
                data
        )
    },
	//查询历史版本信息
	getHistoryByRecordId: function (params) {
		return http.get(
			'/archive/info/main/getHistoryByRecordId',
			params
		)
	},
	//关联版本信息
	versionAssociation: function (params) {
		return http.get(
			'/archive/info/main/versionAssociation',
			params
		)
	},
	// 删除档案信息
	delete: function (params) {
		return http.delete(
			'/archive/info/main/delete',
			params
		)
	},
	// 保存或更新档案信息
	save: function (data) {
		return http.post(
			'/archive/info/main/save',
			data
		)
	},
	// 档案批量导出
	fileBatchExport: function (params) {
		return http.get(
			'/archive/info/main/fileBatchExport',
			params
		)
	},
	// 彻底删除档案
	thoroughDelete: function (params) {
		return http.delete(
			'/archive/info/main/thoroughDelete',
			params
		)
	},
	// 档案信息文件上传
	infoFileUpload: function (data) {
		return http.post(
            '/archive/info/main/infoFileUpload',
            data
        )
    },
    // 档案归档和移动以及恢复
    infoOperate: function (data) {
        return http.post(
            '/archive/info/main/infoOperate',
            data
        )
    },
    // 获取新增数据结构
    getDataConfig: function (data) {
        return http.get(
            '/archive/info/main/getDataConfig',
            data
        )
    },
    // 新增档案信息
    saveInfoData: function (data) {
        return http.post(
            '/archive/info/main/saveInfoData',
			data
		)
	},
	// 编辑档案基本信息
	updateJsonData: function (data) {
		return http.post(
			'/archive/info/data/updateJsonData',
			data
		)
	},
	//删除档案数据
	deleteDataByIds: function (params) {
		return http.delete(
			'/archive/info/data/delete',
			params
		)
	},
	// 查询档案文件信息表列表数据
	fileFist: function (params) {
		return http.get(
			'/archive/info/file/list',
			params
		)
	},
	//删除档案文件/archive/info/file/delete
	fileDelete: function (params) {
		return http.delete(
            '/archive/info/file/delete',
            params
        )
    },
    // 查询档案文件信息表列表数据
    getArchiveList: function (params) {
        return http.get(
                '/archive/info/main/archive-list',
                params
        )
    },
}
