<template>
	<el-dialog v-if="props.openAssociation" v-model="props.openAssociation" :close-on-click-modal="false" :close-on-press-escape="false"
			   :title="props.title" append-to-body top="12vh" width="80%" @close="closeDialog">
		<el-container style="height: 62vh">
			<el-aside width="22%">
                <categoryTree :is-the-first-level-clickable="true"  @clickNode="clickEven"/>
			</el-aside>
			<el-main>
				<el-table :data="receiveData" border @selection-change="handleSelectionChange">
					<el-table-column align="center" type="selection" width="50"/>
					<el-table-column align="left" label="档案名称" prop="name"/>
					<el-table-column align="center" label="档案号" prop="num">
						<template #default="scope">
							{{ scope.row.num || "未填写数据" }}
						</template>
					</el-table-column>
					<el-table-column align="center" label="档案年份" prop="year"/>
					<el-table-column align="center" label="档案月份" prop="month">
						<template #default="scope">
							{{ scope.row.month + "月" || "未填写数据" }}
						</template>
					</el-table-column>
					<el-table-column align="center" label="档案创建时间" prop="createDate">
						<template #default="scope">
							{{ moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
						</template>
					</el-table-column>
					<el-table-column align="center" class-name="small-padding fixed-width" fixed="right"
									 label="操作" width="80">
						<template #default="scope">
							<el-button icon="View" link size="small" type="primary" @click="collectFile(scope.row)">
								查看
							</el-button>
						</template>
					</el-table-column>
				</el-table>
				<div style="float: right;">
					<Pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
								style="padding: 22px" @pagination="pagination()"/>
				</div>
			</el-main>
		</el-container>

		<!-- 查看 -->
		<el-dialog v-model="openView" append-to-body title="查看" top="10vh" width="90%">
			<viewFiles :receiveId="receiveId" @childMove="parentView"/>
		</el-dialog>

		<template #footer>
			<el-button type="primary" @click="() => collect()">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import {defineEmits, defineProps, getCurrentInstance, reactive, ref, toRefs} from 'vue'
import collectList from '@/api/archive/archiveReception/collect';
import receiveList from "@/api/archive/archiveReception/receive"
import viewFiles from '../view.vue';
import moment from "moment";

const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	}
})
const total = ref(0)
const {queryParams} = toRefs(data)
const {proxy} = getCurrentInstance()
// 接收库List
const receiveData = ref([])
// 点击查询列表
const openTree = ref(false)

const props = defineProps({
	relevance: {
		type: Object,
		default: {}
	},
	openAssociation: {
		type: Boolean,
		default: false
	},
	title: {
		type: String,
		default: ''
	},
	dataType: {
		type: String,
		default: ''
	}
})

// 点击树结构的id
const clickEvenId = ref([])

const handList = ref([])

const emit = defineEmits(["childRelevance"]);

const openView = ref(false)

const receiveId = ref('')

function clickEven(val) {
	clickEvenId.value = val;
	clickEvenList(val);
}

function collectFile(val) {
	receiveId.value = val;
	openView.value = true;
}

//关闭窗口
function closeDialog() {
	emit("childRelevance");
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 点击树结构查询表格
function clickEvenList(val) {
	if (val.dataType === '1') {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlGroup.id': val.id,
			archiveStatus: 1,
			delFlag: 0
		}).then(res => {
			if (res.code === 200) {
				receiveData.value = res.data.records;
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	} else {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlCategory.id': val.id,
			archiveStatus: 1,
			delFlag: 0
		}).then(res => {
			if (res.code === 200) {
				receiveData.value = res.data.records;
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	}
}

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

function pagination() {
	clickEvenList(clickEvenId.value);
}

// 确定
function collect() {
	if (handList.value.length > 0) {
		if (props.dataType === 'version') {
			//拼接的数组字符串，接口传参
			collectList.versionAssociation({
				idStr: handList.value.map((v) => v.id).join(",") + "," + props.relevance.id
			}).then(result => {
				if (result.code === 200) {
					proxy.msgSuccess('关联成功');
					emit("childRelevance");
				} else {
					proxy.msgError(result.msg);
				}
			}).catch(error => {
				proxy.msgError('关联失败');
			});
		} else if (props.dataType === 'info') {
			//拼接的数组字符串，接口传参
			let ids = handList.value.map((v) => v.id).join(",");

			// 关联档案
			receiveList.save({
				fileOriginalId: props.relevance.id,
				fileRelevanceIds: ids,
			}).then(res => {
				if (res.code === 200) {
					emit("childRelevance");
					proxy.msgSuccess('关联成功');
				} else {
					proxy.msgError(res.msg);
				}
			}).catch(() => {
				proxy.msgError('关联失败');
			});
		}
	} else {
		proxy.msgError('请至少选择一个关联档案');
	}
}
</script>

<style scoped></style>
