<template>
	<el-container>
		<el-aside style="border-right: 0;background-color: #fff" width="21%" class="p-el">
			<el-card class="h-full w-full" body-class="h-full w-full p-0">
                <categoryTree :is-the-first-level-clickable="true"  @clickNode="clickEven"/>
			</el-card>
		</el-aside>
		<el-container>
			<el-main class="p-el">
				<el-container>
					<el-main v-if="openTree" ref="main" class="p-0" style="">
						<el-card body-style="padding-top: 0;padding-bottom: 0;" class="box-card">
							<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
								<el-tab-pane label="延期" name="1">
									<div style="display: flex;margin-bottom: 10px;">
										<el-form ref="formList" :inline="true" :model="form" label-position="right"
											label-width="auto">
											<el-form-item label="档案名称" prop="name"
												style="margin: 0;padding-right: 10px;">
												<el-input v-model="form.name" placeholder="请输入档案名称" @keyup.enter="handleEnterKey" />
											</el-form-item>
											<el-form-item label="档案号" prop="numFormat" style="margin: 0;padding-right: 10px;">
												<el-input v-model="form.numFormat" placeholder="请输入档案号" @keyup.enter="handleEnterKey" />
											</el-form-item>
											<el-form-item style="margin: 0;padding-left: 10px;">
												<el-button :icon="Search" type="primary" @click="() => handleQuery()" :disabled="loading">查询
												</el-button>
												<el-button :icon="RefreshRight" plain @click="() => resetQuery()" :disabled="loading">重置
												</el-button>
											</el-form-item>
										</el-form>
									</div>
									<el-divider style="margin: 10px 0" />
									<div
										style="margin-bottom: 10px;display: flex;justify-content:space-between;align-items:center;">
										<div>
											<el-button icon="Finished" plain type="primary"
												@click="() => collectExtension()" :disabled="loading">批量鉴定</el-button>
										</div>
										<div>
											<span style="margin-right: 15px;" @click="handleRefresh">
												<el-tooltip class="box-item" content="刷新" effect="light"
													placement="top">
													<el-icon :size="20" :color="loading ? '#C0C4CC' : '#409EFC'" style="cursor:pointer;">
														<Refresh />
													</el-icon>
												</el-tooltip>
											</span>
											<span @click="screen">
												<el-tooltip class="box-item" content="全屏" effect="light"
													placement="top">
													<el-icon :size="20" color="#409EFC"
														style="cursor:pointer;"><el-icon-full-screen /></el-icon>
												</el-tooltip>
											</span>
										</div>
									</div>
									<el-table :data="extensionList" border @selection-change="handleSelectionChange" v-loading="loading">
										<el-table-column align="center" min-width="30" type="selection" width="50" />
										<el-table-column align="left" label="档案名称" prop="name" min-width="200" />
										<el-table-column align="center" label="档案号" prop="num" min-width="200" />
										<el-table-column align="center" label="保留年限" prop="retentionPeriod" width="80">
											<template #default="scope">
												{{ reserve(scope.row.retentionPeriod) }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="控制等级" prop="controlStatus" width="100">
											<template #default="scope">
												{{ control(scope.row.controlStatus) }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="所属部门" prop="office.name" min-width="100">
											<template #default="scope">
												{{ scope.row.office ? scope.row.office.name : '暂无' }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="归档时间" prop="archiveTime" width="180">
											<template #default="scope">
												{{
													(scope.row.archiveTime ?
														moment(scope.row.archiveTime).format('YYYY-MM-DD HH:mm:ss') : undefined)
												|| '暂无'
												}}
											</template>
										</el-table-column>
										<el-table-column align="center" label="到期时间" prop="expirationData" width="180">
											<template #default="scope">
												{{
													(scope.row.expirationData ?
														moment(scope.row.expirationData).format('YYYY-MM-DD HH:mm:ss') :
														undefined) || '暂无'
												}}
											</template>
										</el-table-column>
										<el-table-column align="center" class-name="small-padding fixed-width"
											label="操作" width="150px" fixed="right">
											<template #default="scope">
												<el-button icon="View" link type="primary"
													@click="collectFile(scope.row)">查看</el-button>
											</template>
										</el-table-column>
									</el-table>
									<div style="float: right;">
										<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
											:total="total" style="padding: 22px"
											@pagination="paginationing()" />
									</div>
								</el-tab-pane>
								<el-tab-pane label="销毁" name="2">
									<div style="display: flex;margin-bottom: 10px;">
										<el-form ref="formList" :inline="true" :model="form" label-position="right"
											label-width="70px">
											<el-form-item label="档案名称" prop="name"
												style="margin: 0;padding-right: 10px;">
												<el-input v-model="form.name" placeholder="请输入档案名称" @keyup.enter="handleEnterKey" />
											</el-form-item>
											<el-form-item label="档案号" prop="numFormat" style="margin: 0;padding-right: 10px;">
												<el-input v-model="form.numFormat" placeholder="请输入档案号" @keyup.enter="handleEnterKey" />
											</el-form-item>
											<el-form-item style="margin: 0;padding-left: 10px;">
												<el-button :icon="Search" type="primary" @click="() => handleQuery()" :disabled="loading">查询
												</el-button>
												<el-button :icon="RefreshRight" plain @click="() => resetQuery()" :disabled="loading">重置
												</el-button>
											</el-form-item>
										</el-form>
									</div>
									<el-divider style="margin: 10px 0" />
									<div
										style="margin-bottom: 10px;display: flex;justify-content:space-between;align-items:center;">
										<div>
											<el-button icon="Finished" plain type="primary"
												@click="() => collectDestruction()" :disabled="loading">批量鉴定
											</el-button>
										</div>
										<div>
											<span style="margin-right: 15px;" @click="handleRefresh">
												<el-tooltip class="box-item" content="刷新" effect="light"
													placement="top">
													<el-icon :size="20" :color="loading ? '#C0C4CC' : '#409EFC'" style="cursor:pointer;">
														<Refresh />
													</el-icon>
												</el-tooltip>
											</span>
											<span @click="screen">
												<el-tooltip class="box-item" content="全屏" effect="light"
													placement="top">
													<el-icon :size="20" color="#409EFC"
														style="cursor:pointer;"><el-icon-full-screen /></el-icon>
												</el-tooltip>
											</span>
										</div>
									</div>
									<el-table :data="destructionLifa" border @selection-change="handleSelectionChange" v-loading="loading">
										<el-table-column align="center" min-width="30" type="selection" width="50" />
										<el-table-column align="left" label="档案名称" prop="name" min-width="200" />
										<el-table-column align="center" label="档案号" prop="num" min-width="200" />
										<el-table-column align="center" label="保留年限" prop="retentionPeriod" width="80">
											<template #default="scope">
												{{ reserve(scope.row.retentionPeriod) }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="控制等级" prop="controlStatus" min-width="100">
											<template #default="scope">
												{{ control(scope.row.controlStatus) }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="所属部门" prop="office.name" min-width="100">
											<template #default="scope">
												{{ scope.row.office ? scope.row.office.name : '暂无' }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="归档时间" prop="archiveTime" width="180">
											<template #default="scope">
												{{
													(scope.row.archiveTime ?
														moment(scope.row.archiveTime).format('YYYY-MM-DD HH:mm:ss') : undefined)
												|| '暂无'
												}}
											</template>
										</el-table-column>
										<el-table-column align="center" label="到期时间" prop="expirationData" width="180">
											<template #default="scope">
												{{
													(scope.row.expirationData ?
														moment(scope.row.expirationData).format('YYYY-MM-DD HH:mm:ss') :
														undefined) || '暂无'
												}}
											</template>
										</el-table-column>
										<el-table-column align="center" class-name="small-padding fixed-width"
											label="操作" width="150px" fixed="right">
											<template #default="scope">
												<el-button icon="View" link type="primary"
													@click="collectFile(scope.row)">查看</el-button>
											</template>
										</el-table-column>
									</el-table>
									<div style="float: right;">
										<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
											:total="total" style="padding: 22px"
											@pagination="paginationing()" />
									</div>
								</el-tab-pane>
								<el-tab-pane label="开放" name="3">
									<div style="display: flex;margin-bottom: 10px;">
										<el-form ref="formList" :inline="true" :model="form" label-position="right"
											label-width="70px">
											<el-form-item label="档案名称" prop="name"
												style="margin: 0;padding-right: 10px;">
												<el-input v-model="form.name" placeholder="请输入档案名称" @keyup.enter="handleEnterKey" />
											</el-form-item>
											<el-form-item label="档案号" prop="numFormat" style="margin: 0;padding-right: 10px;">
												<el-input v-model="form.numFormat" placeholder="请输入档案号" @keyup.enter="handleEnterKey" />
											</el-form-item>
											<el-form-item style="margin: 0;padding-left: 10px;">
												<el-button :icon="Search" type="primary" @click="() => handleQuery()" :disabled="loading">查询
												</el-button>
												<el-button :icon="RefreshRight" plain @click="() => resetQuery()" :disabled="loading">重置
												</el-button>
											</el-form-item>
										</el-form>
									</div>
									<el-divider style="margin: 10px 0" />
									<div
										style="margin-bottom: 10px;display: flex;justify-content:space-between;align-items:center;">
										<div>
											<el-button icon="Finished" plain type="primary"
												@click="() => collectOpen()" :disabled="loading">批量鉴定
											</el-button>
										</div>
										<div>
											<span style="margin-right: 15px;" @click="handleRefresh">
												<el-tooltip class="box-item" content="刷新" effect="light"
													placement="top">
													<el-icon :size="20" :color="loading ? '#C0C4CC' : '#409EFC'" style="cursor:pointer;">
														<Refresh />
													</el-icon>
												</el-tooltip>
											</span>
											<span @click="screen">
												<el-tooltip class="box-item" content="全屏" effect="light"
													placement="top">
													<el-icon :size="20" color="#409EFC"
														style="cursor:pointer;"><el-icon-full-screen /></el-icon>
												</el-tooltip>
											</span>
										</div>
									</div>
									<el-table :data="opennessList" border @selection-change="handleSelectionChange" v-loading="loading">
										<el-table-column align="center" min-width="30" type="selection" width="50" />
										<el-table-column align="left" label="档案名称" prop="name" min-width="200" />
										<el-table-column align="center" label="档案号" prop="num" min-width="200" />
										<el-table-column align="center" label="保留年限" prop="retentionPeriod" width="80">
											<template #default="scope">
												{{ reserve(scope.row.retentionPeriod) }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="控制等级" prop="controlStatus" min-width="100">
											<template #default="scope">
												{{ control(scope.row.controlStatus) }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="所属部门" prop="office.name" min-width="100">
											<template #default="scope">
												{{ scope.row.office ? scope.row.office.name : '暂无' }}
											</template>
										</el-table-column>
										<el-table-column align="center" label="归档时间" prop="archiveTime" width="180">
											<template #default="scope">
												{{
													(scope.row.archiveTime ?
														moment(scope.row.archiveTime).format('YYYY-MM-DD HH:mm:ss') : undefined)
												|| '暂无'
												}}
											</template>
										</el-table-column>
										<el-table-column align="center" label="到期时间" prop="expirationData" width="180">
											<template #default="scope">
												{{
													(scope.row.expirationData ?
														moment(scope.row.expirationData).format('YYYY-MM-DD HH:mm:ss') :
														undefined) || '暂无'
												}}
											</template>
										</el-table-column>
										<el-table-column align="center" class-name="small-padding fixed-width"
											label="操作" width="150px" fixed="right">
											<template #default="scope">
												<el-button icon="View" link type="primary"
													@click="collectFile(scope.row)">查看</el-button>
											</template>
										</el-table-column>
									</el-table>
									<div style="float: right;">
										<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
											:total="total" style="padding: 22px"
											@pagination="paginationing()" />
									</div>
								</el-tab-pane>
							</el-tabs>

						</el-card>
					</el-main>
					<!-- 开放鉴定 -->
					<el-dialog v-if="open" v-model="open" :title="title" append-to-body width="1000px">
						<openness :clickEvenId="clickEvenId" :handList="handList" @childMove="parentMove"></openness>
					</el-dialog>
					<!-- 销毁鉴定 -->
					<el-dialog v-if="openDestruction" v-model="openDestruction" :title="title" append-to-body
						width="1000px">
						<destruction :clickEvenId="clickEvenId" :handList="handList" @childMove="parentDestruction">
						</destruction>
					</el-dialog>
					<!-- 鉴定延期 -->
					<el-dialog v-if="openExtension" v-model="openExtension" :title="title" append-to-body
						width="1000px">
						<extension :clickEvenId="clickEvenId" :handList="handList" @childMove="parentExtension">
						</extension>
					</el-dialog>
					<!-- 查看 -->
					<el-dialog v-if="openView" v-model="openView" :title="title" append-to-body top="10vh" width="90%">
						<viewFiles :receiveId="receiveId" @childMove="parentView"></viewFiles>
					</el-dialog>
				</el-container>
			</el-main>
		</el-container>
	</el-container>
</template>

<script setup>
import { getCurrentInstance, reactive, ref, toRefs } from 'vue'
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue'
import collectList from '@/api/archive/archiveReception/collect';
import moment from 'moment'
import tool from '@/utils/tool';
// 开放鉴定
import openness from './openness.vue';
// 销毁鉴定
import destruction from './destruction.vue';
// 鉴定延期
import extension from './extension.vue';
// 查看弹窗
import viewFiles from '../view.vue';

const data = reactive({
	queryParams: {
		current: 1,
		size: 10,
	}
})
const activeName = ref('1')
const total = ref(0)
const { queryParams } = toRefs(data)
const { proxy } = getCurrentInstance()
// 接收库List
const extensionList = ref([]) //延期
const destructionLifa = ref([]) //销毁
const opennessList = ref([]) //开放
// 点击查询列表
const openTree = ref(false)
const loading = ref(false) // 添加loading状态变量

// 点击树结构的id
const clickEvenId = ref(null)

function clickEven(val) {
	clickEvenId.value = val;
	clickEvenList(val);
}

//全屏
function screen() {
	var element = document.documentElement;
	tool.screen(element);
}

// 查看receiveId
const receiveId = ref('')
const openView = ref(false)

function collectFile(val) {
	title.value = '查看';
	receiveId.value = val;
	openView.value = true;
}

// 关闭查看
function parentView() {
	openView.value = false;
}

// 切换tab时掉查询接口
const handleClick = (tabName) => {
	activeName.value = tabName;
	getList();
}

// 添加处理回车事件的函数
function handleEnterKey() {
	if (!loading.value) {
		handleQuery();
	}
}

// 头部查询
const form = ref({
	name: '',
	numFormat: ''
})

function handleQuery() {
	loading.value = true;
	collectList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		name: form.value.name,
		numFormat: form.value.numFormat,
		'controlCategory.id': clickEvenId.value?.id || undefined,
		archiveStatus: 1,
		delFlag: 0,
		infoType: activeName.value
	}).then(res => {
		if (res.code === 200) {
			if (activeName.value == '1') {
				extensionList.value = res.data.records;
			} else if (activeName.value == '2') {
				destructionLifa.value = res.data.records;
			} else if (activeName.value == '3') {
				opennessList.value = res.data.records;
			}

			total.value = res.data.total;
			openTree.value = true;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	}).finally(() => {
		loading.value = false;
	})
}

// 重置
function resetQuery() {
	form.value = {
		name: '',
		numFormat: ''
	};
	// 如果有选中的树节点，重新查询该节点的数据；否则查询全部数据
	if (clickEvenId.value) {
		clickEvenList(clickEvenId.value);
	} else {
		getList();
	}
}

// 分页
function paginationing() {
	if (clickEvenId.value) {
		clickEvenList(clickEvenId.value);
	} else {
		getList()
	}
}

// 点击树结构查询表格
function clickEvenList(val) {
	// 防御性检查：确保 val 存在
	if (!val) {
		loading.value = false;
		proxy.msgError('参数错误');
		return;
	}

	loading.value = true;
	if (val.dataType === '1') {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlGroup.id': val.id,
			archiveStatus: 1,
			delFlag: 0,
			infoType: activeName.value
		}).then(res => {
			if (res.code === 200) {
				if (activeName.value == '1') {
					extensionList.value = res.data.records;
				} else if (activeName.value == '2') {
					destructionLifa.value = res.data.records;
				} else if (activeName.value == '3') {
					opennessList.value = res.data.records;
				}
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		}).finally(() => {
			loading.value = false;
		})
	} else {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			'controlCategory.id': val.id || undefined,
			archiveStatus: 1,
			delFlag: 0,
			infoType: activeName.value
		}).then(res => {
			if (res.code === 200) {
				if (activeName.value == '1') {
					extensionList.value = res.data.records;
				} else if (activeName.value == '2') {
					destructionLifa.value = res.data.records;
				} else if (activeName.value == '3') {
					opennessList.value = res.data.records;
				}
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		}).finally(() => {
			loading.value = false;
		})
	}
}

const handList = ref([])

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

// 保留年限
function reserve(val) {
	if (val == 'Y') {
		return '永久'
	} else if (val == 'D5') {
		return '5年'
	} else if (val == 'D10') {
		return '10年 '
	} else if (val == 'D20') {
		return '20年'
	} else if (val == 'D30') {
		return '30年'
	} else {
		return '暂无'
	}
}

// 控制等级
function control(val) {
	if (val === '1') {
		return '公开'
	} else if (val === '2') {
		return '公司内部开放'
	} else if (val === '3') {
		return '部门内部开放 '
	} else if (val === '4') {
		return '控制'
	} else {
		return '暂无'
	}
}

// 取消开放鉴定
function parentMove() {
	open.value = false;
	getList();
}

// 弹窗标题
const title = ref('')

// 开放鉴定
const open = ref(false)

function collectOpen() {
	if (handList.value.length > 0) {
		open.value = true;
		title.value = '开放鉴定';
	} else {
		proxy.msgError('请先选择需要开放鉴定的数据！');
	}

}

// 销毁鉴定
const openDestruction = ref(false)

function collectDestruction() {
	if (handList.value.length > 0) {
		openDestruction.value = true;
		title.value = '鉴定销毁';
	} else {
		proxy.msgError('请先选择需要鉴定销毁的数据！');
	}

}

// 取消销毁鉴定
function parentDestruction() {
	openDestruction.value = false;
	getList();
}

// 鉴定延期
const openExtension = ref(false)

function collectExtension() {
	if (handList.value.length > 0) {
		openExtension.value = true;
		title.value = '鉴定延期';
	} else {
		proxy.msgError('请先选择需要鉴定延期的数据！');
	}

}

// 鉴定延期
function parentExtension() {
	openExtension.value = false;
	getList();
}


// 进入时查询全部
function getList() {
	loading.value = true;
	collectList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		'controlCategory.id': clickEvenId.value?.id || undefined,
		archiveStatus: 1,
		delFlag: 0,
		infoType: activeName.value,
	}).then(res => {
		if (res.code === 200) {
			if (activeName.value == '1') {
				extensionList.value = res.data.records;
			} else if (activeName.value == '2') {
				destructionLifa.value = res.data.records;
			} else if (activeName.value == '3') {
				opennessList.value = res.data.records;
			}
			total.value = res.data.total;
			openTree.value = true;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	}).finally(() => {
		loading.value = false;
	})
}

// 处理刷新按钮点击事件
function handleRefresh() {
	if (!loading.value) {
		getList();
	}
}

getList()
</script>

<style scoped></style>
