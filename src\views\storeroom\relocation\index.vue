<template>
    <el-container>
        <DragLayout type="row">
            <template #first>
                <el-aside class="w-full h-full p-el">
                    <el-card body-class="h-full w-full p-0" class="h-full w-full">
                        <categoryTree :is-the-first-level-clickable="true" @clickNode="clickEven" />
                    </el-card>
                </el-aside>
            </template>
            <template #second>
                <el-main v-if="openTree" ref="main" class="p-el h-full el-flex-column" >
                    <el-card  style="height: auto;">
                        <div style="display: flex">
                            <el-form ref="formList" :inline="true" :model="form" label-position="right" label-width="auto">
                                <el-form-item label="档案盒名称" prop="name" style="margin: 0; padding-right: 10px">
                                    <el-input v-model="form.name" placeholder="请输入档案名称" />
                                </el-form-item>
                                <el-form-item style="margin: 0; padding-left: 10px">
                                    <el-button :icon="Search" type="primary" @click="() => handleQuery()">查询 </el-button>
                                    <el-button :icon="RefreshRight" plain @click="() => resetQuery()">重置 </el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                    </el-card>
                    <el-card class="w-full h-full" body-class="h-full el-flex-column">
                        <div style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;flex-wrap: nowrap;">
                            <div>
                                <el-button icon="Switch" plain type="warning" @click="() => collectBoxing()">移库</el-button>
                            </div>
                            <div>
                                <span style="margin-right: 15px" @click="getList">
                                    <el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC" style="cursor: pointer">
                                            <Refresh />
                                        </el-icon>
                                    </el-tooltip>
                                </span>
                                <span @click="screen">
                                    <el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
                                        <el-icon :size="20" color="#409EFC" style="cursor: pointer"><el-icon-full-screen /></el-icon>
                                    </el-tooltip>
                                </span>
                            </div>
                        </div>

                        <el-table class="w-full h-full" :data="receiveData" border @selection-change="handleSelectionChange">
                            <el-table-column align="center" min-width="30" type="selection" width="50" />
                            <el-table-column align="center" label="档案盒名称" prop="boxName" />
                            <el-table-column align="center" label="档案盒规格" prop="boxSpecification" />
                            <el-table-column align="center" label="年份" prop="boxYear" />
                            <el-table-column align="center" label="容量" prop="boxSize" />
                            <el-table-column align="center" label="装盒人" prop="boxIntoPerson.name" />
                            <el-table-column align="center" label="备注" prop="boxRemark" show-overflow-tooltip />
                            <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="150px">
                                <template #default="scope">
                                    <el-button icon="View" link type="primary" @click="collectFile(scope.row)">查看 </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="el-page">
                            <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="paginationing()" />
                        </div>
                    </el-card>
                </el-main>
            </template>
        </DragLayout>

        <!-- 查看 -->
        <el-dialog v-if="openBoxing" v-model="openBoxing" :title="title" append-to-body width="1300px">
            <Boxing :clickEvenId="clickEvenId" :handList="handList" @childMove="parentMove"></Boxing>
        </el-dialog>
        <!-- 移库 -->
        <el-dialog v-if="openView" v-model="openView" :title="title" append-to-body width="1300px">
            <storeroom :receiveId="receiveId" style="height: 650px" @openNewAdd="openNewAdd"></storeroom>
        </el-dialog>
    </el-container>
</template>

<script setup>
import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import boxList from '@/api/archive/storeroom/filingBox';
import tool from '@/utils/tool';
// 查看弹窗
import Boxing from './boxing.vue';
// 移库弹窗
import storeroom from './storeroom.vue';
import { getOrganizationInfo } from '@/utils/permission';
import DragLayout from '@/components/DragLayout/index.vue';
const data = reactive({
    queryParams: {
        current: 1,
        size: 10
    }
});
const total = ref(0);
const { queryParams } = toRefs(data);
const { proxy } = getCurrentInstance();
// 接收库List
const receiveData = ref([]);
// 点击查询列表
const openTree = ref(false);

// 点击树结构的id
const clickEvenId = ref([]);
function clickEven(val) {
    clickEvenId.value = val;
    clickEvenList(val);
}

//全屏
function screen() {
    var element = document.documentElement;
    tool.screen(element);
}

// 头部查询
const form = ref([]);
function handleQuery() {
    getList();
}
// 重置
function resetQuery() {
    form.value = [];
    clickEvenList(clickEvenId.value);
}

// 分页
function paginationing() {
    if (clickEvenId.value.length > 0) {
        clickEvenList(clickEvenId.value);
    } else {
        getList();
    }
}
// 点击树结构查询表格
function clickEvenList(val) {
    form.value['controlGroup.id'] = undefined;
    form.value['controlCategory.id'] = undefined;
    if (val.dataType === '1') {
        form.value['controlGroup.id'] = val.id;
    } else {
        form.value['controlCategory.id'] = val.id;
    }
    getList();
}
const handList = ref([]);
// 选中表格
function handleSelectionChange(val) {
    handList.value = val;
}

// 弹窗 标题
const title = ref('');
// 是否弹窗
const openBoxing = ref(false);
// 查看
function collectFile(val) {
    title.value = '查看';
    handList.value = val;
    openBoxing.value = true;
}

const openView = ref(false);
const receiveId = ref('');
// 移库
function collectBoxing() {
    if (handList.value.length > 0) {
        title.value = '移库';
        receiveId.value = handList.value;
        openView.value = true;
    } else if (handList.value.length === 0) {
        proxy.msgError('请先选择需要移库的数据！');
    }
}
// 关闭弹窗
function openNewAdd() {
    openView.value = false;
    getList();
}
// 移库标题
function parentMove(val, type) {
    if (type == '1') {
        getList();
        openBoxing.value = false;
        handList.value = [];
    }
}

// 进入时查询全部
function getList() {
    boxList.list({
            current: queryParams.value.current,
            size: queryParams.value.size,
            ...form.value,
            boxName: form.value.name,
            'org.id': getOrganizationInfo()?.id
        })
        .then((res) => {
            if (res.code === 200) {
                receiveData.value = res.data.records;
                total.value = res.data.total;
                openTree.value = true;
            }
        })
        .catch(() => {
            proxy.msgError('查询失败');
        });
}

getList();
</script>

<style scoped></style>
