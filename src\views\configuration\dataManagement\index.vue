<template>
	<el-container v-loading="loading">
		<el-main class="bodyBox">
			<el-card style="height: 9%;width: 100%">
				<el-form ref="searchForm" :model="data.searchForm" inline label-width="auto">
					<el-form-item label="数据名称" prop="dataName" style="margin-bottom: 0">
						<el-input v-model="data.searchForm.dataName" clearable placeholder="请输入需要查询的数据名称"
								  style="width: 100%;"/>
					</el-form-item>
					<el-form-item label="数据类型" prop="dataType" style="margin-bottom: 0">
						<el-select v-model="data.searchForm.dataType" clearable filterable placeholder="请选择数据类型" style="width: 12vw">
							<el-option label="系统数据" value="1"/>
							<el-option label="用户自定义" value="2"/>
						</el-select>
					</el-form-item>
					<el-form-item style="margin-bottom: 0">
						<el-button icon="Search" type="primary" @click="selectList" @keydown.enter="selectList">
							查询
						</el-button>
						<el-button icon="RefreshRight" plain @click="resetSearch">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-card>
			<el-card :body-style="{ padding: '20px 20px 0 20px' }" style="height: 91%;width: 100%">
				<el-container>
					<el-header height="47px" style="padding: 0 0 15px 0;border-bottom: none;">
						<el-button icon="Plus" plain type="primary" @click="createMethod">
							新增
						</el-button>
					</el-header>
					<el-main style="padding: 0;">
						<el-table :data="dataList" border style="height: 100%;width: 100%;">
							<el-table-column align="center" label="序号" prop="sort" width="80">
								<template #default="scope">
									{{ scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="数据名称" prop="dataName"/>
							<el-table-column align="center" label="数据类型" prop="dataType">
								<template #default="scope">
									{{ scope.row.dataType === '1' ? "系统数据" : "用户自定义" }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="数据分类" prop="systemClassifyConfig.name"/>
							<el-table-column align="center" label="数据结构" prop="dataStructure.name"/>
							<el-table-column align="center" label="数据状态" prop="status" width="92">
								<template #default="scope">
									<el-tag v-if="scope.row.status === '0'" type="danger">禁用</el-tag>
									<el-tag v-if="scope.row.status === '1'">启用</el-tag>
								</template>
							</el-table-column>
							<el-table-column align="center" class-name="small-padding fixed-width" label="操作"
											 min-width="42px">
								<template #default="scope">
									<el-button icon="Edit" link type="primary" @click="editInfo(scope.row)">
										编辑
									</el-button>
									<el-button icon="Delete" link type="danger" @click="deleteInfo(scope.row)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer style="padding: 0;">
						<div style="display: flex;justify-content: flex-end;align-items: center;width: 100%;
							height: 100%">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
										:page-sizes="[15,30,45,60]" :total="total" style="padding: 10px"
										@pagination="selectList()"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>

		<!-- 添加或修改数据配置对话框 -->
		<el-dialog v-if="open" v-model="open" :title="title" append-to-body width="42%">
			<el-form ref="formRef" v-loading="loading" :model="form" :rules="rules" label-width="auto"
					 style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="数据名称" prop="dataName">
					<el-input v-model="form.dataName" clearable placeholder="请输入数据名称"/>
				</el-form-item>
				<el-form-item label="数据类型" prop="dataType">
					<el-select v-model="form.dataType" clearable filterable placeholder="请选择数据类型" style="width: 100%;">
						<el-option label="系统数据" value="1"/>
						<el-option label="用户自定义" value="2"/>
					</el-select>
				</el-form-item>
				<el-form-item v-if="form.dataType === '1'" label="数据源访问地址" prop="dataSourceUrl">
					<el-input v-model="form.dataSourceUrl" clearable placeholder="请输入数据源访问地址"/>
				</el-form-item>
				<el-form-item v-if="form.dataType === '1'" label="数据源访问参数" prop="dataSourceParams">
					<el-button icon="Plus" plain type="primary"
							   @click="() => {
								   paramForm = {};
								   paramOpen = true;
								   paramTitle = '参数添加'
							   }">
						新增
					</el-button>
					<el-table :data="paramsList" border style="margin-top: 15px;">
						<el-table-column align="center" label="序号" prop="sort" width="80">
							<template #default="scope">
								{{ scope.$index + 1 }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="参数名称" prop="paramName">
							<template #default="scope">
								{{ switchDataText(scope.row.paramName) }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="参数值" prop="paramValue"/>
						<el-table-column align="center" label="操作" min-width="42px">
							<template #default="scope">
								<el-button icon="Edit" link type="primary"
										   @click="() => {
											   paramOpen = true;
											   paramForm = {
												   paramName: scope.row.paramName,
												   paramValue: scope.row.paramValue
											   };
											   paramTitle = '参数修改'
										   }"/>
								<el-button icon="Delete" link type="danger" @click="deleteParamInfo(scope.row)"/>
							</template>
						</el-table-column>
					</el-table>
				</el-form-item>
				<el-form-item label="数据分类" prop="dataPlatform">
					<el-tree-select v-model="form.dataPlatform" :data="platformList" :props="{ value: 'id', label: 'name' }"
									clearable filterable highlight-current placeholder="请选择数据分类"
									style="width: 100%;"
									@change="(newData) => {dataPlatformChange(newData);form.dataConfigId = '';}">
					</el-tree-select>
				</el-form-item>
				<el-form-item label="数据结构" prop="dataConfigId">
					<el-tree-select v-model="form.dataConfigId" :data="dataConfigList" :props="{ value: 'id', label: 'name' }" check-strictly
									clearable filterable highlight-current placeholder="请选择数据结构"
									style="width: 100%;"
									@change="(newData) => {dataConfigChange(newData);form.dataRecordName = '';form.dataRecordOrg = '';form.dataRecordOwner='';}">
						<template #default="{ data }">
							<div style="display:flex;justify-content: space-between">
								<el-text>{{ data.name }}</el-text>
								<el-tag size="small">{{ data.versionName }}</el-tag>
							</div>
						</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item v-if="form.dataConfigId" label="数据档案名称" prop="dataRecordName">
					<el-tree-select v-model="form.dataRecordName" :data="dataConfigDetailsList" :max-collapse-tags="3"
									:props="{ value: 'name', label: 'remark' }" check-strictly
									clearable collapse-tags collapse-tags-tooltip
									filterable highlight-current multiple placeholder="请选择数据档案名称"
									style="width: 100%;"
									@change="(newData) => {dataRecordChange(newData);form.dataSortName = '';}">
					</el-tree-select>
				</el-form-item>
				<el-form-item v-if="form.dataRecordName && form.dataConfigId" label="数据排序名称" prop="dataSortName">
					<el-select v-model="form.dataSortName" :data="dataRecordNameList"
							   :props="{ value: 'name', label: 'remark' }" clearable filterable
							   highlight-current placeholder="请选择数据排序名称"
							   style="width: 100%;">
						<el-option v-for="item in dataRecordNameList" :key="item.name" :label="item.remark"
								   :value="item.name"
						/>
					</el-select>
				</el-form-item>
                <el-form-item v-if="form.dataConfigId" label="数据归属机构" prop="dataRecordOrg">
                    <el-tree-select v-model="form.dataRecordOrg" :data="dataConfigDetailsList" :max-collapse-tags="3"
                                    :props="{ value: 'name', label: 'remark' }"
                                    check-strictly clearable collapse-tags collapse-tags-tooltip
                                    filterable highlight-current placeholder="请选择数据归属机构"
                                    style="width: 100%;">
                    </el-tree-select>
                </el-form-item>
                <el-form-item v-if="form.dataConfigId" label="数据开放机构" prop="dataRecordOwner">
                    <el-tree-select v-model="form.dataRecordOwner" :data="dataConfigDetailsList" :max-collapse-tags="3"
                                    :props="{ value: 'name', label: 'remark' }"
                                    check-strictly clearable collapse-tags collapse-tags-tooltip
                                    filterable highlight-current multiple placeholder="请选择数据开放机构"
                                    style="width: 100%;">
                    </el-tree-select>
                </el-form-item>
				<el-form-item label="数据状态" prop="status">
					<el-switch v-model="form.status" active-text="启用" active-value="1" inactive-text="禁用"
							   inactive-value="0" inline-prompt/>
				</el-form-item>
				<el-form-item label="备注" prop="dataRemark">
					<el-input v-model="form.dataRemark" :autosize="{minRows: 6}" clearable maxlength="500"
							  placeholder="请输入备注" show-word-limit type="textarea"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitData">确 定</el-button>
					<el-button @click="() => {open = false;formRef.resetFields();}">取 消</el-button>
				</div>
			</template>
		</el-dialog>
		<!--数据参数添加-->
		<el-dialog v-if="paramOpen" v-model="paramOpen" :title="paramTitle" align-center append-to-body width="30%">
			<el-form ref="paramFormRef" v-loading="loading" :model="paramForm" :rules="rules" label-width="auto"
					 style="margin-top: 0;padding-right: 20px;">
				<el-form-item label="参数名称" prop="paramName">
					<el-select v-model="paramForm.paramName" clearable placeholder="请选择数据类型" style="width: 100%;">
						<el-option label="当前页码" value="page"/>
						<el-option label="每页显示条数" value="size"/>
						<el-option label="查询条件" value="conditions"/>
					</el-select>
				</el-form-item>
				<el-form-item label="参数值" prop="paramValue">
					<div v-if="paramForm.paramName === 'conditions'" style="width: 100%">
						<el-input v-model="paramForm.paramValue" :autosize="{minRows: 5}" clearable placeholder="请输入参数值"
								  type="textarea"/>
						<el-alert title="多个参数请用逗号隔开, 参数示例: create_time > 2020, id = 1" type="info"/>
					</div>
					<el-input v-else v-model="paramForm.paramValue" clearable placeholder="请输入参数值"/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitParamData">确 定</el-button>
					<el-button @click="() => {paramOpen = false;paramFormRef.resetFields();}">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, reactive, ref, toRefs} from 'vue';
import dataManager from '@/api/archive/dataManager';

const {proxy} = getCurrentInstance();
const data = reactive({
	form: {
		dataName: '',
		dataType: '',
		dataRecordName: '',
		dataSortName: '',
		dataPlatform: '',
		dataConfigId: '',
		dataSourceUrl: '',
		dataSourceParams: '',
		dataRemark: '',
		status: '',
        dataRecordOrg:'',
        dataRecordOwner:''
	},
	searchForm: {
		dataName: '',
		dataType: ''
	},
	queryParams: {
		current: 1,
		size: 15,
	},
});
// 表单校验
const rules = reactive({
	dataName: [
		{required: true, message: '请输入数据名称', trigger: 'blur'},
	],
	dataType: [
		{required: true, message: '请输入数据类型', trigger: 'change'},
	],
	dataPlatform: [
		{required: true, message: '请选择数据分类', trigger: 'change'},
	],
	dataConfigId: [
		{required: true, message: '请选择数据结构', trigger: 'change'},
	],
	dataRecordName: [
		{required: true, message: '请选择数据档案名称', trigger: 'change'}
	],
	dataSortName: [
		{required: true, message: '请选择数据排序名称', trigger: 'change'}
	],
	dataRemark: [
		{required: true, message: '请输入数据备注', trigger: 'blur'},
	],
	dataRecordOrg: [
		{required: true, message: '请选择数据归属机构', trigger: 'change'},
	],
})
const paramTitle = ref("");
const loading = ref(false);
const total = ref(0);
const {queryParams, form} = toRefs(data);
// 数据集合
const dataList = ref([]);
const paramForm = ref({});
const searchForm = ref(null);
const allTableList = ref([]);
const platformList = ref([]);
const dataConfigList = ref([]);
const paramsList = ref([]);
const dataConfigDetailsList = ref([]);
const dataRecordNameList = ref([]);
//弹窗状态
const open = ref(false);
const paramOpen = ref(false);
const title = ref("");
const formRef = ref();
const paramFormRef = ref(null);

onBeforeMount(() => {
	selectList();
	selectTableList();
});

//查询数据集合
function selectList() {
	loading.value = true
	dataManager.getList({
		dataName: data.searchForm.dataName,
		dataType: data.searchForm.dataType,
		current: queryParams.value.current,
		size: queryParams.value.size
	}).then(res => {
		if (res.code === 200) {
			dataList.value = res.data.records;
			total.value = res.data.total;
			loading.value = false;
		} else {
			console.log(res.msg);
			proxy.msgError("查询错误: " + res.msg);
		}
	}).catch(error => {
		console.log(error);
		proxy.msgError("查询错误");
	});
}

//重置搜索
function resetSearch() {
	searchForm.value.resetFields();
	selectList();
}

//数据名称处理
function switchDataText(textData) {
	if (textData === 'page') {
		return '当前页码';
	} else if (textData === 'size') {
		return '每页显示条数';
	} else if (textData === 'conditions') {
		return '查询条件';
	} else {
		return '未填写';
	}
}

//数据分类切换
async function dataPlatformChange(data) {
	try {
		const res = await dataManager.versionList({
			platformId: data,
			current: 1,
			size: -1
		});
		if (res.code === 200) {
			dataConfigList.value = res.data.records;
		}
	} catch (error) {
		console.error('请求失败:', error);
	}
}

//数据结构切换
async function dataConfigChange(data) {
	try {
		let filter = dataConfigList.value.filter(dataConfig => dataConfig.id === data);
		if (filter.length > 0) {
			const res = await dataManager.getDetailsList({
				configId: filter[0].structureId,
				current: 0,
				size: -1
			});

			if (res.code === 200) {
				dataConfigDetailsList.value = res.data.records;
			}
		} else {
			dataConfigDetailsList.value = [];
		}
	} catch (error) {
		console.error('请求失败:', error);
	}
}

//数据结构切换
function dataRecordChange(data) {
	try {
		dataRecordNameList.value = [];
		dataConfigDetailsList.value.forEach(details => {
			if (data.includes(details.name)) {
				dataRecordNameList.value.push(details);
			}
		})
	} catch (error) {
		console.error('请求失败:', error);
	}
}

//新增数据
function createMethod() {
	title.value = '新增';
	form.value = {
		dataName: '',
		dataType: '',
		dataRecordName: '',
		dataSortName: '',
		dataPlatform: '',
		dataConfigId: '',
		dataSourceUrl: '',
		dataSourceParams: '',
		dataRemark: '',
        dataRecordOrg:'',
        dataRecordOwner:''
	};
	dataConfigList.value = [];
	dataConfigDetailsList.value = [];
	dataRecordNameList.value = [];
	paramForm.value = {};
	paramsList.value = [];
	open.value = true;
}

//查询所有数据表
function selectTableList() {
	dataManager.getAllTableList().then((res) => {
		allTableList.value = res.data;
	});
	dataManager.classifyList().then((res) => {
		platformList.value = res.data;
	});
	dataManager.dataConfigList().then((res) => {
		dataConfigList.value = res.data;
	});
}

//数据提交
function submitData() {
	formRef.value.validate((valid) => {
		if (valid) {
			if (paramsList.value.length > 0) {
				let returnData = {};
				paramsList.value.forEach(param => {
					if (param.paramName === 'conditions') {
						returnData.conditions = param.paramValue.split(',');
					}
					if (param.paramName === 'size') {
						returnData.size = param.paramValue;
					}
					if (param.paramName === 'page') {
						returnData.page = param.paramValue;
					}
				});
				form.value.dataSourceParams = JSON.stringify(returnData);
			}
			dataManager.save({
				id: form.value.id,
				dataName: form.value.dataName,
				dataType: form.value.dataType,
				dataSourceUrl: form.value.dataSourceUrl,
				dataSourceParams: form.value.dataSourceParams,
				systemClassifyConfig: {
					id: form.value.dataPlatform
				},
				dataStructure: {
					id: form.value.dataConfigId
				},
				dataRecordName: form.value.dataRecordName.join(','),
				dataSortName: form.value.dataSortName,
				dataRemark: form.value.dataRemark,
				status: form.value.status,
                dataRecordOrg: form.value.dataRecordOrg,
                dataRecordOwner: form.value.dataRecordOwner.join(',')
			}).then((res) => {
				if (res.code === 200) {
					proxy.msgSuccess("操作成功");
					open.value = false;
					selectList();
					selectTableList();
					formRef.value.resetFields();
					console.log(formRef.value);
				}
			}).catch((err) => {
				console.log(err);
				proxy.msgError("操作失败");
			});
		}
	});
}

//添加参数
function submitParamData() {
	let filter = paramsList.value.filter(param => param.paramName === paramForm.value.paramName);
	console.log(filter);
	if (filter.length > 0) {
		paramsList.value.forEach(param => {
			console.log(param);
			if (param.paramName === paramForm.value.paramName) {
				console.log(param.paramName);
				param.paramValue = paramForm.value.paramValue;
				console.log(param.paramValue);
			}
		});
	} else {
		paramsList.value.push({
			paramName: paramForm.value.paramName,
			paramValue: paramForm.value.paramValue
		});
	}
	paramOpen.value = false;
	paramFormRef.value.resetFields();
}

//修改数据
async function editInfo(data) {
	dataConfigList.value = [];
	form.value.id = data.id;
	form.value.dataName = data.dataName;
	form.value.dataType = data.dataType;
	form.value.dataSourceUrl = data.dataSourceUrl;
	paramsList.value = data.dataSourceParams ? handleJson(data.dataSourceParams) : [];
	form.value.dataRecordName = data.dataRecordName.split(',');
	form.value.dataSortName = data.dataSortName;
	form.value.dataPlatform = data.systemClassifyConfig ? data.systemClassifyConfig.id : '';
	form.value.dataConfigId = data.dataStructure ? data.dataStructure.id : '';
	form.value.dataRemark = data.dataRemark;
	form.value.status = data.status;
    form.value.dataRecordOrg = data.dataRecordOrg;
    form.value.dataRecordOwner = data.dataRecordOwner?data.dataRecordOwner.split(',') : [];
	await dataPlatformChange(form.value.dataPlatform);
	await dataConfigChange(form.value.dataConfigId);
	dataRecordChange(form.value.dataRecordName);
	title.value = '修改';
	open.value = true;
}

//处理json数据
function handleJson(data) {
	let json = JSON.parse(data);
	let param = [];
	for (let key in json) {
		if (key === 'conditions') {
			param.push({
				paramName: key,
				paramValue: json[key].join(',')
			});
		} else {
			param.push({
				paramName: key,
				paramValue: json[key]
			});
		}
	}
	return param;
}

//删除数据
function deleteInfo(data) {
	proxy.$confirm('是否确认删除该条数据吗?', '提示', {
		type: 'warning',
		confirmButtonText: "确定",
		cancelButtonText: "取消",
	}).then(() => {
		dataManager.delete({
			ids: data.id
		}).then((res) => {
			if (res.code === 200) {
				proxy.msgSuccess("删除成功");
				selectList();
				selectTableList();
			} else {
				proxy.msgError('删除失败')
			}
		});
	}).catch(() => {
		proxy.msgError('取消删除');
	})
}

//删除数据
function deleteParamInfo(data) {
	paramsList.value = paramsList.value.filter(param => !(param.paramName === data.paramName && param.paramValue === data.paramValue))
}
</script>

<style scoped>
.bodyBox {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	grid-row-gap: 10px;
	padding: 10px;
	align-items: stretch;
	justify-content: space-evenly;
}

.tableBox {
	width: 100%;
	height: 100%;
}

:deep(.el-card__body) {
	width: 100%;
	height: 100%;
}
</style>
