<template>
	<el-container>
		<el-aside style="border-right: 0;background: none" width="21%" class="p-el">
			<el-card class="h-full w-full" body-class="h-full w-full p-0">
                <categoryTree :is-the-first-level-clickable="true"  @clickNode="clickEven"/>
			</el-card>
		</el-aside>
		<el-main class="p-el el-flex-column">
			<el-card style="height: auto;width: 100%">
				<div style="display: flex;">
					<el-form ref="formList" :inline="true" :model="form" label-position="right"
							 label-width="auto">
						<el-form-item label="档案名称" prop="name" style="margin: 0;">
							<el-input v-model="form.name" placeholder="请输入档案名称"/>
						</el-form-item>
						<el-form-item label="档案号" prop="numFormat" style="margin: 0;">
							<el-input v-model="form.numFormat" placeholder="请输入档案号"/>
						</el-form-item>
						<el-form-item style="margin: 0;padding-left: 10px;">
							<el-button :icon="Search" type="primary" @click="() => handleQuery()">
								查询
							</el-button>
							<el-button :icon="RefreshRight" plain @click="() => resetQuery()">
								重置
							</el-button>
						</el-form-item>
					</el-form>
				</div>
			</el-card>
			<el-card :body-style="{ height: '100%', width: '100%', padding: '15px 15px 0 15px' }"
					 style="height: 91%;width: 100%">
				<el-container>
					<el-header height="auto" style="padding: 0 0 10px 0">
						<div style="width:100%;display: flex;justify-content:space-between;align-items:center;">
							<div>
								<el-button icon="RefreshLeft" plain type="success" @click="() => collectFile()">
									恢复
								</el-button>
								<el-button icon="Delete" plain type="danger" @click="() => collectDelete()">
									删除
								</el-button>
							</div>
							<div style="display: flex;align-items: center">
								<div style="margin-right: 15px;" @click="getList">
									<el-tooltip class="box-item" content="刷新" effect="dark" placement="top">
										<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
											<Refresh/>
										</el-icon>
									</el-tooltip>
								</div>
								<div @click="screen">
									<el-tooltip class="box-item" content="全屏" effect="dark" placement="top">
										<el-icon :size="20" color="#409EFC" style="cursor:pointer;">
											<FullScreen/>
										</el-icon>
									</el-tooltip>
								</div>
							</div>
						</div>
					</el-header>
					<el-main style="padding: 0">
						<el-table :data="receiveData" border style="height: 100%;width: 100%"
								  @selection-change="handleSelectionChange">
							<el-table-column align="center" min-width="30" type="selection" width="50"/>
							<el-table-column align="left" label="档案名称" prop="name"/>
							<el-table-column align="center" label="档案号" prop="num">
								<template #default="scope">
									{{ scope.row.num ? scope.row.num : '暂无' }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="档案年份" prop="year" width="100"/>
							<el-table-column align="center" label="档案保留时间" prop="retentionPeriod" width="100">
								<template #default="scope">
									{{ reserve(scope.row.retentionPeriod) }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="档案保密等级" prop="protectLevel" width="100">
								<template #default="scope">
									{{ secrecy(scope.row.protectLevel) }}
								</template>
							</el-table-column>
							<el-table-column align="center" label="档案控制状态" prop="controlStatus" width="100">
								<template #default="scope">
									{{ control(scope.row.controlStatus) }}
								</template>
							</el-table-column>
							<el-table-column align="center" class-name="small-padding fixed-width" label="操作"
											 width="92">
								<template #default="scope">
									<el-button icon="RefreshLeft" link type="success"
											   @click="collectFile(scope.row)">恢复
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-main>
					<el-footer>
						<div style="display: flex;justify-content: flex-end">
							<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current"
										:page-sizes="[20,40,60,80]" :total="total" style="padding: 0"
										@pagination="pageMethod()"/>
						</div>
					</el-footer>
				</el-container>
			</el-card>
		</el-main>
	</el-container>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import {FullScreen, Refresh, RefreshRight, Search} from '@element-plus/icons-vue'
import collectList from '@/api/archive/archiveReception/collect';
import tool from "@/utils/tool";

const data = reactive({
	queryParams: {
		current: 1,
		size: 20,
	}
});
const total = ref(0);
const {queryParams} = toRefs(data);
const {proxy} = getCurrentInstance();
// 接收库List
const receiveData = ref([]);
// 点击查询列表
const openTree = ref(false);
const handList = ref([]);
// 点击树结构的id
const clickEvenId = ref([]);
// 头部查询
const form = ref({})

onMounted(() => {
	getList();
});

function handleQuery() {
	clickEvenList(clickEvenId.value);
}

// 重置
function resetQuery() {
	form.value = [];
	clickEvenList(clickEvenId.value);
}

function getList() {
	queryParams.value.current = 1;
	collectList.list({
		current: queryParams.value.current,
		size: queryParams.value.size,
		delFlag: 1,
	}).then(res => {
		if (res.code === 200) {
			receiveData.value = res.data.records;
			total.value = res.data.total;
			openTree.value = true;
		}
	}).catch(() => {
		proxy.msgError('查询失败');
	})
}

//全屏
function screen() {
	let element = document.documentElement;
	tool.screen(element);
}

// 分页
function pageMethod() {
	if (clickEvenId.value.length > 0) {
		clickEvenList(clickEvenId.value);
	} else {
		handleQuery();
	}
}

function clickEven(val) {
	clickEvenId.value = val;
	clickEvenList(val);
}

// 点击树结构查询表格
function clickEvenList(val) {
	if (val.dataType === '1') {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			name: form.value.name,
			numFormat: form.value.numFormat,
			'controlGroup.id': val.id,
			delFlag: 1,
		}).then(res => {
			if (res.code === 200) {
				receiveData.value = res.data.records;
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	} else {
		collectList.list({
			current: queryParams.value.current,
			size: queryParams.value.size,
			name: form.value.name,
			numFormat: form.value.numFormat,
			'controlCategory.id': val.id,
			delFlag: 1
		}).then(res => {
			if (res.code === 200) {
				receiveData.value = res.data.records;
				total.value = res.data.total;
				openTree.value = true;
			}
		}).catch(() => {
			proxy.msgError('查询失败');
		})
	}
}

// 选中表格
function handleSelectionChange(val) {
	handList.value = val;
}

// 恢复
function collectFile(val) {
	if (val) {
		proxy.$confirm('是否要恢复这条数据?', '提示', {
			type: 'warning',
			confirmButtonText: "确定",
			cancelButtonText: "取消",
		}).then(() => {
			let data = {};
			data.delFlag = 0;
			data.infoIds = val.id;
			collectList.infoOperate(data).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess('恢复成功');
					clickEvenList(clickEvenId.value);
				}
			}).catch(() => {
				proxy.msgError('恢复失败');
			})
		}).catch(() => {
			console.log(111);
		})
	} else if (handList.value.length > 0) {
		proxy.$confirm('是否要恢复这条数据?', '提示', {
			type: 'warning',
			confirmButtonText: "确定",
			cancelButtonText: "取消",
		}).then(() => {
			let idStr = handList.value.map((v) => v.id);
			//拼接的数组字符串，接口传参
			let ids = idStr.join(",");
			let data = {};
			data.delFlag = 0;
			data.infoIds = ids;
			collectList.infoOperate(data).then(res => {
				if (res.code === 200) {
					proxy.msgSuccess('恢复成功');
					clickEvenList(clickEvenId.value);
				}
			}).catch(() => {
				proxy.msgError('恢复失败');
			})
		}).catch(() => {
			console.log(111);
		})
	} else {
		proxy.msgError('请选择一条数据');
	}
}

// 删除
function collectDelete() {
	if (handList.value.length > 0) {
		let idStr = handList.value.map((v) => v.id);
		//拼接的数组字符串，接口传参
		let ids = idStr.join(",");
		proxy.$confirm('所选档案及相关信息会被彻底删除，请确认是否进行删除操作?', '提示', {
			type: 'warning',
			confirmButtonText: "确认",
			cancelButtonText: "取消",
		}).then(() => {
			collectList.thoroughDelete({ids}).then(res => {
				if (res.code === 200) {
					clickEvenList(clickEvenId.value);
					proxy.msgSuccess('删除成功');
				} else {
					proxy.msgError('删除失败')
				}
			})
		}).catch((err) => {
			console.log(err);
		})
	} else {
		proxy.msgError('请先选择删除的数据！');
	}
}

// 保留年限
function reserve(val) {
	if (val == 'Y') {
		return '永久'
	} else if (val == 'D5') {
		return '5年'
	} else if (val == 'D10') {
		return '10年 '
	} else if (val == 'D20') {
		return '20年'
	} else if (val == 'D30') {
		return '30年'
	} else {
		return '暂无'
	}
}

// 保密等级
function secrecy(val) {
	if (val == 'GK') {
		return '公开'
	} else if (val == 'KZ') {
		return '限制'
	} else if (val == 'MOM') {
		return '秘密 '
	} else if (val == 'JM') {
		return '机密'
	} else if (val == 'UM') {
		return '绝密'
	} else {
		return '暂无'
	}
}

// 控制等级
function control(val) {
	if (val == '1') {
		return '公开'
	} else if (val == '2') {
		return '公司内部开放'
	} else if (val == '3') {
		return '部门内部开放 '
	} else if (val == '4') {
		return '控制'
	} else {
		return '暂无'
	}
}
</script>

<style scoped></style>
