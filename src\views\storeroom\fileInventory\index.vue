<template>
    <DragLayout type="row">
        <template #first>
            <el-aside class="p-el w-full h-full">
                <el-card class="tree-card" body-class="h-full w-full p-0">
                    <tree @clickChild="clickEven" />
                </el-card>
            </el-aside>
        </template>
        <template #second>
            <el-main ref="main" class="h-full p-el">
                <el-card :body-style="{ padding: '5px 15px 0 15px' }" class="box-card content-card">
                    <el-tabs v-model="activeName" class="content-tabs">
                        <el-tab-pane label="盘点计划" name="1">
                            <el-button icon="Plus" plain size="default" type="primary" @click="personelAdd(planform)"> 新增 </el-button>
                            <el-table v-loading="loading" :data="dataList" border>
                                <el-table-column align="center" label="计划标题" min-width="200" prop="checkPlanTitle" />
                                <el-table-column :formatter="(row) => moment(row.checkPlanStartTime).format('YYYY-MM-DD')" align="center" label="计划开始日期" prop="checkPlanStartTime" width="130" />
                                <el-table-column :formatter="(row) => moment(row.checkPlanEndTime).format('YYYY-MM-DD')" align="center" label="计划完成日期" prop="checkPlanEndTime" width="130" />
                                <el-table-column align="center" label="盘点负责人" prop="checkPlanPrincipal.name" width="150" />
                                <el-table-column align="center" label="盘点成员" min-width="180" prop="checkPlanPersonIds" />
                                <el-table-column align="center" label="盘点项" min-width="200" prop="checkPlanItem">
                                    <!-- {{ checkPlanItem }} -->
                                </el-table-column>
                                <el-table-column align="center" fixed="right" label="操作" prop="boxYear" width="210">
                                    <template #default="scope">
                                        <el-button icon="View" link size="small" type="primary" @click="planDetail(scope.row)">查看</el-button>
                                        <el-button icon="Edit" link size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                                        <el-button icon="Delete" link size="small" type="danger" @click="planDelete(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="el-page">
                                <pagination v-model:limit="planQueryParams.size" v-model:page="planQueryParams.current" :total="planTotal" @pagination="getList" />
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="盘点记录" name="2">
                            <el-button icon="Plus" plain size="default" type="primary" @click="humidityAdd(addRecordForm)"> 新增 </el-button>
                            <el-table ref="recordTable" v-loading="loading" :data="recordList" border>
                                <el-table-column type="expand">
                                    <template #default="scope">
                                        <el-table :data="scope.row.list" border stripe style="width: 100%">
                                            <el-table-column align="center" label="盘点项" prop="planDetailItem" />
                                            <el-table-column align="center" label="执行人" prop="checkPlanPersonIds" />
                                            <el-table-column align="center" label="盘点结果" prop="planDetailResult" />
                                        </el-table>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="盘点计划标题" prop="checkPlanInfo.checkPlanTitle" />
                                <el-table-column align="center" label="执行开始时间" prop="checkHistoryStartTime" />
                                <el-table-column align="center" label="执行完成时间" prop="checkHistoryEndTime" />
                                <el-table-column align="center" label="盘点负责人" prop="checkPlanInfo.checkPlanPrincipal.name" />
                                <el-table-column align="center" fixed="right" label="操作" width="210">
                                    <template #default="scope">
                                        <el-button icon="View" link size="small" type="primary" @click="humidityDetail(scope.row)">查看</el-button>
                                        <el-button icon="Edit" link size="small" type="primary" @click="humidityEdit(scope.row)">编辑</el-button>
                                        <el-button icon="Delete" link size="small" type="danger" @click="humidityDelete(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="el-page">
                                <pagination v-model:limit="recordQueryParams.size" v-model:page="recordQueryParams.current" :total="recordTotal" @pagination="getList2" />
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="修复记录" name="3">
                            <el-button icon="Plus" plain size="default" type="primary" @click="repairAdd(humidityform)"> 新增 </el-button>
                            <el-table v-loading="loading" :data="humidityList" border>
                                <el-table-column align="center" label="档号" prop="info.num" width="250" />
                                <el-table-column align="center" label="档案名称" prop="info.name" width="250" />
                                <el-table-column align="center" label="修复人" prop="replenishPerson.name" />
                                <el-table-column align="center" label="修复时间" min-width="120" prop="replenishTime" />
                                <el-table-column align="center" label="修复结果" min-width="150" prop="replenishResult" show-overflow-tooltip />
                                <el-table-column align="center" fixed="right" label="操作" width="220">
                                    <template #default="scope">
                                        <el-button icon="View" link size="small" type="primary" @click="repairDetail(scope.row)">查看</el-button>
                                        <el-button icon="Edit" link size="small" type="primary" @click="repairEdit(scope.row)">编辑</el-button>
                                        <el-button icon="Delete" link size="small" type="danger" @click="repairDelete(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="el-page">
                                <pagination v-model:limit="repairQueryParams.size" v-model:page="repairQueryParams.current" :total="repairTotal" @pagination="getList3" />
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </el-card>
            </el-main>
        </template>
    </DragLayout>
    <!-- 盘点计划增加弹框 -->
    <el-dialog v-model="dialogFormVisible" title="新增盘点计划" width="30%">
        <el-form ref="planform" :model="planForm" :rules="planRules" label-position="right" label-width="110px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="计划标题" prop="checkPlanTitle">
                    <el-input v-model="planForm.checkPlanTitle" clearable placeholder="请输入计划标题" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="计划开始时间" prop="checkPlanStartTime">
                    <el-date-picker v-model="planForm.checkPlanStartTime" placeholder="请选择计划开始时间" style="width: 90%" value-format="YYYY-MM-DD" @change="onPlanStartTimeChange"></el-date-picker>
                </el-form-item>
                <el-form-item label="计划完成时间" prop="checkPlanEndTime">
                    <el-date-picker v-model="planForm.checkPlanEndTime" placeholder="请选择计划完成时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="盘点负责人" prop="checkPlanPrincipal">
                    <el-select v-model="planForm.checkPlanPrincipal" class="form_225" clearable placeholder="请选择盘点负责人" style="width: 90% !important">
                        <el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="盘点成员" prop="checkPlanPersonIds">
                    <el-select v-model="planForm.checkPlanPersonIds" class="form_225" clearable collapse-tags collapse-tags-tooltip multiple placeholder="请选择盘点成员" style="width: 90% !important">
                        <el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="盘点项" prop="checkPlanItem">
                    <el-select v-model="planForm.checkPlanItem" class="form_225" clearable collapse-tags collapse-tags-tooltip multiple placeholder="请选择盘点项" style="width: 90% !important">
                        <el-option v-for="item in checkitemList" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="planForm.remark" clearable placeholder="" size="default" style="width: 90%" type="textarea"></el-input>
                </el-form-item>
            </div>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="addPlan(planform)">确定</el-button>
        </div>
    </el-dialog>
    <!-- 查看盘点计划弹框 -->
    <el-dialog v-model="planDetailVisible" title="盘点计划详情" width="30%">
        <el-form ref="form" :model="plandetailForm" :rules="rules" label-position="right" label-width="100px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="计划标题" prop="valueName">
                    <el-input v-model="plandetailForm.checkPlanTitle" clearable disabled placeholder="请输入计划标题" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="计划开始时间">
                    <el-date-picker v-model="plandetailForm.checkPlanStartTime" disabled placeholder="请选择计划开始时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="计划完成时间">
                    <el-date-picker v-model="plandetailForm.checkPlanEndTime" disabled placeholder="请选择计划完成时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="盘点负责人" prop="valueName">
                    <el-input v-model="plandetailForm.checkPlanPrincipal.name" class="form_225" clearable disabled style="width: 90% !important"> </el-input>
                </el-form-item>
                <el-form-item label="盘点成员">
                    <el-input v-model="plandetailForm.checkPlanPersonIds" class="form_225" clearable collapse-tags collapse-tags-tooltip disabled multiple style="width: 90% !important"> </el-input>
                </el-form-item>
                <el-form-item label="盘点项">
                    <el-input v-model="plandetailForm.checkPlanItem" class="form_225" clearable collapse-tags collapse-tags-tooltip disabled multiple style="width: 90% !important"> </el-input>
                </el-form-item>
                <el-form-item label="备注" prop="valueName">
                    <el-input v-model="plandetailForm.remark" clearable disabled placeholder="" size="default" style="width: 90%" type="textarea"></el-input>
                </el-form-item>
            </div>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="planDetailVisible = false">取消</el-button>
            <!-- <el-button type="primary" @click="addPlan(form)">确定</el-button> -->
        </div>
    </el-dialog>
    <!-- 盘点计划编辑弹框 -->
    <el-dialog v-model="editdialogFormVisible" title="修改盘点计划" width="30%">
        <el-form ref="editplanform" :model="editplanForm" :rules="editPlanRules" label-position="right" label-width="110px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="计划标题" prop="checkPlanTitle">
                    <el-input v-model="editplanForm.checkPlanTitle" clearable placeholder="请输入计划标题" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="计划开始时间" prop="checkPlanStartTime">
                    <el-date-picker v-model="editplanForm.checkPlanStartTime" placeholder="请选择计划开始时间" style="width: 90%" value-format="YYYY-MM-DD" @change="onEditPlanStartTimeChange"></el-date-picker>
                </el-form-item>
                <el-form-item label="计划完成时间" prop="checkPlanEndTime">
                    <el-date-picker v-model="editplanForm.checkPlanEndTime" placeholder="请选择计划完成时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="盘点负责人" prop="checkPlanPrincipal.id">
                    <el-select v-model="editplanForm.checkPlanPrincipal.id" class="form_225" clearable placeholder="请选择盘点负责人" style="width: 90% !important">
                        <el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="盘点成员" prop="checkPlanPersonIds">
                    <el-select v-model="editplanForm.checkPlanPersonIds" class="form_225" clearable collapse-tags collapse-tags-tooltip multiple placeholder="请选择盘点成员" style="width: 90% !important">
                        <el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="盘点项" prop="checkPlanItem">
                    <el-select v-model="editplanForm.checkPlanItem" class="form_225" clearable collapse-tags collapse-tags-tooltip multiple placeholder="请选择盘点项" style="width: 90% !important">
                        <el-option v-for="item in checkitemList" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="editplanForm.remark" clearable placeholder="" size="default" style="width: 90%" type="textarea"></el-input>
                </el-form-item>
            </div>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="editdialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="editPlan(editplanform)">确定</el-button>
        </div>
    </el-dialog>
    <!-- 盘点记录增加弹框 -->
    <el-dialog v-model="dialogFormVisible2" title="新增盘点记录" width="35%">
        <el-form ref="addRecordForm" :model="recordForm" :rules="recordrules" label-position="right" label-width="110px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="盘点计划标题" prop="checkPlanTitle">
                    <el-select v-model="recordForm.checkPlanTitle" class="form_225" clearable placeholder="请选择盘点计划标题" style="width: 90% !important" @change="titleChacnge">
                        <el-option v-for="item in titleList" :key="item.id" :label="item.title" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行开始时间" prop="checkHistoryStartTime">
                    <el-date-picker v-model="recordForm.checkHistoryStartTime" placeholder="请选择执行开始时间" style="width: 90%" value-format="YYYY-MM-DD" @change="onRecordStartTimeChange"></el-date-picker>
                </el-form-item>
                <el-form-item label="执行完成时间" prop="checkHistoryEndTime">
                    <el-date-picker v-model="recordForm.checkHistoryEndTime" placeholder="请选择执行完成时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="盘点负责人" prop="checkPlanPrincipal">
                    <el-input v-model="recordForm.checkPlanPrincipalName" class="form_225" clearable disabled placeholder="请先选择盘点计划标题" style="width: 90% !important"> </el-input>
                </el-form-item>
            </div>
        </el-form>
        <el-table :data="bottomList" border style="margin-left: 30px; margin-bottom: 15px; width: 90%">
            <el-table-column :formatter="(row) => formDict(checkitemList, row.checkPlanItem)" align="center" label="盘点项" prop="checkPlanItem" />
            <el-table-column align="center" label="执行人" prop="zhixing">
                <template #default="scope">
                    <div v-clickOutside="($event) => handleOutsideClick(scope, 'isShow', $event)">
                        <p v-if="!scope.row.isShow" @click="handleInputEdit(scope)">
                            {{ filterArr(orguserList, 'id', scope.row.zhixing) || '请选择执行人' }}
                        </p>
                        <div v-if="scope.row.isShow" id="div">
                            <el-select id="div" v-model="scope.row.zhixing" :popper-append-to-body="false" multiple placeholder="请选择执行人" style="width: 100%" @blur="handleExecutorBlurAdd(scope)" @change="handleExecutorChangeAdd(scope)">
                                <el-option v-for="item in orguserList" id="div" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column align="center" label="盘点结果" prop="checkPlanTitle">
                <template #default="scope">
                    <div v-clickOutside="($event) => handleOutsideClick(scope, 'isShow2', $event)">
                        <p v-show="!scope.row.isShow2" @click="handleInputEdit2(scope)">
                            {{ scope.row.checkPlanTitle || '请输入盘点结果' }}
                        </p>
                        <div v-show="scope.row.isShow2">
                            <el-input id="input" v-model="scope.row.checkPlanTitle" clearable placeholder="请输入盘点结果" size="default" style="width: 100%"></el-input>
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="dialog-footer">
            <el-button @click="dialogFormVisible2 = false">取消</el-button>
            <el-button type="primary" @click="recordAdd(addRecordForm)">确定</el-button>
        </div>
    </el-dialog>
    <!-- 盘点记录查看弹框 -->
    <el-dialog v-model="humidityDetailVisible" title="盘点记录详情" width="35%">
        <el-form ref="detailRecordForm" :model="recorddatailForm" :rules="rules" label-position="right" label-width="100px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="盘点计划标题">
                    <el-input v-model="recorddatailForm.checkPlanTitle" class="form_225" disabled placeholder="请选择盘点计划标题" style="width: 90% !important"> </el-input>
                </el-form-item>
                <el-form-item label="执行开始时间">
                    <el-date-picker v-model="recorddatailForm.checkHistoryStartTime" disabled placeholder="请选择执行开始时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="执行完成时间">
                    <el-date-picker v-model="recorddatailForm.checkHistoryEndTime" disabled placeholder="请选择执行完成时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="盘点负责人">
                    <el-input v-model="recorddatailForm.checkPlanPrincipalName" class="form_225" clearable disabled placeholder="盘点负责人" style="width: 90% !important"> </el-input>
                </el-form-item>
            </div>
        </el-form>
        <el-table :data="bottomList2" border style="margin-left: 30px; margin-bottom: 15px; width: 90%">
            <el-table-column align="center" label="盘点项" prop="checkPlanItem">
                <template #default="scope">
                    <p>{{ scope.row.planDetailItem }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" label="执行人" prop="wet">
                <template #default="scope">
                    <p>{{ scope.row.checkPlanPersonIds }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" label="盘点结果" prop="ery">
                <template #default="scope">
                    <p>{{ scope.row.planDetailResult }}</p>
                </template>
            </el-table-column>
        </el-table>
        <div class="dialog-footer">
            <el-button @click="humidityDetailVisible = false">取消</el-button>
        </div>
    </el-dialog>
    <!-- 盘点记录编辑弹框 -->
    <el-dialog v-model="edithumidityVisible" title="修改盘点记录" width="35%">
        <el-form ref="editRecordForm" :model="editrecordForm" :rules="editRecordRules" label-position="right" label-width="110px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="盘点计划标题" prop="checkPlanTitle">
                    <el-select v-model="editrecordForm.checkPlanTitle" class="form_225" clearable placeholder="请选择盘点计划标题" style="width: 90% !important" @change="titleChacnge">
                        <el-option v-for="item in titleList" :key="item.id" :label="item.title" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行开始时间" prop="checkHistoryStartTime">
                    <el-date-picker v-model="editrecordForm.checkHistoryStartTime" placeholder="请选择执行开始时间" style="width: 90%" value-format="YYYY-MM-DD" @change="onEditRecordStartTimeChange"></el-date-picker>
                </el-form-item>
                <el-form-item label="执行完成时间" prop="checkHistoryEndTime">
                    <el-date-picker v-model="editrecordForm.checkHistoryEndTime" placeholder="请选择执行完成时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="盘点负责人" prop="checkPlanPrincipal">
                    <el-input v-model="editrecordForm.checkPlanPrincipalName" class="form_225" clearable disabled placeholder="请先选择盘点计划标题" style="width: 90% !important"> </el-input>
                </el-form-item>
            </div>
        </el-form>
        <el-table :data="editbottomList" border style="margin-left: 30px; margin-bottom: 15px; width: 90%">
            <el-table-column :formatter="(row) => formDict(checkitemList, row.planDetailItem)" align="center" label="盘点项" prop="planDetailItem">
                <!-- <template #default="scope">
					<p>{{ scope.row.planDetailItem }}</p>
				</template> -->
            </el-table-column>

            <el-table-column align="center" label="执行人" prop="wet">
                <template #default="scope">
                    <div v-clickOutside="($event) => handleOutsideClick(scope, 'isShow', $event)">
                        <p v-if="!scope.row.isShow" @click="handleInputEdit(scope)">
                            {{ filterArr(orguserList, 'id', scope.row.checkPlanPersonIds) || '请选择执行人' }}
                        </p>
                        <div v-if="scope.row.isShow" id="div">
                            <el-select
                                id="div"
                                v-model="scope.row.checkPlanPersonIds"
                                :popper-append-to-body="false"
                                collapse-tags
                                collapse-tags-tooltip
                                multiple
                                placeholder="请选择执行人"
                                style="width: 100%"
                                @blur="handleExecutorBlur(scope)"
                                @change="handleExecutorChange(scope)"
                            >
                                <el-option v-for="item in orguserList" id="div" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column align="center" label="盘点结果" prop="ery">
                <template #default="scope">
                    <div v-clickOutside="($event) => handleOutsideClick(scope, 'isShow2', $event)">
                        <p v-show="!scope.row.isShow2" @click="handleInputEdit2(scope)">
                            {{ scope.row.planDetailResult || '请输入盘点结果' }}
                        </p>
                        <div v-show="scope.row.isShow2">
                            <el-input id="input" v-model="scope.row.planDetailResult" clearable placeholder="请输入盘点结果" size="default" style="width: 100%"></el-input>
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="dialog-footer">
            <el-button @click="edithumidityVisible = false">取消</el-button>
            <el-button type="primary" @click="recordEdit(editRecordForm)">确定</el-button>
        </div>
    </el-dialog>
    <!-- 修复记录增加弹框 -->
    <el-dialog v-model="dialogFormVisible3" title="新增修复记录" width="30%">
        <el-form ref="humidityform" :model="humidityForm" :rules="humidityRules" label-position="right" label-width="90px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="档号" prop="num">
                    <el-select v-model="humidityForm.num" class="form_225" clearable filterable placeholder="请选择档号" style="width: 90% !important" @change="numberChange">
                        <el-option v-for="item in numberStatus" :key="item.id" :label="item.num" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="档案名称" prop="infoName">
                    <el-input v-model="humidityForm.infoName" clearable disabled placeholder="请选择档号" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="修复人" prop="replenishPerson">
                    <el-select v-model="humidityForm.replenishPerson" class="form_225" clearable placeholder="请选择修复人" style="width: 90% !important">
                        <el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="修复时间" prop="replenishTime">
                    <el-date-picker v-model="humidityForm.replenishTime" placeholder="请选择修复时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="修复结果" prop="replenishResult">
                    <el-input v-model="humidityForm.replenishResult" clearable placeholder="" size="default" style="width: 90%" type="textarea"></el-input>
                </el-form-item>
            </div>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="dialogFormVisible3 = false">取消</el-button>
            <el-button type="primary" @click="creatRepair(humidityform)">确定</el-button>
        </div>
    </el-dialog>
    <!-- 查看修复记录弹框 -->
    <el-dialog v-model="editdialogFormVisible3" title="修复记录详情" width="30%">
        <el-form ref="form" :model="edithumidityForm" :rules="rules" label-position="right" label-width="90px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="档号" prop="valueName">
                    <el-input v-model="edithumidityForm.num" clearable disabled placeholder="" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="档案名称">
                    <el-input v-model="edithumidityForm.infoName" clearable disabled placeholder="" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="修复人">
                    <el-input v-model="edithumidityForm.replenishPerson" clearable disabled placeholder="" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="修复时间">
                    <el-date-picker v-model="edithumidityForm.replenishTime" disabled placeholder="请选择修复时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="修复结果">
                    <el-input v-model="edithumidityForm.replenishResult" clearable disabled placeholder="" size="default" style="width: 90%" type="textarea"></el-input>
                </el-form-item>
            </div>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="editdialogFormVisible3 = false">取消</el-button>
        </div>
    </el-dialog>
    <!-- 修改修复记录弹框 -->
    <el-dialog v-model="detaildialogFormVisible3" title="修改修复记录" width="30%">
        <el-form ref="humidityeditform" :model="humiditydetailForm" :rules="humidityEditRules" label-position="right" label-width="90px" size="default">
            <div style="margin-left: 5%">
                <el-form-item label="档号" prop="info.id">
                    <el-select v-model="humiditydetailForm.info.id" class="form_225" clearable filterable placeholder="请选择档号" style="width: 90% !important" @change="numberChange">
                        <el-option v-for="item in numberStatus" :key="item.id" :label="item.num" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="档案名称" prop="info.name">
                    <el-input v-model="humiditydetailForm.info.name" clearable disabled placeholder="" size="default" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="修复人" prop="replenishPerson.id">
                    <el-select v-model="humiditydetailForm.replenishPerson.id" class="form_225" clearable placeholder="请选择修复人" style="width: 90% !important">
                        <el-option v-for="item in orguserList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="修复时间" prop="replenishTime">
                    <el-date-picker v-model="humiditydetailForm.replenishTime" placeholder="请选择修复时间" style="width: 90%" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item label="修复结果" prop="replenishResult">
                    <el-input v-model="humiditydetailForm.replenishResult" clearable placeholder="" size="default" style="width: 90%" type="textarea"></el-input>
                </el-form-item>
            </div>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="detaildialogFormVisible3 = false">取消</el-button>
            <el-button type="primary" @click="editRepair(humidityeditform)">确定</el-button>
        </div>
    </el-dialog>
</template>

<script setup>
import { getCurrentInstance, reactive, ref } from 'vue';
import storeroomData from '@/api/archive/storeroom/storeroom';
import fileInventory from '@/api/archive/storeroom/fileInventory';
import DragLayout from '@/components/DragLayout/index.vue';
import { ElMessage } from 'element-plus';
import tree from '../tree';
import moment from 'moment';
import { getOrganizationInfo } from '@/utils/permission';

// 日期验证器 - 验证结束日期不能早于开始日期
const validateEndDate = (_, value, callback) => {
    if (!value) {
        callback(new Error('请选择计划完成时间'));
        return;
    }
    if (!planForm.checkPlanStartTime) {
        callback();
        return;
    }
    try {
        const startDate = new Date(planForm.checkPlanStartTime);
        const endDate = new Date(value);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            callback();
            return;
        }
        if (endDate < startDate) {
            callback(new Error('计划完成时间不能早于计划开始时间'));
        } else {
            callback();
        }
    } catch (error) {
        callback();
    }
};

const planRules = reactive({
    checkPlanTitle: [{ required: true, message: '请填写计划标题', trigger: 'blur' }],
    checkPlanStartTime: [{ required: true, message: '请选择计划开始时间', trigger: 'blur' }],
    checkPlanEndTime: [
        { required: true, message: '请选择计划完成时间', trigger: 'blur' },
        { validator: validateEndDate, trigger: 'change' }
    ],
    checkPlanPrincipal: [{ required: true, message: '请选择盘点负责人', trigger: 'blur' }],
    checkPlanPersonIds: [{ required: true, message: '请选择盘点成员', trigger: 'blur' }],
    checkPlanItem: [{ required: true, message: '请选择盘点项', trigger: 'blur' }]
});
// 编辑表单日期验证器 - 验证结束日期不能早于开始日期
const validateEditEndDate = (_, value, callback) => {
    if (!value) {
        callback(new Error('请选择计划完成时间'));
        return;
    }
    if (!editplanForm.value || !editplanForm.value.checkPlanStartTime) {
        callback();
        return;
    }
    try {
        const startDate = new Date(editplanForm.value.checkPlanStartTime);
        const endDate = new Date(value);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            callback();
            return;
        }
        if (endDate < startDate) {
            callback(new Error('计划完成时间不能早于计划开始时间'));
        } else {
            callback();
        }
    } catch (error) {
        callback();
    }
};

const editPlanRules = reactive({
    checkPlanTitle: [{ required: true, message: '请填写计划标题', trigger: 'blur' }],
    checkPlanStartTime: [{ required: true, message: '请选择计划开始时间', trigger: 'blur' }],
    checkPlanEndTime: [
        { required: true, message: '请选择计划完成时间', trigger: 'blur' },
        { validator: validateEditEndDate, trigger: 'change' }
    ],
    'checkPlanPrincipal.id': [{ required: true, message: '请选择盘点负责人', trigger: 'blur' }],
    checkPlanPersonIds: [{ required: true, message: '请选择盘点成员', trigger: 'blur' }],
    checkPlanItem: [{ required: true, message: '请选择盘点项', trigger: 'blur' }]
});
// 盘点记录日期验证器 - 验证执行完成时间不能早于执行开始时间
const validateRecordEndDate = (_, value, callback) => {
    if (!value) {
        callback(new Error('请选择执行完成时间'));
        return;
    }
    if (!recordForm.checkHistoryStartTime) {
        callback();
        return;
    }
    try {
        const startDate = new Date(recordForm.checkHistoryStartTime);
        const endDate = new Date(value);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            callback();
            return;
        }
        if (endDate < startDate) {
            callback(new Error('执行完成时间不能早于执行开始时间'));
        } else {
            callback();
        }
    } catch (error) {
        callback();
    }
};

// 编辑盘点记录日期验证器 - 验证执行完成时间不能早于执行开始时间
const validateEditRecordEndDate = (_, value, callback) => {
    if (!value) {
        callback(new Error('请选择执行完成时间'));
        return;
    }
    if (!editrecordForm.checkHistoryStartTime) {
        callback();
        return;
    }
    try {
        const startDate = new Date(editrecordForm.checkHistoryStartTime);
        const endDate = new Date(value);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            callback();
            return;
        }
        if (endDate < startDate) {
            callback(new Error('执行完成时间不能早于执行开始时间'));
        } else {
            callback();
        }
    } catch (error) {
        callback();
    }
};

const recordrules = reactive({
    checkPlanTitle: [{ required: true, message: '请选择盘点计划标题', trigger: 'blur' }],
    checkHistoryStartTime: [{ required: true, message: '请选择执行开始时间', trigger: 'blur' }],
    checkHistoryEndTime: [
        { required: true, message: '请选择执行完成时间', trigger: 'blur' },
        { validator: validateRecordEndDate, trigger: 'change' }
    ],
    checkPlanPrincipal: [{ required: true, message: '请选择盘点负责人', trigger: 'blur' }]
});
const editRecordRules = reactive({
    checkPlanTitle: [{ required: true, message: '请选择盘点计划标题', trigger: 'blur' }],
    checkHistoryStartTime: [{ required: true, message: '请选择执行开始时间', trigger: 'blur' }],
    checkHistoryEndTime: [
        { required: true, message: '请选择执行完成时间', trigger: 'blur' },
        { validator: validateEditRecordEndDate, trigger: 'change' }
    ],
    checkPlanPrincipal: [{ required: true, message: '请选择盘点负责人', trigger: 'blur' }]
});
const humidityRules = reactive({
    num: [{ required: true, message: '请选择档号', trigger: 'blur' }],
    infoName: [{ required: true, message: '请选择档号', trigger: 'blur' }],
    replenishPerson: [{ required: true, message: '请选择修复人', trigger: 'blur' }],
    replenishTime: [{ required: true, message: '请选择修复时间', trigger: 'blur' }],
    replenishResult: [{ required: true, message: '请填写修复结果', trigger: 'blur' }]
});
const humidityEditRules = reactive({
    'info.id': [{ required: true, message: '请选择档号', trigger: 'blur' }],
    'info.name': [{ required: true, message: '请选择档号', trigger: 'blur' }],
    'replenishPerson.id': [{ required: true, message: '请选择修复人', trigger: 'blur' }],
    replenishTime: [{ required: true, message: '请选择修复时间', trigger: 'blur' }],
    replenishResult: [{ required: true, message: '请填写修复结果', trigger: 'blur' }]
});

const planform = ref();
const editplanform = ref();
const humidityform = ref();
const humidityeditform = ref();
const { proxy } = getCurrentInstance();
const menuList = ref([]);
const humidityList = ref([]);
const checkitemList = ref([]);
const bottomList = ref([]);
const bottomList2 = ref([]);
const numberStatus = ref([]);
const editbottomList = ref([]);
// 点击树结构的id
const clickEvenId = ref([]);
const addRecordForm = ref();
const detailRecordForm = ref();
const editRecordForm = ref();
const titleList = ref([]);
const dataList = ref([]);
const planForm = reactive({});
const recordForm = reactive({});
const editrecordForm = reactive({});
const recorddatailForm = reactive({});
const humidityForm = reactive({});
const edithumidityForm = reactive({});
const editplanForm = ref({});
const plandetailForm = ref({});
const humiditydetailForm = ref({});
const orgId = ref('');
const planId = ref('');
const recoedItem = ref('');
const editId = ref('');
const editrecordId = ref('');
const recordList = ref([]);
const activeName = ref('1');
const editParams = ref([]);
const orguserList = ref([]);
const dialogFormVisible = ref(false);
const editdialogFormVisible = ref(false);
const detaildialogFormVisible3 = ref(false);
const editdialogFormVisible3 = ref(false);
const humidityDetailVisible = ref(false);
const edithumidityVisible = ref(false);
const dialogFormVisible2 = ref(false);
const dialogFormVisible3 = ref(false);
const planDetailVisible = ref(false);
const loading = ref(false);
const recordTable = ref(null);
const recordId = ref('');
// 盘点计划分页参数
const planTotal = ref(0);
const planQueryParams = ref({
    current: 1,
    size: 10
});

// 盘点记录分页参数
const recordTotal = ref(0);
const recordQueryParams = ref({
    current: 1,
    size: 10
});

// 修复记录分页参数
const repairTotal = ref(0);
const repairQueryParams = ref({
    current: 1,
    size: 10
});

const handleOutsideClick = (scope, key, e) => {
    // 检查点击的元素是否在下拉框或输入框内
    if (e.target.id === 'div' || e.target.id === 'input' || e.target.closest('.el-select') || e.target.closest('.el-select-dropdown')) {
        return;
    }
    scope.row[key] = false;
};
const filterArr = (option, key, value) => {
    if (!option?.length || !key || !value) return;

    // 确保value是数组格式
    let valueArray = Array.isArray(value) ? value : typeof value === 'string' ? value.split(',') : [];

    if (!valueArray.length) return;

    const res = option.filter((v) => valueArray.includes(v[key]) === true);
    const NameArr = [];
    res?.forEach((item) => {
        NameArr.push(item?.name);
    });
    return NameArr?.toString();
};

// 日期变化事件处理函数
const onPlanStartTimeChange = () => {
    // 当开始时间变化时，重新验证结束时间
    try {
        if (planForm.checkPlanEndTime && planform.value && typeof planform.value.validateField === 'function') {
            planform.value.validateField('checkPlanEndTime').catch(() => {
                // 忽略验证错误，只是为了触发验证
            });
        }
    } catch (error) {
        console.warn('验证字段时出错:', error);
    }
};

const onEditPlanStartTimeChange = () => {
    // 当编辑表单开始时间变化时，重新验证结束时间
    try {
        if (editplanForm.value && editplanForm.value.checkPlanEndTime && editplanform.value && typeof editplanform.value.validateField === 'function') {
            editplanform.value.validateField('checkPlanEndTime').catch(() => {
                // 忽略验证错误，只是为了触发验证
            });
        }
    } catch (error) {
        console.warn('验证字段时出错:', error);
    }
};

const onRecordStartTimeChange = () => {
    // 当盘点记录开始时间变化时，重新验证结束时间
    try {
        if (recordForm.checkHistoryEndTime && addRecordForm.value && typeof addRecordForm.value.validateField === 'function') {
            addRecordForm.value.validateField('checkHistoryEndTime').catch(() => {
                // 忽略验证错误，只是为了触发验证
            });
        }
    } catch (error) {
        console.warn('验证字段时出错:', error);
    }
};

const onEditRecordStartTimeChange = () => {
    // 当编辑盘点记录开始时间变化时，重新验证结束时间
    try {
        if (editrecordForm.checkHistoryEndTime && editRecordForm.value && typeof editRecordForm.value.validateField === 'function') {
            editRecordForm.value.validateField('checkHistoryEndTime').catch(() => {
                // 忽略验证错误，只是为了触发验证
            });
        }
    } catch (error) {
        console.warn('验证字段时出错:', error);
    }
};

//盘点计划增加按钮
const personelAdd = (formEl) => {
    dialogFormVisible.value = true;
    planForm.checkPlanStartTime = moment(new Date()).format('YYYY-MM-DD');
    planForm.checkPlanEndTime = moment(new Date()).format('YYYY-MM-DD');
    // 安全地重置表单字段
    if (formEl && typeof formEl.resetFields === 'function') {
        formEl.resetFields();
    }
};
//盘点记录增加按钮
const humidityAdd = (formEl) => {
    dialogFormVisible2.value = true;
    recordForm.checkHistoryStartTime = moment(new Date()).format('YYYY-MM-DD');
    recordForm.checkHistoryEndTime = moment(new Date()).format('YYYY-MM-DD');
    // 清空盘点负责人字段
    recordForm.checkPlanPrincipal = '';
    recordForm.checkPlanPrincipalName = '';
    recordForm.checkPlanTitle = '';
    titleList.value = [];
    dataList.value.forEach((i) => {
        // console.log(i);
        titleList.value.push({
            id: i.id,
            title: i.checkPlanTitle
        });
    });
    // 安全地重置表单字段
    if (formEl && typeof formEl.resetFields === 'function') {
        formEl.resetFields();
    }
    bottomList.value = [];
};
//修复记录增加按钮
const repairAdd = (formEl) => {
    dialogFormVisible3.value = true;
    humidityForm.replenishTime = moment(new Date()).format('YYYY-MM-DD');
    humidityForm.infoName = '';
    // 安全地重置表单字段
    if (formEl && typeof formEl.resetFields === 'function') {
        formEl.resetFields();
    }
    // addForm2.executeTime = moment(new Date()).format('HH:mm:ss')
};
//侧边栏树结构
const treeList = () => {
    storeroomData.treeData({ orgId: getOrganizationInfo()?.id }).then((res) => {
        if (res.code === 200) {
            menuList.value = res.data;
        }
    });
};
//盘点计划列表
const getList = () => {
    loading.value = true;
    fileInventory.list(planQueryParams.value).then((res) => {
        if (res.code === 200) {
            dataList.value = res.data.records;
            res?.data?.records?.forEach((v) => {
                let arr2 = [];
                let arr = v.checkPlanItem.split(',');
                checkitemList.value.forEach((i) => {
                    arr.forEach((item) => {
                        if (item == i.value) {
                            arr2.push(i.name);
                        }
                    });
                });
                v.checkPlanItem = arr2.toString();
            });
            res?.data?.records?.forEach((v) => {
                let arr3 = [];
                let arr4 = v.checkPlanPersonIds.split(',');
                let orguser = JSON.parse(localStorage.getItem('orguserList'));
                orguser.forEach((i) => {
                    arr4.forEach((item) => {
                        if (item == i.id) {
                            arr3.push(i.name);
                        }
                    });
                });
                v.checkPlanPersonIds = arr3.toString();
            });
            // dataList.value 已在上面设置，无需重复设置
            planTotal.value = res.data.total;
            loading.value = false;
        }
    });
};
//查看盘点计划详情
const planDetail = (row) => {
    planDetailVisible.value = true;
    fileInventory.detail({ id: row.id }).then((res) => {
        if (res.code === 200) {
            plandetailForm.value = res.data;
            let arr2 = [];
            let arr = res.data.checkPlanItem.split(',');
            checkitemList.value.forEach((i) => {
                arr.forEach((item) => {
                    if (item == i.value) {
                        arr2.push(i.name);
                    }
                });
            });
            plandetailForm.value.checkPlanItem = arr2.toString();

            let arr3 = [];
            let arr4 = res.data.checkPlanPersonIds.split(',');
            let orguser = JSON.parse(localStorage.getItem('orguserList'));
            orguser.forEach((i) => {
                arr4.forEach((item) => {
                    if (item == i.id) {
                        arr3.push(i.name);
                    }
                });
            });
            plandetailForm.value.checkPlanPersonIds = arr3.toString();
        }
    });
};
//盘点计划编辑按钮
const handleEdit = (row) => {
    planId.value = row.id;
    editdialogFormVisible.value = true;
    fileInventory.detail({ id: row.id }).then((res) => {
        if (res.code === 200) {
            editplanForm.value = res.data;
            editplanForm.value.checkPlanPersonIds = res.data.checkPlanPersonIds.split(',');
            editplanForm.value.checkPlanItem = res.data.checkPlanItem.split(',');
        }
    });
};
//盘点计划编辑请求
const editPlan = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var params = {
                id: planId.value,
                checkPlanTitle: editplanForm.value.checkPlanTitle,
                checkPlanStartTime: editplanForm.value.checkPlanStartTime,
                checkPlanEndTime: editplanForm.value.checkPlanEndTime,
                checkPlanPrincipal: { id: editplanForm.value.checkPlanPrincipal.id },
                checkPlanPersonIds: editplanForm.value.checkPlanPersonIds.toString(),
                checkPlanItem: editplanForm.value.checkPlanItem.toString(),
                remark: editplanForm.value.remark
            };
            fileInventory.save(params).then((res) => {
                if (res.code === 200) {
                    ElMessage({
                        message: '修改成功',
                        type: 'success'
                    });
                    editdialogFormVisible.value = false;
                    getList();
                }
            });
        }
    });
};
//保存盘点计划
const addPlan = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var params = {
                ...planForm
            };
            if (params.checkPlanPrincipal) {
                params.checkPlanPrincipal = {
                    id: params.checkPlanPrincipal
                };
            }
            if (params.checkPlanPersonIds) {
                params.checkPlanPersonIds = params.checkPlanPersonIds.toString();
            }
            if (params.checkPlanItem) {
                params.checkPlanItem = params.checkPlanItem.toString();
            }
            fileInventory.save(params).then((res) => {
                if (res.code === 200) {
                    ElMessage({
                        message: '保存成功',
                        type: 'success'
                    });
                    dialogFormVisible.value = false;
                    getList();
                }
            });
        }
    });
};

// 删除盘点计划
function planDelete(row) {
    proxy
        .$confirm('是否确认删除此条盘点计划?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            fileInventory.delete({ ids: row.id }).then((res) => {
                if (res.code === 200) {
                    getList();
                    proxy.msgSuccess('删除成功');
                }
            });
        })
        .catch(() => {});
}

//获取机构成员
const orgList = () => {
    let org = JSON.parse(localStorage.getItem('Organization'));
    org.content.forEach((v) => {
        orgId.value = v.id;
    });
    fileInventory.orgList({ 'sysOrg.id': orgId.value }).then((res) => {
        if (res.code === 200) {
            orguserList.value = res.data.records;
            localStorage.setItem('orguserList', JSON.stringify(orguserList.value));
        }
    });
};
//盘点记录列表
const getList2 = () => {
    loading.value = true;
    fileInventory.recordList(recordQueryParams.value).then((res) => {
        if (res.code === 200) {
            recordList.value = res.data.records;
            recordList.value.forEach((item) => {
                fileInventory.recordDetail({ 'checkHistoryInfo.id': item.id }).then((res) => {
                    if (res.code === 200) {
                        res.data.records.forEach((v) => {
                            let arr3 = [];
                            let arr4 = v.planDetailPerson.id.split(',');
                            orguserList.value.forEach((i) => {
                                arr4.forEach((item) => {
                                    if (item == i.id) {
                                        arr3.push(i.name);
                                    }
                                });
                                v.checkPlanPersonIds = arr3.toString();
                                let arr = v.planDetailItem;
                                checkitemList.value.forEach((i) => {
                                    if (i.value == arr) {
                                        v.planDetailItem = i.name;
                                    }
                                });
                            });
                            item.list = res.data.records;
                        });
                    }
                });
            });
            recordTotal.value = res.data.total;
            loading.value = false;
        }
    });
};
//盘点记录标题更改
const titleChacnge = (v) => {
    fileInventory.detail({ id: v }).then((res) => {
        if (res.code === 200) {
            recoedItem.value = res.data.checkPlanItem.split(',');
            // 设置盘点负责人ID，确保下拉框能正确显示对应的name
            // 如果orguserList还没有加载，先从localStorage获取
            if (orguserList.value.length === 0) {
                const orguser = JSON.parse(localStorage.getItem('orguserList') || '[]');
                if (orguser.length > 0) {
                    orguserList.value = orguser;
                }
            }

            // 设置盘点负责人ID和名称
            recordForm.checkPlanPrincipal = res.data.checkPlanPrincipal.id;
            recordForm.checkPlanPrincipalName = res.data.checkPlanPrincipal.name;
            editrecordForm.checkPlanPrincipal = res.data.checkPlanPrincipal.id;
            editrecordForm.checkPlanPrincipalName = res.data.checkPlanPrincipal.name;

            let arr = res.data.checkPlanItem.split(',');
            bottomList.value = [];
            editbottomList.value = [];
            arr.forEach((i) => {
                bottomList.value.push({
                    checkPlanItem: i,
                    zhixing: [], // 初始化为空数组
                    isShow: false,
                    isShow2: false
                });
                editbottomList.value.push({
                    planDetailItem: i,
                    checkPlanPersonIds: [], // 初始化为空数组
                    isShow: false,
                    isShow2: false
                });
            });
        }
    });
};
//盘点记录新增
const recordAdd = async (formEl) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            // 验证表格数据
            if (bottomList.value.length === 0) {
                proxy.msgError('请先选择盘点计划标题以生成盘点项');
                return;
            }

            let validationErrors = [];
            let hasEmptyExecutor = false;
            let hasEmptyResult = false;

            bottomList.value.forEach((item, index) => {
                if (!item.zhixing || (Array.isArray(item.zhixing) && item.zhixing.length === 0)) {
                    validationErrors.push(`第${index + 1}行：请选择执行人`);
                    hasEmptyExecutor = true;
                }
                if (!item.checkPlanTitle || item.checkPlanTitle.trim() === '') {
                    validationErrors.push(`第${index + 1}行：请输入盘点结果`);
                    hasEmptyResult = true;
                }
            });

            // 如果有验证错误，显示提示并返回
            if (validationErrors.length > 0) {
                let errorMsg = '请完善以下信息：\n' + validationErrors.join('\n');
                if (hasEmptyExecutor && hasEmptyResult) {
                    errorMsg += '\n\n提示：点击表格中的"请选择执行人"和"请输入盘点结果"可以进行编辑';
                } else if (hasEmptyExecutor) {
                    errorMsg += '\n\n提示：点击表格中的"请选择执行人"可以进行编辑';
                } else if (hasEmptyResult) {
                    errorMsg += '\n\n提示：点击表格中的"请输入盘点结果"可以进行编辑';
                }
                proxy.msgError(errorMsg);
                return;
            }

            let params = {
                ...recordForm
            };
            if (params.checkPlanTitle) {
                params.checkPlanInfo = {
                    id: params.checkPlanTitle
                };
                delete params.checkPlanTitle;
                delete params.checkPlanPrincipal;
            }
            try {
                await fileInventory.recordSave(params).then((res) => {
                    if (res.code === 200) {
                        recordId.value = res.data.id;
                    } else {
                        proxy.msgError(res.msg || '保存盘点记录失败');
                        return;
                    }
                });

                let params2 = [];
                bottomList.value.forEach((i) => {
                    params2.push({
                        checkHistoryInfo: { id: recordId.value },
                        planDetailItem: i.checkPlanItem,
                        planDetailPerson: { id: i.zhixing.toString() },
                        planDetailResult: i.checkPlanTitle
                    });
                });
                await fileInventory.recordTableSave(params2).then((res) => {
                    if (res.code === 200) {
                        proxy.msgSuccess('保存成功');
                        dialogFormVisible2.value = false;
                        getList2();
                    } else {
                        proxy.msgError(res.msg || '保存盘点详情失败');
                    }
                });
            } catch (error) {
                console.error('保存盘点记录时发生错误:', error);
                proxy.msgError('保存失败，请稍后重试');
            }
        }
    });
};
// 查看盘点记录
const humidityDetail = (row) => {
    humidityDetailVisible.value = true;
    recorddatailForm.checkPlanTitle = row.checkPlanInfo.checkPlanTitle;
    recorddatailForm.checkHistoryStartTime = row.checkHistoryStartTime;
    recorddatailForm.checkHistoryEndTime = row.checkHistoryEndTime;
    recorddatailForm.checkPlanPrincipalName = row.checkPlanInfo.checkPlanPrincipal.name;
    fileInventory.recordDetail({ 'checkHistoryInfo.id': row.id }).then((res) => {
        if (res.code === 200) {
            res.data.records.forEach((v) => {
                let arr3 = [];
                let arr4 = v.planDetailPerson.id.split(',');
                orguserList.value.forEach((i) => {
                    arr4.forEach((item) => {
                        if (item == i.id) {
                            arr3.push(i.name);
                        }
                    });
                    v.checkPlanPersonIds = arr3.toString();
                    let arr = v.planDetailItem;
                    checkitemList.value.forEach((i) => {
                        if (i.value == arr) {
                            v.planDetailItem = i.name;
                        }
                    });
                });
            });
            bottomList2.value = res.data.records;
        }
    });
};
//盘点记录编辑按钮
const humidityEdit = (row) => {
    edithumidityVisible.value = true;
    editId.value = row.id;
    titleList.value = [];
    dataList.value.forEach((i) => {
        titleList.value.push({
            id: i.id,
            title: i.checkPlanTitle
        });
    });
    editrecordForm.checkPlanTitle = row.checkPlanInfo.id;
    editrecordForm.checkHistoryStartTime = row.checkHistoryStartTime;
    editrecordForm.checkHistoryEndTime = row.checkHistoryEndTime;
    editrecordForm.checkPlanPrincipal = row.checkPlanInfo.checkPlanPrincipal.id;
    editrecordForm.checkPlanPrincipalName = row.checkPlanInfo.checkPlanPrincipal.name;
    fileInventory.recordDetail({ 'checkHistoryInfo.id': row.id }).then((res) => {
        if (res.code === 200) {
            res.data.records.forEach((v) => {
                // 确保checkPlanPersonIds是数组格式，并且包含有效的ID
                const personIds = v.planDetailPerson.id ? v.planDetailPerson.id.split(',').filter((id) => id.trim()) : [];
                v.checkPlanPersonIds = personIds;

                // 初始化显示状态
                v.isShow = false;
                v.isShow2 = false;
            });
            editbottomList.value = res.data.records;
        }
    });
};
//盘点记录编辑请求
const recordEdit = async (formEl) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            // 验证表格数据
            if (editbottomList.value.length === 0) {
                proxy.msgError('请先选择盘点计划标题以生成盘点项');
                return;
            }

            let validationErrors = [];
            let hasEmptyExecutor = false;
            let hasEmptyResult = false;

            editbottomList.value.forEach((item, index) => {
                if (!item.checkPlanPersonIds || (Array.isArray(item.checkPlanPersonIds) && item.checkPlanPersonIds.length === 0)) {
                    validationErrors.push(`第${index + 1}行：请选择执行人`);
                    hasEmptyExecutor = true;
                }
                if (!item.planDetailResult || item.planDetailResult.trim() === '') {
                    validationErrors.push(`第${index + 1}行：请输入盘点结果`);
                    hasEmptyResult = true;
                }
            });

            // 如果有验证错误，显示提示并返回
            if (validationErrors.length > 0) {
                let errorMsg = '请完善以下信息：\n' + validationErrors.join('\n');
                if (hasEmptyExecutor && hasEmptyResult) {
                    errorMsg += '\n\n提示：点击表格中的"请选择执行人"和"请输入盘点结果"可以进行编辑';
                } else if (hasEmptyExecutor) {
                    errorMsg += '\n\n提示：点击表格中的"请选择执行人"可以进行编辑';
                } else if (hasEmptyResult) {
                    errorMsg += '\n\n提示：点击表格中的"请输入盘点结果"可以进行编辑';
                }
                proxy.msgError(errorMsg);
                return;
            }

            let params = {
                ...editrecordForm
            };
            if (isNaN(params.checkPlanTitle) == true) {
                titleList.value.forEach((v) => {
                    if (v.title == params.checkPlanTitle) {
                        params.checkPlanTitle = v.id;
                    }
                });
            }
            if (params.checkPlanTitle) {
                params.checkPlanInfo = {
                    id: params.checkPlanTitle
                };
                params.checkPlanPrincipal = {
                    id: params.checkPlanPrincipal
                };
                params.id = editId.value;
                delete params.checkPlanTitle;
            }
            try {
                await fileInventory.recordSave(params).then((res) => {
                    if (res.code === 200) {
                        editrecordId.value = res.data.id;
                    } else {
                        proxy.msgError(res.msg || '修改盘点记录失败');
                        return;
                    }
                });
                editParams.value = [];
                console.log('111', editbottomList.value);
                editbottomList.value.forEach((item) => {
                    editParams.value.push({
                        id: item.id,
                        checkHistoryInfo: {
                            id: editId.value
                        },
                        planDetailItem: item.planDetailItem,
                        planDetailPerson: {
                            id: item.checkPlanPersonIds.toString()
                        },
                        planDetailResult: item.planDetailResult
                    });
                });
                console.log(editParams.value);
                let params2 = [...editParams.value];
                await fileInventory.recordTableSave(params2).then((res) => {
                    if (res.code === 200) {
                        proxy.msgSuccess('修改成功');
                        edithumidityVisible.value = false;
                        getList2();
                    } else {
                        proxy.msgError(res.msg || '修改盘点详情失败');
                    }
                });
            } catch (error) {
                console.error('修改盘点记录时发生错误:', error);
                proxy.msgError('修改失败，请稍后重试');
            }
        }
    });
};

// 删除盘点记录
function humidityDelete(row) {
    proxy
        .$confirm('是否确认删除此条盘点记录?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            fileInventory.recordDelete({ ids: row.id }).then((res) => {
                if (res.code === 200) {
                    getList2();
                    proxy.msgSuccess('删除成功');
                }
            });
        })
        .catch(() => {});
}

//档号
const numberList = () => {
    fileInventory.numberList({ size: -1, current: 1 }).then((res) => {
        if (res.code === 200) {
            numberStatus.value = res.data.records;
        }
    });
};
// 档号变动
const numberChange = (v) => {
    numberStatus.value.forEach((i) => {
        if (i.id == v) {
            humidityForm.infoName = i.name;
            humiditydetailForm.value.info.name = i.name;
            console.log(humiditydetailForm.value.info.name);
        }
    });
};
// 新增修复记录
const creatRepair = async (formEl) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            let params = {
                ...humidityForm
            };
            if (params.replenishPerson) {
                params.replenishPerson = {
                    id: params.replenishPerson
                };
                params.info = {
                    id: params.num
                };
                delete params.num;
                delete params.infoName;
            }
            fileInventory.repairSave(params).then((res) => {
                if (res.code === 200) {
                    ElMessage({
                        message: '保存成功',
                        type: 'success'
                    });
                    dialogFormVisible3.value = false;
                    getList3();
                }
            });
        }
    });
};
//修复记录列表
const getList3 = () => {
    loading.value = true;
    fileInventory.repairList(repairQueryParams.value).then((res) => {
        if (res.code === 200) {
            humidityList.value = res.data.records;
            repairTotal.value = res.data.total;
            loading.value = false;
        }
    });
};
//修复记录详情
const repairDetail = (row) => {
    editdialogFormVisible3.value = true;
    edithumidityForm.num = row.info.num;
    edithumidityForm.infoName = row.info.name;
    edithumidityForm.replenishPerson = row.replenishPerson.name;
    edithumidityForm.replenishTime = row.replenishTime;
    edithumidityForm.replenishResult = row.replenishResult;
};
//修复记录编辑按钮
const repairEdit = (row) => {
    detaildialogFormVisible3.value = true;
    fileInventory.repairList({ id: row.id }).then((res) => {
        if (res.code === 200) {
            humiditydetailForm.value = res.data.records[0];
        }
    });
};
//修复记录编辑请求
const editRepair = async (formEl) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            let params = {
                id: humiditydetailForm.value.id,
                info: {
                    id: humiditydetailForm.value.info.id
                },
                replenishPerson: {
                    id: humiditydetailForm.value.replenishPerson.id
                },
                replenishTime: humiditydetailForm.value.replenishTime,
                replenishResult: humiditydetailForm.value.replenishResult
            };
            console.log(params);
            fileInventory.repairSave(params).then((res) => {
                if (res.code === 200) {
                    ElMessage({
                        message: '修改成功',
                        type: 'success'
                    });
                    detaildialogFormVisible3.value = false;
                    getList3();
                }
            });
        }
    });
};
//删除修复记录
const repairDelete = (row) => {
    proxy
        .$confirm('是否确认删除此条修复记录?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            fileInventory.repairDelete({ ids: row.id }).then((res) => {
                if (res.code === 200) {
                    getList3();
                    proxy.msgSuccess('删除成功');
                }
            });
        })
        .catch(() => {});
};
//显示执行人
const handleInputEdit = (scope) => {
    scope.row.isShow = true;
};
const handleInputEdit2 = (scope) => {
    scope.row.isShow2 = true;
};

// 处理执行人选择变化
const handleExecutorChange = (scope) => {
    // 确保选择的值是数组格式
    if (!Array.isArray(scope.row.checkPlanPersonIds)) {
        scope.row.checkPlanPersonIds = [];
    }
    // 可以在这里添加其他逻辑，比如验证等
};

// 处理执行人选择失焦
const handleExecutorBlur = (scope) => {
    // 延迟关闭下拉框，确保选择操作完成
    setTimeout(() => {
        scope.row.isShow = false;
    }, 200);
};

// 处理新增对话框中的执行人选择变化
const handleExecutorChangeAdd = (scope) => {
    // 确保选择的值是数组格式
    if (!Array.isArray(scope.row.zhixing)) {
        scope.row.zhixing = [];
    }
};

// 处理新增对话框中的执行人选择失焦
const handleExecutorBlurAdd = (scope) => {
    // 延迟关闭下拉框，确保选择操作完成
    setTimeout(() => {
        scope.row.isShow = false;
    }, 200);
};
//点击树形组件事件
const clickEven = (val) => {
    console.log('树形组件点击事件触发:', val);
    clickEvenId.value = val;
    clickEvenList(val);
};

// 点击树结构查询表格
const clickEvenList = (val) => {
    console.log('开始处理树形选择:', val);
    if (val && val.id) {
        // 根据选择的树节点更新查询参数
        // 根据树节点的层级和类型使用不同的参数名
        if (val.recordGroupName) {
            // 全宗级别
            console.log('设置全宗级别筛选参数:', val.id);
            planQueryParams.value['controlGroup.id'] = val.id;
            recordQueryParams.value['controlGroup.id'] = val.id;
            repairQueryParams.value['controlGroup.id'] = val.id;
        } else {
            // 门类级别或其他
            console.log('设置门类级别筛选参数:', val.id);
            planQueryParams.value['controlCategory.id'] = val.id;
            recordQueryParams.value['controlCategory.id'] = val.id;
            repairQueryParams.value['controlCategory.id'] = val.id;
        }

        console.log('当前激活标签页:', activeName.value);
        // 重新查询当前激活的标签页数据
        if (activeName.value === '1') {
            console.log('重新查询盘点计划');
            getList(); // 盘点计划
        } else if (activeName.value === '2') {
            console.log('重新查询盘点记录');
            getList2(); // 盘点记录
        } else if (activeName.value === '3') {
            console.log('重新查询修复记录');
            getList3(); // 修复记录
        }
    }
};

//字典回显
const formDict = (data, val) => {
    return data && val ? proxy.selectDictLabel(data, val) : '--';
};
// 字典
const getDict = async () => {
    checkitemList.value = await proxy.getDictList('check_item');
};
getDict();
treeList();
getList();
orgList();
getList2();
numberList();
getList3();
</script>

<style lang="scss" scoped>
.dialog-footer {
    display: flex;
    justify-content: end;
    margin: 20px 10px 10px 0px;
}

/* 主容器布局 */
.main-container {
    overflow: hidden;
}

/* 左侧树形结构卡片 */
.tree-card {
    height: calc(100vh - 95px);
    overflow: hidden;

    :deep(.el-card__body) {
        height: 100%;
        overflow: auto;
        padding: 8px 0 0 0;
    }
}

/* 右侧内容容器 */
.content-container {
    overflow: hidden;
}

/* 主内容区域 */

/* 内容卡片 */
.content-card {
    height: calc(100vh - 95px);
    overflow: hidden;

    :deep(.el-card__body) {
        height: 100%;
        overflow: hidden;
        padding-bottom: 0;
    }
}

/* 标签页容器 */
.content-tabs {
    height: 100%;
    display: flex;
    flex-direction: column-reverse;

    :deep(.el-tabs__header) {
        flex-shrink: 0;
        margin-bottom: 0;
    }

    :deep(.el-tabs__content) {
        flex: 1;
        overflow: hidden;
        padding: 0;
        height: 0; /* 重要：强制flex子元素计算高度 */
    }

    :deep(.el-tab-pane) {
        height: 100%;
        overflow: hidden;
        padding: 10px 0 0 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
}

/* Tab面板内容布局 */
:deep(.el-tab-pane) {
    /* 新增按钮区域 - 靠左显示，固定高度 */
    > .el-button {
        flex-shrink: 0;
        display: inline-block;
        margin-bottom: 10px;
        margin-right: 10px;
    }
}
</style>
<script setup lang="ts"></script>
